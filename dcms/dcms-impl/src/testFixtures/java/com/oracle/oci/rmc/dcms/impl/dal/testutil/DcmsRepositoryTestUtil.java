package com.oracle.oci.rmc.dcms.impl.dal.testutil;

import static com.oracle.oci.rmc.dcms.api.model.entity.details.DeviceView.FRONT;

import com.google.common.base.Preconditions;
import com.oracle.oci.rmc.auditevent.dal.AuditEventRepository;
import com.oracle.oci.rmc.catalog.api.model.entity.Cable;
import com.oracle.oci.rmc.catalog.api.model.entity.Part;
import com.oracle.oci.rmc.catalog.api.model.entity.PartCategory;
import com.oracle.oci.rmc.catalog.api.model.entity.PartsCompatibility;
import com.oracle.oci.rmc.catalog.api.model.entity.RackPlatform;
import com.oracle.oci.rmc.catalog.api.model.object.part.PartDetailsSource;
import com.oracle.oci.rmc.catalog.impl.dal.CableRepository;
import com.oracle.oci.rmc.catalog.impl.dal.PartCategoryRepository;
import com.oracle.oci.rmc.catalog.impl.dal.PartRepository;
import com.oracle.oci.rmc.catalog.impl.dal.PartsCompatibilityRepository;
import com.oracle.oci.rmc.catalog.impl.dal.RackPlatformRepository;
import com.oracle.oci.rmc.changemanagement.api.model.common.ChangeOrderSourceType;
import com.oracle.oci.rmc.changemanagement.api.model.common.ChangeOrderState;
import com.oracle.oci.rmc.changemanagement.api.model.common.ChangeOrderType;
import com.oracle.oci.rmc.changemanagement.api.model.common.ChangeTaskState;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeOrder;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeOrderDetails;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeTask;
import com.oracle.oci.rmc.changemanagement.impl.dal.ChangeOrderRepository;
import com.oracle.oci.rmc.changemanagement.impl.dal.ChangeTaskRepository;
import com.oracle.oci.rmc.dcms.api.model.common.Addresses;
import com.oracle.oci.rmc.dcms.api.model.common.ConnectionSource;
import com.oracle.oci.rmc.dcms.api.model.common.Coordinates;
import com.oracle.oci.rmc.dcms.api.model.common.DeviceInstanceLifecycleState;
import com.oracle.oci.rmc.dcms.api.model.common.DevicePortType;
import com.oracle.oci.rmc.dcms.api.model.common.DeviceType;
import com.oracle.oci.rmc.dcms.api.model.common.ExternalPeerType;
import com.oracle.oci.rmc.dcms.api.model.common.NetworkCableType;
import com.oracle.oci.rmc.dcms.api.model.common.PhysicalEntityLifecycleState;
import com.oracle.oci.rmc.dcms.api.model.common.PortLifecycleState;
import com.oracle.oci.rmc.dcms.api.model.common.RackInstanceAccessLevel;
import com.oracle.oci.rmc.dcms.api.model.common.RackInstanceLifecycleState;
import com.oracle.oci.rmc.dcms.api.model.common.RackInstanceLifecycleSubState;
import com.oracle.oci.rmc.dcms.api.model.common.RackModelLifecycleState;
import com.oracle.oci.rmc.dcms.api.model.common.RackPositionLifecycleState;
import com.oracle.oci.rmc.dcms.api.model.common.RackPositionType;
import com.oracle.oci.rmc.dcms.api.model.common.RackType;
import com.oracle.oci.rmc.dcms.api.model.common.ServiceType;
import com.oracle.oci.rmc.dcms.api.model.common.TrunkLifecycleState;
import com.oracle.oci.rmc.dcms.api.model.common.TrunkPath;
import com.oracle.oci.rmc.dcms.api.model.common.TrunkType;
import com.oracle.oci.rmc.dcms.api.model.entity.AssetLocation;
import com.oracle.oci.rmc.dcms.api.model.entity.AssetLocationType;
import com.oracle.oci.rmc.dcms.api.model.entity.Building;
import com.oracle.oci.rmc.dcms.api.model.entity.ConnectionEntity;
import com.oracle.oci.rmc.dcms.api.model.entity.DataHall;
import com.oracle.oci.rmc.dcms.api.model.entity.DeviceInstance;
import com.oracle.oci.rmc.dcms.api.model.entity.DeviceInstanceBuilder;
import com.oracle.oci.rmc.dcms.api.model.entity.DeviceInstanceDetails;
import com.oracle.oci.rmc.dcms.api.model.entity.DeviceModel;
import com.oracle.oci.rmc.dcms.api.model.entity.ExternalPeerEntity;
import com.oracle.oci.rmc.dcms.api.model.entity.ExternalPeerEntityBuilder;
import com.oracle.oci.rmc.dcms.api.model.entity.FabricEntity;
import com.oracle.oci.rmc.dcms.api.model.entity.MarketEntity;
import com.oracle.oci.rmc.dcms.api.model.entity.ModuleSelector;
import com.oracle.oci.rmc.dcms.api.model.entity.NetworkCable;
import com.oracle.oci.rmc.dcms.api.model.entity.NetworkCableLeg;
import com.oracle.oci.rmc.dcms.api.model.entity.PatchPanelSelector;
import com.oracle.oci.rmc.dcms.api.model.entity.PluggablePort;
import com.oracle.oci.rmc.dcms.api.model.entity.PortEntity;
import com.oracle.oci.rmc.dcms.api.model.entity.PurchaseOrder;
import com.oracle.oci.rmc.dcms.api.model.entity.PurchaseOrderCreateDetails;
import com.oracle.oci.rmc.dcms.api.model.entity.PurchaseOrderItem;
import com.oracle.oci.rmc.dcms.api.model.entity.PurchaseOrderItemCreateDetails;
import com.oracle.oci.rmc.dcms.api.model.entity.PurchaseOrderItemNonConformanceReasonType;
import com.oracle.oci.rmc.dcms.api.model.entity.PurchaseOrderItemStatusType;
import com.oracle.oci.rmc.dcms.api.model.entity.PurchaseOrderItemSubInventoryType;
import com.oracle.oci.rmc.dcms.api.model.entity.PurchaseOrderLine;
import com.oracle.oci.rmc.dcms.api.model.entity.PurchaseOrderLineCreateDetails;
import com.oracle.oci.rmc.dcms.api.model.entity.PurchaseOrderStatusType;
import com.oracle.oci.rmc.dcms.api.model.entity.PurchaseOrderType;
import com.oracle.oci.rmc.dcms.api.model.entity.RackInstance;
import com.oracle.oci.rmc.dcms.api.model.entity.RackInstanceBuilder;
import com.oracle.oci.rmc.dcms.api.model.entity.RackInstanceDetails;
import com.oracle.oci.rmc.dcms.api.model.entity.RackModel;
import com.oracle.oci.rmc.dcms.api.model.entity.RackPosition;
import com.oracle.oci.rmc.dcms.api.model.entity.RealmEntity;
import com.oracle.oci.rmc.dcms.api.model.entity.RegionEntity;
import com.oracle.oci.rmc.dcms.api.model.entity.RoomEntity;
import com.oracle.oci.rmc.dcms.api.model.entity.ShelfSelector;
import com.oracle.oci.rmc.dcms.api.model.entity.ShortRackIdCounter;
import com.oracle.oci.rmc.dcms.api.model.entity.SiteEntity;
import com.oracle.oci.rmc.dcms.api.model.entity.StorageRoom;
import com.oracle.oci.rmc.dcms.api.model.entity.Transceiver;
import com.oracle.oci.rmc.dcms.api.model.entity.TrunkAdditionalMetadata;
import com.oracle.oci.rmc.dcms.api.model.entity.TrunkDefinition;
import com.oracle.oci.rmc.dcms.api.model.entity.TrunkDefinitionEndpoint;
import com.oracle.oci.rmc.dcms.api.model.entity.details.ActiveDeviceAttributes;
import com.oracle.oci.rmc.dcms.api.model.entity.details.ActivePortAttributes;
import com.oracle.oci.rmc.dcms.api.model.entity.details.DeviceAttributes;
import com.oracle.oci.rmc.dcms.api.model.entity.details.DeviceView;
import com.oracle.oci.rmc.dcms.api.model.entity.details.PassiveDeviceAttributes;
import com.oracle.oci.rmc.dcms.api.model.entity.details.PortAttributes;
import com.oracle.oci.rmc.dcms.api.model.entity.internal.RegionalId;
import com.oracle.oci.rmc.dcms.api.model.entity.internal.TrunkDefinitionEndpointId;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.Asset;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetBundle;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetCreationDetails;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetCreationDetailsBuilder;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetLifecycleState;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetLocationCreationDetailsBuilder;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetReservation;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetReservationLifecycleState;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetSubInventory;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetTransfer;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetUpdateDetailsBuilder;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.DeviceLocation;
import com.oracle.oci.rmc.dcms.api.service.inventory.AssetService;
import com.oracle.oci.rmc.dcms.impl.dal.BuildingRepository;
import com.oracle.oci.rmc.dcms.impl.dal.ConnectionRepository;
import com.oracle.oci.rmc.dcms.impl.dal.DataHallRepository;
import com.oracle.oci.rmc.dcms.impl.dal.DeviceInstanceRepository;
import com.oracle.oci.rmc.dcms.impl.dal.DeviceModelRepository;
import com.oracle.oci.rmc.dcms.impl.dal.ExternalPeerRepository;
import com.oracle.oci.rmc.dcms.impl.dal.FabricRepository;
import com.oracle.oci.rmc.dcms.impl.dal.MarketRepository;
import com.oracle.oci.rmc.dcms.impl.dal.NetworkCableLegRepository;
import com.oracle.oci.rmc.dcms.impl.dal.NetworkCableRepository;
import com.oracle.oci.rmc.dcms.impl.dal.NodeGroupRepository;
import com.oracle.oci.rmc.dcms.impl.dal.NodeGroupToDeviceModelRepository;
import com.oracle.oci.rmc.dcms.impl.dal.NodeGroupToFabricRepository;
import com.oracle.oci.rmc.dcms.impl.dal.PatchPanelPortAttributesRepository;
import com.oracle.oci.rmc.dcms.impl.dal.PluggablePortRepository;
import com.oracle.oci.rmc.dcms.impl.dal.PortRepository;
import com.oracle.oci.rmc.dcms.impl.dal.PurchaseOrderItemRepository;
import com.oracle.oci.rmc.dcms.impl.dal.PurchaseOrderLineRepository;
import com.oracle.oci.rmc.dcms.impl.dal.PurchaseOrderRepository;
import com.oracle.oci.rmc.dcms.impl.dal.RackInstanceDetailsRepository;
import com.oracle.oci.rmc.dcms.impl.dal.RackInstanceRepository;
import com.oracle.oci.rmc.dcms.impl.dal.RackModelRepository;
import com.oracle.oci.rmc.dcms.impl.dal.RackPositionRepository;
import com.oracle.oci.rmc.dcms.impl.dal.RackRowRepository;
import com.oracle.oci.rmc.dcms.impl.dal.RealmRepository;
import com.oracle.oci.rmc.dcms.impl.dal.RegionRepository;
import com.oracle.oci.rmc.dcms.impl.dal.RoomRepository;
import com.oracle.oci.rmc.dcms.impl.dal.ShortRackIdCounterRepository;
import com.oracle.oci.rmc.dcms.impl.dal.SiteRepository;
import com.oracle.oci.rmc.dcms.impl.dal.StorageRoomRepository;
import com.oracle.oci.rmc.dcms.impl.dal.TransceiverRepository;
import com.oracle.oci.rmc.dcms.impl.dal.TrunkDefinitionEndpointRepository;
import com.oracle.oci.rmc.dcms.impl.dal.TrunkDefinitionRepository;
import com.oracle.oci.rmc.dcms.impl.dal.inventory.AssetAttestationRepository;
import com.oracle.oci.rmc.dcms.impl.dal.inventory.AssetReservationRepository;
import com.oracle.oci.rmc.dcms.impl.dal.inventory.asset.AssetRepository;
import com.oracle.oci.rmc.dcms.impl.dal.inventory.assetbundle.AssetBundleRepository;
import com.oracle.oci.rmc.dcms.impl.dal.inventory.assetlocation.AssetLocationRepository;
import com.oracle.oci.rmc.dcms.impl.dal.inventory.assettransfer.AssetTransferRepository;
import com.oracle.oci.rmc.model.RmcId;
import com.oracle.oci.rmc.model.RmcIdType;
import com.oracle.oci.sfw.micronaut.http.pagination.PageQuery;
import io.micronaut.context.annotation.Prototype;
import io.micronaut.core.annotation.NonNull;
import jakarta.transaction.Transactional;
import java.security.SecureRandom;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings("PMD")
@Prototype
@Transactional
public class DcmsRepositoryTestUtil {

  private static final Logger LOG = LoggerFactory.getLogger(DcmsRepositoryTestUtil.class);
  public static final String UNIT_TEST_USER = "test-user";
  public static final String AISLE_A = "A";
  public static final String AISLE_B = "B";
  public static final String AISLE_C = "C";
  private static final Random RANDOM = new SecureRandom();
  private final DeviceInstanceRepository deviceInstanceRepository;
  RealmEntity realmEntity;
  MarketEntity marketEntity;
  RegionEntity regionEntity;
  SiteEntity siteEntity;
  Building building;
  DataHall dataHall;
  DataHall dataHall2;
  FabricEntity fabricEntity;
  FabricEntity fabricEntity2;
  RoomEntity roomEntity;
  RoomEntity roomEntity2;
  RoomEntity roomEntity3;
  AssetLocation assetLocation;
  private final RealmRepository realmRepository;
  private final MarketRepository marketRepository;
  private final RegionRepository regionRepository;
  private final SiteRepository siteRepository;
  private final BuildingRepository buildingRepository;
  private final DataHallRepository dataHallRepository;
  private final RackModelRepository rackRepository;
  private final RoomRepository roomRepository;
  private final RackRowRepository rackRowRepository;
  private final DeviceModelRepository deviceModelRepository;
  private final PortRepository portRepository;
  private final PartRepository partRepository;
  private final PartCategoryRepository partCategoryRepository;
  private final PartsCompatibilityRepository partsCompatibilityRepository;
  private final FabricRepository fabricRepository;
  private final NodeGroupRepository nodeGroupRepository;
  private final NodeGroupToFabricRepository nodeGroupToFabricRepository;
  private final NodeGroupToDeviceModelRepository nodeGroupToDeviceRepository;
  private final ConnectionRepository connectionRepository;
  private final RackPositionRepository rackPositionRepository;
  private final StorageRoomRepository storageRoomRepository;
  private final AssetLocationRepository assetLocationRepository;
  private final AssetService assetService;
  private final AssetRepository assetRepository;
  private final AssetBundleRepository assetBundleRepository;
  private final RackInstanceRepository rackInstanceRepository;
  private final RackInstanceDetailsRepository rackInstanceDetailsRepository;
  private final TransceiverRepository transceiverRepository;
  private final PluggablePortRepository pluggablePortRepository;
  private final NetworkCableRepository networkCableRepository;
  private final NetworkCableLegRepository networkCableLegRepository;
  private final PatchPanelPortAttributesRepository patchPanelPortAttributesRepository;
  private final AssetReservationRepository assetReservationRepository;
  private final AssetTransferRepository assetTransferRepository;
  private final AssetAttestationRepository assetAttestationRepository;
  private final PurchaseOrderLineRepository purchaseOrderLineRepository;
  private final PurchaseOrderItemRepository purchaseOrderItemRepository;
  private final PurchaseOrderRepository purchaseOrderRepository;
  private final RackPlatformRepository rackPlatformRepository;
  private final CableRepository cableRepository;
  private final ExternalPeerRepository externalPeerRepository;
  private final AuditEventRepository auditEventRepository;
  private final ShortRackIdCounterRepository shortRackIdCounterRepository;
  private final TrunkDefinitionRepository trunkDefinitionRepository;
  private final TrunkDefinitionEndpointRepository trunkDefinitionEndpointRepository;
  private final ChangeOrderRepository changeOrderRepository;
  private final ChangeTaskRepository changeTaskRepository;

  private final String region = RandomStringUtils.insecure().nextAlphanumeric(3);

  public DcmsRepositoryTestUtil(
      PartCategoryRepository partCategoryRepository,
      ConnectionRepository connectionRepository,
      DeviceModelRepository deviceModelRepository,
      FabricRepository fabricRepository,
      NodeGroupRepository nodeGroupRepository,
      NodeGroupToFabricRepository nodeGroupToFabricRepository,
      NodeGroupToDeviceModelRepository nodeGroupToDeviceRepository,
      MarketRepository marketRepository,
      PortRepository portRepository,
      PartRepository partRepository,
      PartsCompatibilityRepository partsCompatibilityRepository,
      RackModelRepository rackRepository,
      RealmRepository realmRepository,
      RegionRepository regionRepository,
      RoomRepository roomRepository,
      RackRowRepository rackRowRepository,
      BuildingRepository buildingRepository,
      SiteRepository siteRepository,
      DataHallRepository dataHallRepository,
      RackPositionRepository rackPositionRepository,
      StorageRoomRepository storageRoomRepository,
      AssetLocationRepository assetLocationRepository,
      AssetService assetService,
      AssetRepository assetRepository,
      AssetBundleRepository assetBundleRepository,
      RackInstanceRepository rackInstanceRepository,
      DeviceInstanceRepository deviceInstanceRepository,
      RackInstanceDetailsRepository rackInstanceDetailsRepository,
      AssetReservationRepository assetReservationRepository,
      AssetTransferRepository assetTransferRepository,
      TransceiverRepository transceiverRepository,
      PluggablePortRepository pluggablePortRepository,
      NetworkCableRepository networkCableRepository,
      NetworkCableLegRepository networkCableLegRepository,
      PatchPanelPortAttributesRepository patchPanelPortAttributesRepository,
      AssetAttestationRepository assetAttestationRepository,
      ExternalPeerRepository externalPeerRepository,
      AuditEventRepository auditEventRepository,
      PurchaseOrderLineRepository purchaseOrderLineRepository,
      PurchaseOrderItemRepository purchaseOrderItemRepository,
      PurchaseOrderRepository purchaseOrderRepository,
      RackPlatformRepository rackPlatformRepository,
      CableRepository cableRepository,
      ShortRackIdCounterRepository shortRackIdCounterRepository,
      TrunkDefinitionRepository trunkDefinitionRepository,
      TrunkDefinitionEndpointRepository trunkDefinitionEndpointRepository,
      ChangeOrderRepository changeOrderRepository,
      ChangeTaskRepository changeTaskRepository) {
    this.partCategoryRepository = partCategoryRepository;
    this.connectionRepository = connectionRepository;
    this.deviceModelRepository = deviceModelRepository;
    this.fabricRepository = fabricRepository;
    this.nodeGroupRepository = nodeGroupRepository;
    this.nodeGroupToFabricRepository = nodeGroupToFabricRepository;
    this.nodeGroupToDeviceRepository = nodeGroupToDeviceRepository;
    this.marketRepository = marketRepository;
    this.portRepository = portRepository;
    this.partRepository = partRepository;
    this.partsCompatibilityRepository = partsCompatibilityRepository;
    this.rackRepository = rackRepository;
    this.realmRepository = realmRepository;
    this.regionRepository = regionRepository;
    this.roomRepository = roomRepository;
    this.rackRowRepository = rackRowRepository;
    this.siteRepository = siteRepository;
    this.dataHallRepository = dataHallRepository;
    this.buildingRepository = buildingRepository;
    this.rackPositionRepository = rackPositionRepository;
    this.storageRoomRepository = storageRoomRepository;
    this.assetLocationRepository = assetLocationRepository;
    this.assetService = assetService;
    this.assetRepository = assetRepository;
    this.assetBundleRepository = assetBundleRepository;
    this.rackInstanceRepository = rackInstanceRepository;
    this.deviceInstanceRepository = deviceInstanceRepository;
    this.rackInstanceDetailsRepository = rackInstanceDetailsRepository;
    this.assetReservationRepository = assetReservationRepository;
    this.assetTransferRepository = assetTransferRepository;
    this.transceiverRepository = transceiverRepository;
    this.pluggablePortRepository = pluggablePortRepository;
    this.networkCableRepository = networkCableRepository;
    this.networkCableLegRepository = networkCableLegRepository;
    this.patchPanelPortAttributesRepository = patchPanelPortAttributesRepository;
    this.assetAttestationRepository = assetAttestationRepository;
    this.purchaseOrderRepository = purchaseOrderRepository;
    this.purchaseOrderLineRepository = purchaseOrderLineRepository;
    this.purchaseOrderItemRepository = purchaseOrderItemRepository;
    this.externalPeerRepository = externalPeerRepository;
    this.auditEventRepository = auditEventRepository;
    this.rackPlatformRepository = rackPlatformRepository;
    this.cableRepository = cableRepository;
    this.shortRackIdCounterRepository = shortRackIdCounterRepository;
    this.trunkDefinitionRepository = trunkDefinitionRepository;
    this.trunkDefinitionEndpointRepository = trunkDefinitionEndpointRepository;
    this.changeOrderRepository = changeOrderRepository;
    this.changeTaskRepository = changeTaskRepository;
  }

  @Transactional
  public RackModel createDeviceParentEntities() {
    createRackParentEntities();

    return rackRepository.save(
        new RackModel(
            RmcId.generate(RmcIdType.DCMS.RACK_MODEL),
            region,
            regionEntity.airportCode() + "1-rack1",
            "test_platform",
            "2403XC8006",
            createAndGetRackPosition("0101"),
            RackType.SERVER,
            "bspine",
            "sk-platform",
            1));
  }

  @Transactional
  public void createRackParentEntities() {
    createRoomParentEntities();

    fabricEntity =
        fabricRepository.save(
            new FabricEntity(
                RmcId.generate(RmcIdType.DCMS.FABRIC),
                regionEntity.airportCode(),
                "bldg1-cfabt311",
                "cfab",
                "1",
                "ACTIVE",
                siteEntity.regionalId().id(),
                1));

    fabricEntity2 =
        fabricRepository.save(
            new FabricEntity(
                RmcId.generate(RmcIdType.DCMS.FABRIC),
                regionEntity.airportCode(),
                "bldg1-cfabt312",
                "cfab",
                "1",
                "ACTIVE",
                siteEntity.regionalId().id(),
                1));

    roomEntity = createRoom(dataHall, "Room_" + UUID.randomUUID());
    roomEntity2 = createRoom(dataHall, "Room_" + UUID.randomUUID());
    roomEntity3 = createRoom(dataHall2, "Room_" + UUID.randomUUID());
  }

  @Transactional
  public void createRoomParentEntities() {

    createRegion();

    String randomSuffix = RandomStringUtils.insecure().nextAlphanumeric(7);
    siteEntity =
        siteRepository.save(
            new SiteEntity(
                new RegionalId(regionEntity.airportCode()),
                "site" + randomSuffix,
                "ad",
                "ACTIVE",
                1,
                null));

    building =
        buildingRepository.save(
            new Building(
                RmcId.generate(RmcIdType.Inventory.BUILDING),
                "Building " + randomSuffix,
                new Addresses(List.of("5502 Spinks Rd", "Abilene", "TX", "79601")),
                new Coordinates(32.501_561F, -99.789_117F),
                "Crusoe Energy Systems, LLC",
                PhysicalEntityLifecycleState.ACTIVE));
    shortRackIdCounterRepository.save(new ShortRackIdCounter(building.id()));
    dataHall = createDataHall("Data Hall " + randomSuffix);
    dataHall2 = createDataHall("Data Hall " + randomSuffix + " 2");
  }

  public void deleteAllEntities() {

    rackPlatformRepository.deleteAll();

    // network
    connectionRepository.deleteAll();
    portRepository.deleteAll();
    deleteConnections();
    externalPeerRepository.deleteAll();
    trunkDefinitionEndpointRepository.deleteAll();
    trunkDefinitionRepository.deleteAll();

    // fabric relations
    nodeGroupToDeviceRepository.deleteAll();

    // racks and devices
    deviceInstanceRepository.deleteAll();
    deviceModelRepository.deleteAll();
    rackInstanceRepository.deleteAll();
    rackRepository.deleteAll();

    // fabric
    nodeGroupToFabricRepository.deleteAll();
    nodeGroupRepository.deleteAll();
    fabricRepository.deleteAll();

    // inventory
    assetAttestationRepository.deleteAll();
    assetRepository.hardDeleteAllAssets();
    assetBundleRepository.deleteAll();
    assetReservationRepository.deleteAll();
    assetTransferRepository.hardDeleteAllAssetTransfers();
    assetLocationRepository.hardDeleteAllAssetLocations();
    storageRoomRepository.deleteAll();
    partsCompatibilityRepository.deleteAll();
    partRepository.deleteAll();
    partCategoryRepository.deleteAll();

    // physical locations
    rackPositionRepository.deleteAll();
    rackRowRepository.deleteAll();
    roomRepository.deleteAll();
    dataHallRepository.deleteAll();
    shortRackIdCounterRepository.deleteAll();
    buildingRepository.deleteAll();

    // logical locations
    siteRepository.deleteAll();
    regionRepository.deleteAll();
    marketRepository.deleteAll();
    realmRepository.deleteAll();

    // Audit Events
    auditEventRepository.hardDeleteAllAuditEvents();
  }

  public void deleteConnections() {
    // Remove all physical connections
    networkCableLegRepository.deleteAll();

    // Remove all ports
    patchPanelPortAttributesRepository.deleteAll();
    pluggablePortRepository.deleteAllChildPorts();
    pluggablePortRepository.deleteAll();

    // Remove transceivers
    transceiverRepository.deleteAll();

    // Remove all network cables
    networkCableRepository.deleteAll();
  }

  public RegionEntity getRegionEntity() {
    return this.regionEntity;
  }

  public SiteEntity getSiteEntity() {
    return this.siteEntity;
  }

  public DataHall getDataHall() {
    return this.dataHall;
  }

  public DataHall getDataHall2() {
    return this.dataHall2;
  }

  public FabricEntity getFabricEntity() {
    return this.fabricEntity;
  }

  public FabricEntity getFabricEntity2() {
    return this.fabricEntity2;
  }

  public RoomEntity getRoomEntity() {
    return roomEntity;
  }

  public RoomEntity getRoomEntity2() {
    return roomEntity2;
  }

  public RoomEntity getRoomEntity3() {
    return roomEntity3;
  }

  public Building getBuildingEntity() {
    return building;
  }

  public AssetLocation getAssetLocationEntity() {
    return assetLocation;
  }

  public DataHall createBuildingAndHall() {
    var building =
        new Building(
            RmcId.generate(RmcIdType.Inventory.BUILDING),
            "Building A",
            new Addresses(List.of("5502 Spinks Rd", "Abilene", "TX", "79601")),
            new Coordinates(32.501_561F, -99.789_117F),
            "Crusoe Energy Systems, LLC",
            PhysicalEntityLifecycleState.ACTIVE);
    building = buildingRepository.save(building);

    var dataHall =
        new DataHall(
            RmcId.generate(RmcIdType.Inventory.DATA_HALL),
            "Data Hall A",
            "1",
            100.0F,
            1000F,
            6000F,
            building.id(),
            "displayName");
    return dataHallRepository.save(dataHall);
  }

  public RackPosition createRackPosition(
      String rackNumber, RoomEntity room, int floorRow, int floorPosition) {
    return rackPositionRepository.save(
        new RackPosition(
            RmcId.generate(RmcIdType.DCMS.RACK_POSITION),
            "desc",
            RackPositionType.FLOOR,
            floorRow,
            floorPosition,
            rackNumber,
            RackPositionLifecycleState.READY,
            null,
            room.id(),
            siteEntity.regionalId().id(),
            List.of(),
            building.id(),
            shortRackIdCounterRepository.assignShortRackIdInBuilding(building.id())));
  }

  public RackPosition createRackPosition(
      String rackNumber,
      RoomEntity room,
      int floorRow,
      int floorPosition,
      RackPositionLifecycleState state) {
    return rackPositionRepository.save(
        new RackPosition(
            RmcId.generate(RmcIdType.DCMS.RACK_POSITION),
            "desc",
            RackPositionType.FLOOR,
            floorRow,
            floorPosition,
            rackNumber,
            state,
            null,
            room.id(),
            siteEntity.regionalId().id(),
            List.of(),
            building.id(),
            1));
  }

  public RackPosition createRackPosition(
      String rackNumber,
      RoomEntity room,
      int floorRow,
      int floorPosition,
      SiteEntity siteEntity,
      Building building) {
    return rackPositionRepository.save(
        new RackPosition(
            RmcId.generate(RmcIdType.DCMS.RACK_POSITION),
            "desc",
            RackPositionType.FLOOR,
            floorRow,
            floorPosition,
            rackNumber,
            RackPositionLifecycleState.READY,
            null,
            room.id(),
            siteEntity.regionalId().id(),
            List.of(),
            building.id(),
            1));
  }

  public RackPosition updateRackPositionLifeCycleState(
      RackPosition rackPosition, RackPositionLifecycleState rackPositionLifecycleState) {
    var upatedRackPosition = rackPosition.withLifecycleState(rackPositionLifecycleState);
    rackPositionRepository.update(upatedRackPosition);
    return upatedRackPosition;
  }

  public RackModel createRackAtPosition(RackPosition position) {
    return rackRepository.save(
        new RackModel(
            RmcId.generate(RmcIdType.DCMS.RACK_MODEL),
            region,
            siteEntity.displayName() + "-rack1",
            "Reg_Comp_E2-2C_Rack.01",
            "profile",
            position,
            RackType.SERVER,
            "bspine",
            "sk-platform",
            1));
  }

  public RackPosition createAndGetRackPosition(String rackNumber, int floorRow, int floorPosition) {
    var room = getRoomEntity();
    return createRackPosition(rackNumber, room, floorRow, floorPosition);
  }

  public RackPosition createAndGetRackPosition(String rackNumber) {
    var room = getRoomEntity();
    return createRackPosition(rackNumber, room, 1, 1);
  }

  public RackPosition makeRackPositionReady(RackPosition rackPosition) {
    var upatedRackPosition =
        rackPosition
            .withIsInfraReady(true)
            .withIsPowerReady(true)
            .withIsNetworkReady(true)
            .withIsCoolingReady(true)
            .withIsStranded(true);
    rackPositionRepository.update(upatedRackPosition);
    return upatedRackPosition;
  }

  public RackModel createAndGetComputeRackModel(
      RackPosition position, String airportCode, SiteEntity site) {
    return rackRepository.save(
        new RackModel(
            RmcId.generate(RmcIdType.DCMS.RACK_MODEL),
            airportCode,
            site.displayName() + "-rack1",
            "Reg_Comp_E2-2C_Rack.01",
            "profile",
            position,
            RackType.SERVER,
            Objects.equals(position.rackNumber(), "001") ? "fleaf" : "fspine",
            "sk-platform",
            1));
  }

  public RackModel createAndGetComputeRackModel() {
    return rackRepository.save(
        new RackModel(
            RmcId.generate(RmcIdType.DCMS.RACK_MODEL),
            region,
            siteEntity.displayName() + "-rack1",
            "Reg_Comp_E2-2C_Rack.01",
            "profile",
            createAndGetRackPosition("0101"),
            RackType.SERVER,
            "bspine",
            "sk-platform",
            1));
  }

  public RackModel createAndGetComputeCustomizableRackModel(
      String rackPlatform, String rackNumber) {
    return rackRepository.save(
        new RackModel(
            RmcId.generate(RmcIdType.DCMS.RACK_MODEL),
            region,
            siteEntity.displayName() + "-rack1",
            rackPlatform,
            "profile",
            createAndGetRackPosition(rackNumber),
            RackType.SERVER,
            "bspine",
            "sk-platform",
            1));
  }

  public RackModel createAndGetRackModel(String skRackPlatform, String rackNumber) {
    return rackRepository.save(
        new RackModel(
            RmcId.generate(RmcIdType.DCMS.RACK_MODEL),
            region,
            siteEntity.displayName() + "-rack1",
            skRackPlatform,
            "profile",
            createAndGetRackPosition(rackNumber),
            RackType.SERVER,
            "bspine",
            skRackPlatform,
            1));
  }

  public void updateRackModelToInstanceAssigned(RackModel rackModel) {
    rackRepository.update(
        rackModel.with().lifecycleState(RackModelLifecycleState.INSTANCE_ASSIGNED).build());
  }

  public RackModel createRackModelWithParentEntities(RegionEntity region) {

    // create building
    Building building = createBuilding();

    // crate data hall
    DataHall dataHall = createDataHall("dataHall-temp", building.id(), "10");

    // create room
    RoomEntity room = createRoom(dataHall, "room1");

    // create site
    SiteEntity siteEntity = createSite(region.airportCode());

    // create rack position
    RackPosition rackPosition = createRackPosition("001", room, 0, 1, siteEntity, building);

    // create rack model
    return createAndGetComputeRackModel(rackPosition, region.airportCode(), siteEntity);
  }

  public RackModel createRackModelInBuilding(
      RegionEntity region,
      Building building,
      String dataHallName,
      String roomName,
      String rackNumber,
      int siteIndex) {

    // crate data hall
    DataHall dataHall = createDataHall(dataHallName, building.id(), "10");

    // create room
    RoomEntity room = createRoom(dataHall, roomName);

    // create site
    SiteEntity siteEntity = createSite(region.airportCode(), siteIndex);

    // create rack position
    RackPosition rackPosition = createRackPosition(rackNumber, room, 0, 1, siteEntity, building);

    // create rack model
    return createAndGetComputeRackModel(rackPosition, region.airportCode(), siteEntity);
  }

  public void createMultipleRackModels(RegionEntity region) {

    // create building
    building = createBuilding();

    // crate data hall
    dataHall = createDataHall("dataHall-temp", building.id(), "10");

    // create room
    roomEntity = createRoom(dataHall, "room1");

    // create site
    siteEntity = createSite(region.airportCode());

    // create rack position and rack model entities
    for (int i = 1; i <= 4; i++) {
      String rackNumber = String.format("%03d", i); // 001, 002, 003, 004
      RackPosition rackPosition = createRackPosition(rackNumber, roomEntity, 0, i);
      createAndGetComputeRackModel(rackPosition, region.airportCode(), siteEntity);
    }
  }

  public RackModel createAndGetNetworkRackModel(@NonNull String rackPlatform) {
    return rackRepository.save(
        new RackModel(
            RmcId.generate(RmcIdType.DCMS.RACK_MODEL),
            region,
            "displayName",
            rackPlatform,
            "profile",
            createRackPosition(UUID.randomUUID().toString(), roomEntity, 0, 1),
            RackType.SERVER,
            "sk-platform",
            "bspine",
            1));
  }

  public RackModel createPassiveRackModelWithDevicesAndPorts(
      @NonNull String elevation, @NonNull Integer rackIdx, @NonNull List<String> slots) {
    RackModel rackModel =
        rackRepository.save(
            new RackModel(
                RmcId.generate(RmcIdType.DCMS.RACK_MODEL),
                region,
                "Passive-Rack-" + rackIdx,
                "passive-rack-platform",
                "profile",
                createRackPosition(UUID.randomUUID().toString(), roomEntity, 0, 1 + rackIdx),
                RackType.NETWORK,
                "sk-platform",
                "passive-rack-platform",
                rackIdx));

    DeviceModel patchPanelDevice =
        createPatchPanelInRack(rackModel, "twelveshelf_sixmod_oneport", elevation);

    DeviceModel childDeviceModel1 =
        createPatchPanelChildDeviceInRack(
            rackModel, "edge-2u_corning", elevation, 0, slots.get(0), patchPanelDevice.id());

    PluggablePort frontPort1 =
        createPluggablePortForFrontPatchPanel(childDeviceModel1.id(), FRONT, "front-port-1", 1);
    createPluggablePortForRearPatchPanel(frontPort1, "rear-port-1");
    PluggablePort frontPort2 =
        createPluggablePortForFrontPatchPanel(childDeviceModel1.id(), FRONT, "front-port-2", 2);
    createPluggablePortForRearPatchPanel(frontPort2, "rear-port-2");

    DeviceModel childDeviceModel2 =
        createPatchPanelChildDeviceInRack(
            rackModel, "edge-2u_corning", elevation, 1, slots.get(1), patchPanelDevice.id());

    PluggablePort frontPort3 =
        createPluggablePortForFrontPatchPanel(childDeviceModel2.id(), FRONT, "front-port-3", 1);
    createPluggablePortForRearPatchPanel(frontPort3, "rear-port-3");

    return rackModel;
  }

  public List<PluggablePort> getPluggablePortsByRackId(RackModel rackModel, String elevation) {
    List<PluggablePort> ports = new ArrayList<>();
    List<DeviceModel> devices =
        deviceModelRepository.findAllByRackIdAndElevation(region, rackModel.id(), elevation);
    devices.forEach(
        d ->
            ports.addAll(
                pluggablePortRepository.findAllByDeviceModelIdInList(List.of(d.id())).stream()
                    .sorted(Comparator.comparingInt(PluggablePort::portNumber))
                    .toList()));
    return ports;
  }

  public TrunkDefinition createTrunkDefinition(
      @NonNull RmcId sourceRackModelId,
      @NonNull RmcId destinationRackModelId,
      @NonNull String sourceRackElevation,
      @NonNull String destinationRackElevation,
      TrunkPath path,
      TrunkType type) {
    TrunkDefinition trunkDefinition =
        new TrunkDefinition(
            RmcId.generate(RmcIdType.DCMS.TRUNK_DEFINITION),
            region,
            10.0F,
            "aaa",
            "PF-PF",
            null,
            null,
            path,
            type,
            new TrunkAdditionalMetadata("partNumber", "partName", 1, "fiberBase"),
            TrunkLifecycleState.CREATED);

    TrunkDefinitionEndpoint sourceEndpoint =
        new TrunkDefinitionEndpoint(
            new TrunkDefinitionEndpointId(trunkDefinition.id(), sourceRackModelId),
            "A",
            new PatchPanelSelector(
                sourceRackElevation,
                Map.of(
                    1,
                    new ShelfSelector(
                        Map.of(
                            "A", new ModuleSelector("REAR", List.of(1, 2)),
                            "B", new ModuleSelector("REAR", List.of(1)))))),
            null);
    TrunkDefinitionEndpoint destinationEndpoint =
        new TrunkDefinitionEndpoint(
            new TrunkDefinitionEndpointId(trunkDefinition.id(), destinationRackModelId),
            "B",
            new PatchPanelSelector(
                destinationRackElevation,
                Map.of(
                    1,
                    new ShelfSelector(Map.of("A", new ModuleSelector("REAR", List.of(1, 2)))),
                    2,
                    new ShelfSelector(Map.of("A", new ModuleSelector("REAR", List.of(1)))))),
            null);
    return trunkDefinition.withEndpoints(List.of(sourceEndpoint, destinationEndpoint));
  }

  public DeviceModel createTierZeroDeviceInRack(
      RackModel rackModel, String devicePlatformName, String elevation) {
    return createTierZeroDeviceInRack(
        rackModel, devicePlatformName, elevation, "displayName", "hostName");
  }

  public DeviceModel createTierZeroDeviceInRack(
      RackModel rackModel, String devicePlatformName, String elevation, String role) {
    return createTierZeroDeviceInRack(
        rackModel, devicePlatformName, elevation, "displayName", "hostName", "deviceOpn", role);
  }

  public DeviceModel createTierZeroDeviceInRack(
      RackModel rackModel,
      String devicePlatformName,
      String elevation,
      String displayName,
      String hostName) {
    return createTierZeroDeviceInRack(
        rackModel, devicePlatformName, elevation, displayName, hostName, "deviceOpn");
  }

  public DeviceModel createTierZeroDeviceInRack(
      RackModel rackModel,
      String devicePlatformName,
      String elevation,
      String displayName,
      String hostName,
      String opn) {
    return deviceModelRepository.save(
        new DeviceModel(
            RmcId.generate(RmcIdType.DCMS.DEVICE_MODEL),
            rackModel.region(),
            displayName,
            devicePlatformName,
            "mtor",
            "rackPlatformDeviceClass",
            "rackPlatformDeviceVendorName",
            opn,
            1,
            elevation,
            hostName,
            new DeviceAttributes(
                DeviceType.ACTIVE,
                new ActiveDeviceAttributes("configPlatform", "firmwareFamily"),
                null),
            DeviceType.ACTIVE,
            rackModel.id(),
            1));
  }

  public DeviceModel createTierZeroDeviceInRack(
      RackModel rackModel,
      String devicePlatformName,
      String elevation,
      String displayName,
      String hostName,
      String opn,
      String role) {
    return deviceModelRepository.save(
        new DeviceModel(
            RmcId.generate(RmcIdType.DCMS.DEVICE_MODEL),
            rackModel.region(),
            displayName,
            devicePlatformName,
            role,
            "rackPlatformDeviceClass",
            "rackPlatformDeviceVendorName",
            opn,
            1,
            elevation,
            hostName,
            new DeviceAttributes(
                DeviceType.ACTIVE,
                new ActiveDeviceAttributes("configPlatform", "firmwareFamily"),
                null),
            DeviceType.ACTIVE,
            rackModel.id(),
            1));
  }

  public DeviceModel createPatchPanelInRack(
      RackModel rackModel, String devicePlatformName, String elevation) {
    return deviceModelRepository.save(
        new DeviceModel(
            RmcId.generate(RmcIdType.DCMS.DEVICE_MODEL),
            rackModel.region(),
            "displayName",
            devicePlatformName,
            "PatchPanelRole",
            "patchPanelModel",
            "PatchPanelVendorName",
            "deviceOpn",
            2,
            elevation,
            null,
            new DeviceAttributes(
                DeviceType.PASSIVE,
                null,
                new PassiveDeviceAttributes(
                    6,
                    6,
                    6,
                    List.of("A", "B", "C", "D", "E", "F"),
                    List.of("1", "2", "3", "4", "5", "6"),
                    List.of("FRONT"),
                    72,
                    null)),
            DeviceType.PASSIVE,
            rackModel.id(),
            1));
  }

  public DeviceModel createPatchPanelChildDeviceInRack(
      RackModel rackModel,
      String devicePlatformName,
      String elevation,
      Integer deviceIdx,
      String slot,
      RmcId parentDeviceId) {
    return deviceModelRepository.save(
        new DeviceModel(
            RmcId.generate(RmcIdType.DCMS.DEVICE_MODEL),
            rackModel.region(),
            "displayName" + deviceIdx,
            devicePlatformName,
            "cassette",
            "patchPanelModel",
            "PatchPanelVendorName",
            "deviceOpn",
            2,
            elevation,
            slot,
            "hostName" + deviceIdx,
            null,
            DeviceType.PASSIVE,
            parentDeviceId,
            rackModel.id(),
            deviceIdx));
  }

  public DeviceModel createDeviceModelToAssign(
      RackModel rackModel, String oraclePartNumber, String elevation) {
    return deviceModelRepository.save(
        new DeviceModel(
            RmcId.generate(RmcIdType.DCMS.DEVICE_MODEL),
            rackModel.region(),
            "displayName",
            "devicePlatformName",
            "PatchPanelRole",
            "patchPanelModel",
            "PatchPanelVendorName",
            oraclePartNumber,
            2,
            elevation,
            null,
            new DeviceAttributes(
                DeviceType.ACTIVE,
                null,
                new PassiveDeviceAttributes(
                    6,
                    6,
                    6,
                    List.of("A", "B", "C", "D", "E", "F"),
                    List.of("1", "2", "3", "4", "5", "6"),
                    List.of("FRONT"),
                    72,
                    null)),
            DeviceType.ACTIVE,
            rackModel.id(),
            1));
  }

  public DeviceModel createPatchPanelV2InRack(
      RackModel rackModel, String devicePlatformName, String elevation) {
    return deviceModelRepository.save(
        new DeviceModel(
            RmcId.generate(RmcIdType.DCMS.DEVICE_MODEL),
            rackModel.region(),
            "displayName",
            devicePlatformName,
            "PatchPanelRole",
            "patchPanelModel",
            "PatchPanelVendorName",
            "ppDeviceOpn",
            2,
            elevation,
            null,
            new DeviceAttributes(
                DeviceType.PASSIVE,
                null,
                new PassiveDeviceAttributes(0, 0, 0, null, null, null, 0, null)),
            DeviceType.PASSIVE,
            rackModel.id(),
            1));
  }

  public void createMockCutsheetData() {
    realmEntity = realmRepository.save(new RealmEntity("r1"));
    marketEntity = marketRepository.save(new MarketEntity("m1"));
    regionEntity =
        regionRepository.save(new RegionEntity(region, "r1", "m1", ServiceType.COMMERCIAL, region));
    siteEntity =
        siteRepository.save(
            new SiteEntity(new RegionalId(region), "Building_A", "AD_01", "active", 1, null));

    building =
        buildingRepository.save(
            new Building(
                RmcId.generate(RmcIdType.Inventory.BUILDING),
                "Building A",
                new Addresses(List.of("5502 Spinks Rd", "Abilene", "TX", "79601")),
                new Coordinates(32.501561F, -99.789117F),
                "Crusoe Energy Systems, LLC",
                PhysicalEntityLifecycleState.ACTIVE));

    shortRackIdCounterRepository.save(new ShortRackIdCounter(building.id()));

    dataHall =
        dataHallRepository.save(
            new DataHall(
                RmcId.generate(RmcIdType.Inventory.DATA_HALL),
                "Data Hall A",
                "1",
                100.0F,
                1000F,
                6000F,
                building.id(),
                "displayName"));

    roomEntity =
        roomRepository.save(
            new RoomEntity(
                RmcId.generate(RmcIdType.Inventory.ROOM),
                "Room name 1",
                "Description Room 1",
                "provider Room Name 1",
                1,
                dataHall.id()));

    RackModel rack1 =
        rackRepository.save(
            new RackModel(
                RmcId.generate(RmcIdType.DCMS.RACK_MODEL),
                region,
                "ra1",
                "rp1",
                "rpr",
                createAndGetRackPosition("002"),
                RackType.SERVER,
                "bspine",
                "sk-platform",
                1));
    RackModel rack2 =
        rackRepository.save(
            new RackModel(
                RmcId.generate(RmcIdType.DCMS.RACK_MODEL),
                region,
                "ra2",
                "rp1",
                "rpr",
                createAndGetRackPosition("003"),
                RackType.SERVER,
                "bspine",
                "sk-platform",
                1));

    DeviceModel device1 =
        deviceModelRepository.save(
            new DeviceModel(
                RmcId.generate(RmcIdType.DCMS.DEVICE_MODEL),
                region,
                "d1",
                "p1",
                "r1",
                "m1",
                "v1",
                "DeviceOpn",
                2,
                "e1",
                "h1",
                new DeviceAttributes(
                    DeviceType.ACTIVE, new ActiveDeviceAttributes("c1", "f1"), null),
                DeviceType.ACTIVE,
                rack1.id(),
                1));
    DeviceModel device2 =
        deviceModelRepository.save(
            new DeviceModel(
                RmcId.generate(RmcIdType.DCMS.DEVICE_MODEL),
                region,
                "d2",
                "p1",
                "r1",
                "m1",
                "v1",
                "ppDeviceOpn",
                3,
                "e2",
                "h1",
                new DeviceAttributes(
                    DeviceType.ACTIVE, new ActiveDeviceAttributes("c1", "f1"), null),
                DeviceType.ACTIVE,
                rack2.id(),
                1));

    PortEntity port1 =
        portRepository.save(
            new PortEntity(
                RmcId.generate(RmcIdType.DCMS.PORT),
                region,
                "p1",
                "p1",
                DevicePortType.BASE_T_10G,
                new PortAttributes(
                    device1.deviceType(),
                    new ActivePortAttributes("t1", List.of("g1"), 1, new ArrayList<>())),
                device1.id(),
                device1.deviceType(),
                1));
    PortEntity port2 =
        portRepository.save(
            new PortEntity(
                RmcId.generate(RmcIdType.DCMS.PORT),
                region,
                "p1",
                "p1",
                DevicePortType.BASE_T_1G,
                new PortAttributes(
                    device2.deviceType(),
                    new ActivePortAttributes("t1", List.of("g1"), 1, new ArrayList<>())),
                device2.id(),
                device2.deviceType(),
                2));

    connectionRepository.save(
        new ConnectionEntity(
            RmcId.generate(RmcIdType.DCMS.CONNECTION),
            region,
            port1.id(),
            port2.id(),
            "cs1",
            "cc1",
            null));
    connectionRepository.save(
        new ConnectionEntity(
            RmcId.generate(RmcIdType.DCMS.CONNECTION),
            region,
            port2.id(),
            port1.id(),
            "cs2",
            "cc2",
            null));
  }

  public List<RackModel> getRackModelByRegion(String region) {
    return rackRepository
        .findAllWithOptionalFilters(
            region, null, null, null, null, null, PageQuery.fromEmptyToken(Integer.MAX_VALUE))
        .getContent();
  }

  public List<DeviceModel> getDeviceByRegion(String region, RmcId rackModelId) {
    return deviceModelRepository.findAllWithOptionalFilters(
        region, rackModelId, null, null, null, null);
  }

  public List<ConnectionEntity> getConnections() {
    return connectionRepository.findAll();
  }

  @Transactional
  public PortEntity createPort(
      @NonNull DeviceModel deviceModel,
      int idx,
      @NonNull String portGroup,
      @NonNull DevicePortType devicePortType) {
    return portRepository.save(
        new PortEntity(
            RmcId.generate(RmcIdType.DCMS.PORT),
            region,
            "port-display-name-" + idx,
            "port-label" + idx,
            devicePortType,
            new PortAttributes(
                deviceModel.deviceType(),
                new ActivePortAttributes(
                    "port-topo-name-" + idx, List.of(portGroup), 1, new ArrayList<>())),
            deviceModel.id(),
            DeviceType.ACTIVE,
            idx));
  }

  @Transactional
  public PortEntity createPort(
      @NonNull DeviceModel deviceModel,
      int idx,
      @NonNull String portGroup,
      @NonNull String portNamePrefix,
      @NonNull DevicePortType devicePortType) {
    return portRepository.save(
        new PortEntity(
            RmcId.generate(RmcIdType.DCMS.PORT),
            region,
            portNamePrefix + idx,
            String.valueOf(idx),
            devicePortType,
            new PortAttributes(
                deviceModel.deviceType(),
                new ActivePortAttributes(
                    "port-topo-name-" + idx, List.of(portGroup), 1, new ArrayList<>())),
            deviceModel.id(),
            DeviceType.ACTIVE,
            idx));
  }

  public DeviceModel createDeviceInRack() {
    RackModel rackModel = createAndGetComputeRackModel();
    return createTierZeroDeviceInRack(rackModel, "e2-2c-compute", "1RU");
  }

  public DeviceModel createDeviceInRack(RackModel rackModel) {
    return createTierZeroDeviceInRack(rackModel, "e2-2c-compute", "1RU");
  }

  public DeviceModel createDeviceInRack(RackModel rackModel, String devicePlatformName) {
    return createTierZeroDeviceInRack(rackModel, devicePlatformName, "1RU", "pdu");
  }

  public DeviceModel createDeviceInRack(
      RackModel rackModel, String devicePlatformName, String elevation) {
    return createTierZeroDeviceInRack(rackModel, devicePlatformName, elevation);
  }

  public DeviceModel createPatchPanelDevice() {
    RackModel rackModel = createAndGetComputeRackModel();
    return createPatchPanelInRack(rackModel, "twelveshelf_sixmod_oneport", "4RU");
  }

  public List<DeviceModel> createPatchPanelsInMultipleRacks() {

    RackModel rackModel1 =
        rackRepository.save(
            new RackModel(
                RmcId.generate(RmcIdType.DCMS.RACK_MODEL),
                region,
                siteEntity.displayName() + "-rack1",
                "Reg_Comp_E2-2C_Rack.01",
                "profile1",
                createAndGetRackPosition("0101"),
                RackType.SERVER,
                "bspine",
                "sk-platform",
                1));

    RackModel rackModel2 =
        rackRepository.save(
            new RackModel(
                RmcId.generate(RmcIdType.DCMS.RACK_MODEL),
                region,
                siteEntity.displayName() + "-rack2",
                "Reg_Comp_E2-2C_Rack.01",
                "profile2",
                createAndGetRackPosition("0102"),
                RackType.SERVER,
                "bspine",
                "sk-platform",
                1));

    DeviceModel deviceModel1 =
        deviceModelRepository.save(
            new DeviceModel(
                RmcId.generate(RmcIdType.DCMS.DEVICE_MODEL),
                rackModel1.region(),
                "displayName1",
                "twelveshelf_sixmod_oneport",
                "PatchPanelRole",
                "patchPanelModel",
                "PatchPanelVendorName",
                "ppDeviceOpn",
                2,
                "4RU",
                null,
                new DeviceAttributes(
                    DeviceType.PASSIVE,
                    null,
                    new PassiveDeviceAttributes(
                        6,
                        6,
                        6,
                        List.of("A", "B", "C", "D", "E", "F"),
                        List.of("1", "2", "3", "4", "5", "6"),
                        List.of("FRONT"),
                        72,
                        null)),
                DeviceType.PASSIVE,
                rackModel1.id(),
                1));

    DeviceModel deviceModel2 =
        deviceModelRepository.save(
            new DeviceModel(
                RmcId.generate(RmcIdType.DCMS.DEVICE_MODEL),
                rackModel2.region(),
                "displayName2",
                "twelveshelf_sixmod_oneport",
                "PatchPanelRole",
                "patchPanelModel",
                "PatchPanelVendorName",
                "ppDeviceOpn",
                2,
                "4RU",
                null,
                new DeviceAttributes(
                    DeviceType.PASSIVE,
                    null,
                    new PassiveDeviceAttributes(
                        6,
                        6,
                        6,
                        List.of("A", "B", "C", "D", "E", "F"),
                        List.of("1", "2", "3", "4", "5", "6"),
                        List.of("FRONT"),
                        72,
                        null)),
                DeviceType.PASSIVE,
                rackModel2.id(),
                1));

    DeviceModel deviceModel3 =
        deviceModelRepository.save(
            new DeviceModel(
                RmcId.generate(RmcIdType.DCMS.DEVICE_MODEL),
                rackModel1.region(),
                "displayName3",
                "twelveshelf_sixmod_oneport",
                "PatchPanelRole",
                "patchPanelModel",
                "PatchPanelVendorName",
                "ppDeviceOpn",
                2,
                "5RU",
                null,
                new DeviceAttributes(
                    DeviceType.PASSIVE,
                    null,
                    new PassiveDeviceAttributes(
                        6,
                        6,
                        6,
                        List.of("A", "B", "C", "D", "E", "F"),
                        List.of("1", "2", "3", "4", "5", "6"),
                        List.of("FRONT"),
                        72,
                        null)),
                DeviceType.PASSIVE,
                rackModel1.id(),
                1));

    DeviceModel deviceModel4 =
        deviceModelRepository.save(
            new DeviceModel(
                RmcId.generate(RmcIdType.DCMS.DEVICE_MODEL),
                rackModel2.region(),
                "displayName4",
                "twelveshelf_sixmod_oneport",
                "PatchPanelRole",
                "patchPanelModel",
                "PatchPanelVendorName",
                "ppDeviceOpn",
                2,
                "5RU",
                null,
                new DeviceAttributes(
                    DeviceType.PASSIVE,
                    null,
                    new PassiveDeviceAttributes(
                        6,
                        6,
                        6,
                        List.of("A", "B", "C", "D", "E", "F"),
                        List.of("1", "2", "3", "4", "5", "6"),
                        List.of("FRONT"),
                        72,
                        null)),
                DeviceType.PASSIVE,
                rackModel2.id(),
                1));

    return Arrays.asList(deviceModel1, deviceModel2, deviceModel3, deviceModel4);
  }

  public RegionEntity createRegion(String code) {
    return regionRepository.save(
        new RegionEntity(
            code, realmEntity.name(), marketEntity.name(), ServiceType.COMMERCIAL, code));
  }

  public RegionEntity createRegion() {
    String randomSuffix = RandomStringUtils.insecure().nextAlphanumeric(7);
    realmEntity = realmRepository.save(new RealmEntity("rlm" + randomSuffix));
    LOG.info("Created new test realm: {}", realmEntity);
    marketEntity = marketRepository.save(new MarketEntity("mkt" + randomSuffix));
    LOG.info("Created new test market: {}", marketEntity);
    return createRegion(region, realmEntity.name(), marketEntity.name());
  }

  public RegionEntity createRegion(String airportCode, String realmName, String marketName) {
    regionEntity =
        regionRepository.save(
            new RegionEntity(
                airportCode, realmName, marketName, ServiceType.COMMERCIAL, airportCode));
    LOG.info("Created new test region: {}", regionEntity);
    return regionEntity;
  }

  public SiteEntity createSite() {
    return createSite(regionEntity.airportCode());
  }

  public SiteEntity createSite(String region) {
    return createSite(region, 1);
  }

  public SiteEntity createSite(String region, int index) {
    SiteEntity site =
        siteRepository.save(
            new SiteEntity(new RegionalId(region), "FRA5", "ad", "ACTIVE", index, null));
    LOG.info("Created new test site: {}", site);
    return site;
  }

  public DataHall createDataHall(String name) {
    return createDataHall(name, building.id(), "1");
  }

  public DataHall createDataHall(String name, RmcId buildingId, String floorNumber) {
    return dataHallRepository.save(
        new DataHall(
            RmcId.generate(RmcIdType.Inventory.DATA_HALL),
            name,
            floorNumber,
            100.0F,
            1000F,
            6000F,
            buildingId,
            "displayName"));
  }

  public StorageRoom createStorageRoom(
      RegionEntity region, Building building, DataHall dataHall, String name) {
    return storageRoomRepository.save(
        new StorageRoom(
            RmcId.generate(RmcIdType.Inventory.STORAGE_ROOM),
            region.id(),
            name,
            building.id().toString(),
            dataHall.id().toString()));
  }

  public AssetLocation createAssetLocationWithAisle(String aisle) {
    RmcId id = RmcId.generate(RmcIdType.Inventory.ASSET_LOCATION);
    return assetLocationRepository.saveAssetLocation(
        AssetLocationCreationDetailsBuilder.builder()
            .createdBy(UNIT_TEST_USER)
            .buildingId(getBuildingEntity().id())
            .storageRoomId(null)
            .bin("10")
            .aisle(aisle == null ? AISLE_A : aisle)
            .column("10")
            .dataHallId(getDataHall().id())
            .isActive(true)
            .name("test-" + id)
            .partId(null)
            .regionId(regionEntity.id().toString())
            .shelf("S")
            .type(AssetLocationType.PALLET)
            .build());
  }

  public AssetLocation createAssetLocation(
      RegionEntity region,
      Building building,
      DataHall dataHall,
      StorageRoom storageRoom,
      String name,
      String aisle) {
    return assetLocationRepository.saveAssetLocation(
        AssetLocationCreationDetailsBuilder.builder()
            .createdBy(UNIT_TEST_USER)
            .buildingId(building.id())
            .bin("10")
            .aisle(aisle == null ? AISLE_A : aisle)
            .column("10")
            .dataHallId(dataHall.id())
            .isActive(true)
            .name(name)
            .partId(null)
            .regionId(region.id())
            .storageRoomId(storageRoom.id())
            .shelf("S")
            .type(AssetLocationType.PALLET)
            .build());
  }

  public AssetLocation createAssetLocation(String regionId, String aisle, Boolean active) {
    var building =
        this.building != null && buildingRepository.existsById(this.building.id())
            ? this.building
            : createBuilding();
    var dataHall =
        this.dataHall != null && dataHallRepository.existsById(this.dataHall.id())
            ? this.dataHall
            : createDataHall(UUID.randomUUID().toString(), building.id(), "1");

    return assetLocationRepository.saveAssetLocation(
        AssetLocationCreationDetailsBuilder.builder()
            .regionId(regionId)
            .name("Test-Location")
            .type(AssetLocationType.SPARE)
            .aisle(aisle)
            .column("1")
            .shelf("S")
            .bin("2")
            .buildingId(building.id())
            .dataHallId(dataHall.id())
            .isActive(active)
            .createdBy(UNIT_TEST_USER)
            .build());
  }

  public AssetLocation createAssetLocation(String regionId, String aisle) {
    var building =
        this.building != null && buildingRepository.existsById(this.building.id())
            ? this.building
            : createBuilding();
    var dataHall =
        this.dataHall != null && dataHallRepository.existsById(this.dataHall.id())
            ? this.dataHall
            : createDataHall(UUID.randomUUID().toString(), building.id(), "1");

    return createAssetLocation(regionId, aisle, dataHall.id(), building.id()).get();
  }

  public AssetLocation createAssetLocation(String regionId) {
    var building =
        this.building != null && buildingRepository.existsById(this.building.id())
            ? this.building
            : createBuilding();
    var dataHall =
        this.dataHall != null && dataHallRepository.existsById(this.dataHall.id())
            ? this.dataHall
            : createDataHall(UUID.randomUUID().toString(), building.id(), "1");

    return createAssetLocation(regionId, AISLE_B, dataHall.id(), building.id()).get();
  }

  public AssetLocation createAssetLocation(
      String regionId, RmcId dataHallId, RmcId buildingId, String aisle) {
    RmcId id = RmcId.generate(RmcIdType.Inventory.ASSET_LOCATION);
    return assetLocationRepository.saveAssetLocation(
        AssetLocationCreationDetailsBuilder.builder()
            .createdBy(UNIT_TEST_USER)
            .buildingId(buildingId)
            .bin("10")
            .aisle(aisle)
            .column("10")
            .dataHallId(dataHallId)
            .isActive(true)
            .name("test-" + id)
            .partId(null)
            .regionId(regionId)
            .storageRoomId(null)
            .shelf("S")
            .type(AssetLocationType.PALLET)
            .build());
  }

  public Optional<AssetLocation> createAssetLocation(
      String regionId, String aisle, RmcId dataHallId, RmcId buildingId) {
    RmcId assetLocationId =
        assetLocationRepository
            .saveAssetLocation(
                AssetLocationCreationDetailsBuilder.builder()
                    .regionId(regionId)
                    .name("Test-Location")
                    .type(AssetLocationType.SPARE)
                    .aisle(aisle)
                    .column("1")
                    .shelf("S")
                    .bin("2")
                    .buildingId(buildingId)
                    .dataHallId(dataHallId)
                    .isActive(true)
                    .createdBy(UNIT_TEST_USER)
                    .build())
            .id();
    return assetLocationRepository.findById(assetLocationId);
  }

  public AssetLocation createAssetLocation(String regionId, AssetLocationType alType) {
    RmcId id = RmcId.generate(RmcIdType.Inventory.ASSET_LOCATION);
    var building = this.building != null ? this.building : createBuilding();
    var dataHall =
        this.dataHall != null
            ? this.dataHall
            : createDataHall(UUID.randomUUID().toString(), building.id(), "1");
    return assetLocationRepository.saveAssetLocation(
        AssetLocationCreationDetailsBuilder.builder()
            .createdBy(UNIT_TEST_USER)
            .buildingId(building.id())
            .bin("10")
            .aisle(AISLE_A)
            .column("10")
            .dataHallId(dataHall.id())
            .isActive(true)
            .name("test-" + id)
            .partId(null)
            .regionId(regionId)
            .storageRoomId(null)
            .shelf(AISLE_B)
            .type(alType)
            .build());
  }

  public List<Asset> listAssetsInRegion(String regionId) {
    return assetRepository
        .list(
            regionId,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            PageQuery.fromEmptyToken(Integer.MAX_VALUE))
        .getResults();
  }

  public Asset createAsset(final AssetCreationDetails assetDetails) {
    var defaultState =
        assetDetails.parentAssetId() == null
            ? AssetLifecycleState.AVAILABLE
            : AssetLifecycleState.INSTALLED;
    AssetCreationDetails finalDetails =
        AssetCreationDetailsBuilder.builder()
            .regionId(
                Optional.ofNullable(assetDetails.regionId())
                    .orElseGet(
                        () -> {
                          if (regionEntity == null
                              || !regionRepository.existsById(regionEntity.id())) {
                            regionEntity = createRegion();
                          }
                          return regionEntity.id();
                        }))
            .serialNumber(
                Optional.ofNullable(assetDetails.serialNumber())
                    .orElse("dcmsRepositoryTestUtil-" + UUID.randomUUID()))
            .catalogPartId(
                Optional.ofNullable(assetDetails.catalogPartId())
                    .orElseGet(
                        () -> {
                          String tempPartNumber = UUID.randomUUID().toString();
                          var createdPart = createCatalogPart(tempPartNumber, tempPartNumber).id();
                          // Add part compatibility with itself
                          createPartsCompatibility(createdPart, createdPart);
                          return createdPart;
                        }))
            .lifecycleState(Optional.ofNullable(assetDetails.lifecycleState()).orElse(defaultState))
            .subInventory(
                Optional.ofNullable(assetDetails.subInventory())
                    .orElse(AssetSubInventory.GENERAL_SPARES))
            .createdBy(Optional.ofNullable(assetDetails.createdBy()).orElse(UNIT_TEST_USER))
            .locationId(assetDetails.locationId())
            .parentAssetId(assetDetails.parentAssetId())
            .elevation(assetDetails.elevation())
            .oracleTag(assetDetails.oracleTag())
            .additionalProperties(assetDetails.additionalProperties())
            .build();

    return assetRepository.saveAsset(finalDetails);
  }

  public Asset createAssetAndAcquireReservationOrTransfer(
      AssetCreationDetails creationDetails, RmcId reservationId, RmcId transferId) {
    Preconditions.checkArgument(
        reservationId != null || transferId != null, "must supply reservationId or transferId");

    Asset created = createAsset(creationDetails);
    assetRepository.acquireAsset(
        created.regionId(),
        created.id(),
        created.version(),
        reservationId,
        transferId,
        "test-user",
        created.subInventory().toString());

    return assetRepository
        .findById(created.id())
        .orElseThrow(() -> new RuntimeException("Failed to find asset after updates"));
  }

  public void acquireAsset(String regionId, RmcId assetId, Long assetVerion, RmcId reservationId) {
    assetRepository.acquireAsset(
        regionId, assetId, assetVerion, reservationId, null, "test-user", "GENERAL_SPARES");
  }

  public Part createCatalogPart(
      String oraclePartNumber, String manufacturerPartNumber, boolean isSerializedAsset) {
    return createCatalogPart(
        oraclePartNumber, manufacturerPartNumber, isSerializedAsset, false, "manufacturerName");
  }

  public Part createCatalogPart(
      String oraclePartNumber,
      String manufacturerPartNumber,
      boolean isSerializedAsset,
      boolean isAlias,
      String manufacturerName) {
    // same logic as in the service test - if it exists, don't do anything
    List<Part> parts = partRepository.findAll();
    for (Part part : parts) {
      if (part.oraclePartNumber().equals(oraclePartNumber)
          && part.manufacturerPartNumber() != null
          && part.manufacturerPartNumber().equals(manufacturerPartNumber)) {
        return part;
      }
    }
    Part part =
        new Part(
            RmcId.generate(RmcIdType.Catalog.PART),
            oraclePartNumber,
            manufacturerPartNumber,
            "vendorName",
            manufacturerName,
            "sourceFrom",
            null,
            null,
            RmcId.generate(RmcIdType.Catalog.PART_CATEGORY),
            isSerializedAsset,
            null,
            false,
            "https://example.com",
            PartDetailsSource.RmcStub,
            isAlias,
            null,
            false);
    partRepository.save(part);
    return part;
  }

  public Part createCatalogPart(
      String oraclePartNumber,
      String manufacturerPartNumber,
      boolean isSerializedAsset,
      String manufacturerName,
      String description) {
    // same logic as in the service test - if it exists, don't do anything
    List<Part> parts = partRepository.findAll();
    for (Part part : parts) {
      if (part.oraclePartNumber().equals(oraclePartNumber)
          && part.manufacturerPartNumber().equals(manufacturerPartNumber)) {
        return part;
      }
    }
    Part part =
        new Part(
            RmcId.generate(RmcIdType.Catalog.PART),
            oraclePartNumber,
            manufacturerPartNumber,
            "vendorName",
            manufacturerName,
            "sourceFrom",
            null,
            description,
            RmcId.generate(RmcIdType.Catalog.PART_CATEGORY),
            isSerializedAsset,
            null,
            false,
            "https://example.com",
            PartDetailsSource.RmcStub,
            false,
            null,
            false);
    partRepository.save(part);
    return part;
  }

  public Part createCatalogPart(String oraclePartNumber, String manufacturerPartNumber) {
    return createCatalogPart(
        oraclePartNumber, manufacturerPartNumber, true, false, "manufacturerName");
  }

  public Part createCatalogPart(
      String oraclePartNumber, String manufacturerPartNumber, String description) {
    return createCatalogPart(
        oraclePartNumber, manufacturerPartNumber, true, "manufacturerName", description);
  }

  public PartsCompatibility createPartsCompatibility(RmcId partId, RmcId compatiblePartId) {
    List<PartsCompatibility> partsCompatibilities = partsCompatibilityRepository.findAll();
    for (PartsCompatibility partsCompatibility : partsCompatibilities) {
      if (partsCompatibility.partId().equals(partId)
          && partsCompatibility.compatiblePartId().equals(compatiblePartId)) {
        return partsCompatibility;
      }
    }
    PartsCompatibility partsCompatibility =
        new PartsCompatibility(
            RmcId.generate(RmcIdType.Catalog.PARTS_COMPATIBILITY), partId, compatiblePartId);
    partsCompatibilityRepository.save(partsCompatibility);
    return partsCompatibility;
  }

  public RoomEntity createRoom(DataHall dataHall, String name) {
    return roomRepository.save(
        new RoomEntity(
            RmcId.generate(RmcIdType.Inventory.ROOM),
            name,
            "Description Room 1",
            "provider Room Name 1",
            1,
            dataHall.id()));
  }

  public RoomEntity createRoom(DataHall dataHall, int idx) {
    RmcId dataHallId = dataHall.id();
    return roomRepository.save(
        new RoomEntity(
            RmcId.generate(RmcIdType.Inventory.ROOM),
            dataHall.name() + "-room-" + idx,
            "Room description",
            "Room providerRoomName",
            idx,
            dataHallId));
  }

  public RackInstance getRackInstanceById(RmcId rackInstanceId) {
    return rackInstanceRepository.findById(rackInstanceId).orElseThrow();
  }

  public RackInstanceDetails createRackInstance(
      RegionEntity region, Asset rackAsset, RackModel rackModel) {
    RackInstance instance =
        rackInstanceRepository.save(
            new RackInstance(
                RmcId.generate(RmcIdType.DCMS.RACK_INSTANCE),
                region.airportCode(),
                rackAsset.id(),
                rackModel.id(),
                RackInstanceAccessLevel.REGULAR));
    if (AssetLifecycleState.IN_TRANSIT.name().equals(rackAsset.lifecycleState().name())) {
      assetService.setRackInstanceOnAsset(region.airportCode(), rackAsset.id(), instance.id());
    }
    rackRepository.update(
        rackModel.with().lifecycleState(RackModelLifecycleState.INSTANCE_ASSIGNED).build());
    return rackInstanceDetailsRepository.findById(instance.id()).orElseThrow();
  }

  public RackInstance createRackInstance(
      String region, String serialNumber, String manufacturerName, RmcId rackModelId) {

    Asset rackAsset =
        createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(regionEntity.id())
                .catalogPartId(
                    createCatalogPart(
                            "partnumber" + manufacturerName,
                            "partnumber",
                            true,
                            false,
                            manufacturerName)
                        .id())
                .serialNumber(serialNumber)
                .build());
    RackInstance instance =
        rackInstanceRepository.save(
            new RackInstance(
                RmcId.generate(RmcIdType.DCMS.RACK_INSTANCE),
                region,
                rackAsset.id(),
                rackModelId,
                RackInstanceAccessLevel.REGULAR));
    if (AssetLifecycleState.IN_TRANSIT.name().equals(rackAsset.lifecycleState().name())) {
      assetService.setRackInstanceOnAsset(region, rackAsset.id(), instance.id());
    }
    return instance;
  }

  public RackInstance createRackInstance(
      String serialNumber, String manufacturerName, RmcId rackModelId) {

    Asset rackAsset =
        createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(regionEntity.id())
                .catalogPartId(
                    createCatalogPart(
                            "partnumber" + manufacturerName,
                            "partnumber",
                            true,
                            false,
                            manufacturerName)
                        .id())
                .serialNumber(serialNumber)
                .build());
    RackInstance instance =
        rackInstanceRepository.save(
            new RackInstance(
                RmcId.generate(RmcIdType.DCMS.RACK_INSTANCE),
                region,
                rackAsset.id(),
                rackModelId,
                RackInstanceAccessLevel.REGULAR));
    if (AssetLifecycleState.IN_TRANSIT.name().equals(rackAsset.lifecycleState().name())) {
      assetService.setRackInstanceOnAsset(region, rackAsset.id(), instance.id());
    }
    Optional<RackModel> optionalRack = rackRepository.findById(rackModelId);
    optionalRack.ifPresent(
        rackModel ->
            rackRepository.update(
                rackModel
                    .with()
                    .lifecycleState(RackModelLifecycleState.INSTANCE_ASSIGNED)
                    .build()));
    return instance;
  }

  public RackInstance createRackInstance(String region, RmcId assetId, RmcId rackModelId) {
    RackInstance instance =
        rackInstanceRepository.save(
            new RackInstance(
                RmcId.generate(RmcIdType.DCMS.RACK_INSTANCE),
                region,
                assetId,
                rackModelId,
                RackInstanceAccessLevel.REGULAR));
    Asset rackAsset = assetService.getAsset(region, assetId.toString());
    if (AssetLifecycleState.IN_TRANSIT.name().equals(rackAsset.lifecycleState().name())) {
      assetService.setRackInstanceOnAsset(region, rackAsset.id(), instance.id());
    }
    Optional<RackModel> optionalRack = rackRepository.findById(rackModelId);
    optionalRack.ifPresent(
        rackModel ->
            rackRepository.update(
                rackModel
                    .with()
                    .lifecycleState(RackModelLifecycleState.INSTANCE_ASSIGNED)
                    .build()));
    return instance;
  }

  public RackInstance createRackInstance(
      String region,
      RmcId assetId,
      RmcId rackModelId,
      RackInstanceLifecycleState state,
      RackInstanceLifecycleSubState subState) {
    return rackInstanceRepository.save(
        RackInstanceBuilder.builder()
            .id(RmcId.generate(RmcIdType.DCMS.RACK_INSTANCE))
            .region(region)
            .assetId(assetId)
            .rackModelId(rackModelId)
            .lifecycleState(state)
            .lifecycleSubState(subState)
            .build());
  }

  public void associateAssetToRackInstance(String regionId, RmcId assetId, RmcId rackInstanceId) {
    assetService.installAssetInRack(regionId, assetId, rackInstanceId, "0", null);
  }

  public RealmEntity createRealm(String name) {
    return realmRepository.save(new RealmEntity(name));
  }

  public MarketEntity createMarket(String name) {
    return marketRepository.save(new MarketEntity(name));
  }

  public AssetReservation createAssetReservation(String regionId, RmcId catalogPartId) {
    AssetLocation location = createAssetLocation(regionId, AISLE_B);
    return assetReservationRepository.save(
        new AssetReservation(
            RmcId.generate(RmcIdType.Inventory.ASSET_RESERVATION),
            regionId,
            location.id(),
            RmcId.generate(RmcIdType.CM.CHANGE_ORDER),
            catalogPartId,
            null,
            null,
            null,
            null,
            AssetReservationLifecycleState.ACTIVE,
            AssetSubInventory.GENERAL_SPARES));
  }

  public AssetReservation createAssetReservation(
      String regionId, RmcId locationId, RmcId catalogPartId) {
    return assetReservationRepository.save(
        new AssetReservation(
            RmcId.generate(RmcIdType.Inventory.ASSET_RESERVATION),
            regionId,
            locationId,
            RmcId.generate(RmcIdType.CM.CHANGE_ORDER),
            catalogPartId,
            null,
            null,
            null,
            null,
            AssetReservationLifecycleState.ACTIVE,
            AssetSubInventory.GENERAL_SPARES));
  }

  public AssetReservation saveAssetReservation(AssetReservation reservation) {
    return assetReservationRepository.save(reservation);
  }

  public AssetReservation getAssetReservation(RmcId reservationId) {
    return assetReservationRepository.findById(reservationId).orElseThrow();
  }

  public Asset getAsset(RmcId assetId) {
    return assetRepository.findById(assetId).orElseThrow();
  }

  public void updateAssetLifecycleState(Asset asset, Long version, AssetLifecycleState newState) {
    assetRepository.updateAsset(
        asset.id(),
        asset.regionId(),
        AssetUpdateDetailsBuilder.builder()
            .lifecycleState(newState)
            .updatedBy("DcmsRepositoryTestUtil.UpdateAssetLifecycleState")
            .build(),
        version);
  }

  public void acquireAssetTransfer(RmcId transferId, RmcId assetId) {
    assetTransferRepository.acquireAssetTransfer(assetId, null, transferId, UNIT_TEST_USER);
  }

  public AssetTransfer createAssetTransfer(String regionId, RmcId assetId) {
    AssetLocation dstLocation = createAssetLocation(regionId, AISLE_B);

    return assetTransferRepository
        .findById(
            assetTransferRepository.createAssetTransfer(
                regionId,
                dstLocation.id(),
                assetId,
                null,
                RmcId.generate(RmcIdType.CM.CHANGE_ORDER),
                null,
                UNIT_TEST_USER,
                AssetSubInventory.GENERAL_SPARES.toString()))
        .get();
  }

  public Building createBuilding() {
    RmcId id = RmcId.generate(RmcIdType.Inventory.BUILDING);
    var building =
        buildingRepository.save(
            new Building(
                id,
                "test-building-" + id,
                new Addresses(List.of("x", "y")),
                new Coordinates(1.5F, 2.5F),
                "test-building-" + id,
                PhysicalEntityLifecycleState.ACTIVE));
    shortRackIdCounterRepository.save(new ShortRackIdCounter(building.id()));
    return building;
  }

  public Transceiver createTransceiver(PluggablePort pluggablePort, String transceiverSku) {
    Transceiver transceiver =
        transceiverRepository.save(
            new Transceiver(
                RmcId.generate(RmcIdType.DCMS.TRANSCEIVER),
                regionEntity.airportCode(),
                transceiverSku));
    pluggablePortRepository.update(pluggablePort.withTransceiverId(transceiver.id()));
    return transceiver;
  }

  public PluggablePort createPluggablePort(
      String displayName, int portNumber, DevicePortType devicePortType, DeviceModel deviceModel) {

    return pluggablePortRepository.save(
        new PluggablePort(
            RmcId.generate(RmcIdType.DCMS.PLUGGABLE_PORT),
            regionEntity.airportCode(),
            displayName,
            portNumber,
            devicePortType,
            deviceModel.id(),
            null,
            null,
            null));
  }

  public PluggablePort createPassiveDevicePluggablePorts(
      String displayName,
      String cutsheetDisplayName,
      int portNumber,
      DevicePortType devicePortType,
      DeviceModel deviceModel) {
    PluggablePort pluggablePort =
        new PluggablePort(
            RmcId.generate(RmcIdType.DCMS.PLUGGABLE_PORT),
            regionEntity.airportCode(),
            displayName,
            portNumber,
            devicePortType,
            deviceModel.id(),
            null,
            null,
            null);

    return pluggablePortRepository.save(pluggablePort.withCutsheetDisplayName(cutsheetDisplayName));
  }

  @Transactional
  public NetworkCable createNetworkCable(
      String cableSku, NetworkCableType cableType, String portLabel, String trunkLabel) {
    return networkCableRepository.save(
        new NetworkCable(
            RmcId.generate(RmcIdType.DCMS.NETWORK_CABLE),
            regionEntity.airportCode(),
            cableSku,
            cableType,
            portLabel,
            trunkLabel,
            null,
            ConnectionSource.NODE_GROUP,
            null,
            null,
            null));
  }

  public NetworkCableLeg createNetworkCableLeg(RmcId fromPortId, RmcId toPortId, RmcId cableId) {
    return createNetworkCableLeg(fromPortId, toPortId, cableId, null, null);
  }

  public NetworkCableLeg createNetworkCableLeg(
      RmcId fromPortId, RmcId toPortId, RmcId cableId, String fromColor, String toColor) {

    pluggablePortRepository.updateLifecycleState(
        regionEntity.airportCode(), List.of(fromPortId, toPortId), PortLifecycleState.USED);

    return networkCableLegRepository.save(
        new NetworkCableLeg(
            fromPortId, toPortId, regionEntity.airportCode(), cableId, fromColor, toColor));
  }

  @Transactional
  public void removeNetworkCableLeg(NetworkCableLeg networkCableLeg) {
    pluggablePortRepository.updateLifecycleState(
        regionEntity.airportCode(),
        List.of(networkCableLeg.id().fromPluggablePortId()),
        PortLifecycleState.AVAILABLE);
    pluggablePortRepository.updateLifecycleState(
        regionEntity.airportCode(),
        List.of(networkCableLeg.id().toPluggablePortId()),
        PortLifecycleState.AVAILABLE);
    networkCableLegRepository.delete(networkCableLeg);
  }

  public PluggablePort createPluggablePortForTransceiver(
      String displayName, int portNumber, Transceiver transceiver, PluggablePort pluggablePort) {
    return pluggablePortRepository.save(
        new PluggablePort(
                RmcId.generate(RmcIdType.DCMS.PLUGGABLE_PORT),
                regionEntity.airportCode(),
                displayName,
                portNumber,
                DevicePortType.MPO,
                pluggablePort.deviceModelId(),
                pluggablePort.id(),
                null,
                null)
            // inherit view from parent
            .withDeviceView(pluggablePort.deviceView()));
  }

  @Transactional
  public PluggablePort createPluggablePortForFrontPatchPanel(
      RmcId deviceModelId, DeviceView view, String displayName, Integer portNumber) {

    RmcId id = RmcId.generate(RmcIdType.DCMS.PLUGGABLE_PORT);
    var port =
        pluggablePortRepository.save(
            new PluggablePort(
                id,
                regionEntity.airportCode(),
                displayName,
                portNumber,
                DevicePortType.BASE_T_1G,
                deviceModelId,
                null,
                null,
                view));
    return port;
  }

  @Transactional
  public PluggablePort createPluggablePortForRearPatchPanel(
      PluggablePort frontPatchPanelPort, String displayName) {
    RmcId id = RmcId.generate(RmcIdType.DCMS.PLUGGABLE_PORT);
    PluggablePort port = frontPatchPanelPort.toPatchPanelRearPort(id, displayName);
    return pluggablePortRepository.save(port);
  }

  public DeviceModel createPatchPanel(String elevation, String displayName, RmcId rackModelId) {
    return deviceModelRepository.save(
        new DeviceModel(
            RmcId.generate(RmcIdType.DCMS.DEVICE_MODEL),
            getRegionEntity().airportCode(),
            displayName,
            "devicePlatformName",
            "PatchPanelRole",
            "patchPanelModel",
            "PatchPanelVendorName",
            "ppDeviceOpn",
            2,
            elevation,
            null,
            new DeviceAttributes(
                DeviceType.PASSIVE,
                null,
                new PassiveDeviceAttributes(
                    6,
                    6,
                    6,
                    List.of("A", "B", "C", "D", "E", "F"),
                    List.of("1", "2", "3", "4", "5", "6"),
                    List.of("FRONT", "REAR"),
                    72,
                    null)),
            DeviceType.PASSIVE,
            rackModelId,
            1));
  }

  public DeviceInstance createDeviceInstance(
      RegionEntity region, RackInstance inventoryRack, DeviceModel deviceModel) {
    var inventoryDevice =
        new DeviceInstance(
            RmcId.generate(RmcIdType.DCMS.DEVICE_INSTANCE),
            region.airportCode(),
            inventoryRack.assetId(),
            deviceModel.id(),
            inventoryRack.id(),
            DeviceInstanceLifecycleState.SHIPPING);
    return deviceInstanceRepository.save(inventoryDevice);
  }

  public DeviceInstance createDeviceInstance(
      RackInstance inventoryRack, DeviceModel deviceModel, String deviceSerial) {
    Asset deviceAsset =
        createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(regionEntity.id())
                .catalogPartId(
                    createCatalogPart("opn1234", "partnumber", true, false, "manufacturerName")
                        .id())
                .serialNumber(deviceSerial)
                .build());

    var inventoryDevice =
        new DeviceInstance(
            RmcId.generate(RmcIdType.DCMS.DEVICE_INSTANCE),
            region,
            deviceAsset.id(),
            deviceModel.id(),
            inventoryRack.id(),
            DeviceInstanceLifecycleState.SHIPPING);
    return deviceInstanceRepository.save(inventoryDevice);
  }

  public DeviceInstance createDeviceInstance(
      RegionEntity region,
      RackInstanceDetails rackInstanceDetails,
      Asset deviceAsset,
      DeviceModel deviceModel) {
    var inventoryDevice =
        new DeviceInstance(
            RmcId.generate(RmcIdType.DCMS.DEVICE_INSTANCE),
            region.airportCode(),
            deviceAsset.id(),
            deviceModel.id(),
            RmcId.fromString(rackInstanceDetails.id()),
            DeviceInstanceLifecycleState.SHIPPING,
            0L,
            null,
            null);
    return deviceInstanceRepository.save(inventoryDevice);
  }

  public DeviceInstance updateDeviceInstance(
      DeviceInstanceDetails deviceInstance, DeviceInstanceLifecycleState lifecycleState) {
    var updated =
        DeviceInstanceBuilder.builder()
            .id(RmcId.fromString(deviceInstance.id()))
            .region(deviceInstance.region())
            .assetId(RmcId.fromString(deviceInstance.assetId()))
            .deviceModelId(RmcId.fromString(deviceInstance.deviceModelId()))
            .rackInstanceId(RmcId.fromString(deviceInstance.rackInstanceId()))
            .lifecycleState(lifecycleState)
            .version(deviceInstance.version())
            .timeUpdated(deviceInstance.timeUpdated())
            .timeCreated(deviceInstance.timeCreated())
            .build();
    deviceInstanceRepository.update(updated);
    return updated;
  }

  public List<DeviceInstance> getDeviceInstances(String region, RmcId rackInstanceId) {
    return deviceInstanceRepository.findByRegionAndRackInstanceId(region, rackInstanceId);
  }

  public Map<String, Object> setupMockInventoryData() {
    createRackParentEntities();
    var region = getRegionEntity();

    // Create data halls
    var dataHall1 = createDataHall("DH1");
    var dataHall2 = createDataHall("DH2");

    // Create Rooms
    var room1 = createRoom(dataHall1, "room1");
    var room2 = createRoom(dataHall2, "room2");

    // Create RackPositions
    var rackPosition1 = createRackPosition("001", room1, 0, 1);
    var rackPosition2 = createRackPosition("002", room2, 0, 2);

    // Create Part Categories
    var partCategory1 = createPartCategory("GPU Server/GB200");
    var partCategory2 = createPartCategory("NVLink NVSwitches");

    // Create Parts
    var part1 = createPart("rackOPN1", "rackMPN1", "manufacturer", partCategory1.id());
    var part2 = createPart("rackOPN2", null, null, partCategory2.id());
    createPart("rackOPN2", "rackMPN2", "manufacturer2", partCategory2.id());
    createPart("rackOPN2", "rackMPN3", "manufacturer3", partCategory2.id());

    // Create RackAssets
    var rackAsset1 =
        createAsset(
            AssetCreationDetailsBuilder.builder()
                .catalogPartId(part1.id())
                .serialNumber("rack-fake-serial-number-1")
                .build());
    var rackAsset2 =
        createAsset(
            AssetCreationDetailsBuilder.builder()
                .catalogPartId(part2.id())
                .serialNumber("rackSerial2")
                .build());

    // Create RackModels
    var rackModel1 = createRackAtPosition(rackPosition1);
    var rackModel2 = createRackAtPosition(rackPosition2);

    // Create RackInstances
    var rackInstance1 = createRackInstance(region, rackAsset1, rackModel1);
    associateAssetToRackInstance(
        region.id(), rackAsset1.id(), RmcId.fromString(rackInstance1.id()));
    var rackInstance2 = createRackInstance(region, rackAsset2, rackModel2);
    associateAssetToRackInstance(
        region.id(), rackAsset2.id(), RmcId.fromString(rackInstance2.id()));

    var deviceModel1 = createDeviceInRack(rackModel1);
    var deviceModel2 = createDeviceInRack(rackModel2, "nv_link_network_switch");

    var deviceAsset1 =
        createAsset(
            AssetCreationDetailsBuilder.builder()
                .catalogPartId(part1.id())
                .serialNumber("device-fake-serial-number-1")
                .parentAssetId(rackAsset1.id())
                .build());

    var deviceAsset2 =
        createAsset(
            AssetCreationDetailsBuilder.builder()
                .catalogPartId(part2.id())
                .serialNumber("device-fake-serial-number-2")
                .parentAssetId(rackAsset2.id())
                .build());

    var deviceInstance1 = createDeviceInstance(region, rackInstance1, deviceAsset1, deviceModel1);
    var deviceInstance2 = createDeviceInstance(region, rackInstance2, deviceAsset2, deviceModel2);

    createCatalogPart(rackAsset1.serialNumber(), null);
    createCatalogPart(rackAsset2.serialNumber(), null);

    var expectedLocation1 =
        new DeviceLocation(
            deviceModel1.id(),
            deviceAsset1.serialNumber(),
            dataHall1.name(),
            room1.name(),
            rackPosition1.floorRow().toString(),
            rackPosition1.floorPosition().toString(),
            deviceModel1.elevation(),
            rackAsset1.serialNumber(),
            deviceModel1.hostName(),
            deviceModel1.lifecycleState(),
            deviceModel1.model(),
            deviceModel1.devicePlatform(),
            deviceModel1.vendorName(),
            deviceModel1.role(),
            deviceModel1.height(),
            deviceInstance1.id().toString(),
            room1.id().toString(),
            part1.oraclePartNumber(),
            part1.manufacturerPartNumber(),
            part1.manufacturerName(),
            part1.description());
    var expectedLocation2 =
        new DeviceLocation(
            deviceModel2.id(),
            deviceAsset2.serialNumber(),
            dataHall2.name(),
            room2.name(),
            rackPosition2.floorRow().toString(),
            rackPosition2.floorPosition().toString(),
            deviceModel2.elevation(),
            rackAsset2.serialNumber(),
            deviceModel2.hostName(),
            deviceModel2.lifecycleState(),
            deviceModel2.model(),
            deviceModel2.devicePlatform(),
            deviceModel2.vendorName(),
            deviceModel2.role(),
            deviceModel2.height(),
            deviceInstance2.id().toString(),
            room2.id().toString(),
            part2.oraclePartNumber(),
            part2.manufacturerPartNumber(),
            part2.manufacturerName(),
            part2.description());
    return Map.of(
        "modelIds", List.of(deviceInstance1.deviceModelId(), deviceInstance2.deviceModelId()),
        "dataHallIds", List.of(dataHall1.id(), dataHall2.id()),
        "roomIds", List.of(room1.id(), room2.id()),
        "expectedLocations", List.of(expectedLocation1, expectedLocation2),
        "deviceInstanceIds", List.of(deviceInstance1.id(), deviceInstance2.id()),
        "expectedMpns",
            Map.of(
                expectedLocation1.id(),
                List.of(),
                expectedLocation2.id(),
                List.of("rackMPN2", "rackMPN3")),
        "expectedManufacturers",
            Map.of(
                expectedLocation1.id(),
                List.of(),
                expectedLocation2.id(),
                List.of("manufacturer2", "manufacturer3")));
  }

  public AssetBundle createAssetBundle(
      String regionId, RmcId locationId, Integer quantity, RmcId partId) {
    return assetBundleRepository.save(
        new AssetBundle(
            RmcId.generate(RmcIdType.Inventory.ASSET_BUNDLE),
            regionId,
            locationId,
            partId,
            quantity,
            0,
            AssetLifecycleState.AVAILABLE,
            AssetSubInventory.GENERAL_SPARES));
  }

  public AssetBundle getAssetBundleById(RmcId assetBundleId) {
    return assetBundleRepository.findById(assetBundleId).orElseThrow();
  }

  public AssetBundle updateAssetBundle(AssetBundle assetBundle) {
    return assetBundleRepository.update(assetBundle);
  }

  public List<AssetBundle> listAssetBundleByLocationMpnAndOpn(
      RmcId locationId, String opn, String mpn) {
    return assetBundleRepository
        .listAssetBundle(locationId, opn, mpn, null, null, PageQuery.fromEmptyToken(10))
        .getResults();
  }

  public PartCategory createPartCategory(String name) {
    PartCategory partCategory =
        new PartCategory(RmcId.generate(RmcIdType.Catalog.PART_CATEGORY), name);

    return partCategoryRepository.save(partCategory);
  }

  public Part createPart(
      String oraclePartNumber, String manufacturePartNumber, RmcId partCategoryId) {

    Part part =
        new Part(
            RmcId.generate(RmcIdType.Catalog.PART),
            oraclePartNumber,
            manufacturePartNumber,
            "vendorName",
            "manufacturer",
            "sourceFrom",
            null,
            null,
            partCategoryId,
            null,
            null,
            null,
            "www.CatalogSourceURL.com",
            PartDetailsSource.RmcStub,
            false,
            null,
            null);

    return partRepository.save(part);
  }

  public Part createPart(
      String oraclePartNumber,
      String manufacturePartNumber,
      String manufacturerName,
      RmcId partCategoryId) {

    Part part =
        new Part(
            RmcId.generate(RmcIdType.Catalog.PART),
            oraclePartNumber,
            manufacturePartNumber,
            "vendorName",
            manufacturerName,
            "sourceFrom",
            null,
            null,
            partCategoryId,
            null,
            null,
            null,
            "www.CatalogSourceURL.com",
            PartDetailsSource.RmcStub,
            false,
            null,
            false);

    return partRepository.save(part);
  }

  public Part getPart(RmcId catalogPartId) {
    return partRepository.findById(catalogPartId).orElseThrow();
  }

  public AssetLocation createInactiveAssetLocation(String regionId) {
    RmcId id = RmcId.generate(RmcIdType.Inventory.ASSET_LOCATION);

    var building = this.building != null ? this.building : createBuilding();
    var dataHall =
        this.dataHall != null
            ? this.dataHall
            : createDataHall(UUID.randomUUID().toString(), building.id(), "1");

    return assetLocationRepository.saveAssetLocation(
        AssetLocationCreationDetailsBuilder.builder()
            .createdBy(UNIT_TEST_USER)
            .buildingId(building.id())
            .bin("10")
            .aisle(AISLE_A)
            .column("10")
            .dataHallId(dataHall.id())
            .isActive(true)
            .name("test-" + id)
            .partId(null)
            .regionId(regionId)
            .storageRoomId(null)
            .shelf("S")
            .type(AssetLocationType.PALLET)
            .build());
  }

  public ExternalPeerEntity createExternalPeer(
      String displayName, String externalPartnerName, ExternalPeerType externalPeerType) {
    return externalPeerRepository.save(
        ExternalPeerEntityBuilder.builder()
            .id(RmcId.generate(RmcIdType.DCMS.EXTERNAL_PEER))
            .displayName(displayName)
            .externalPartnerName(externalPartnerName)
            .siteId(siteEntity.regionalId().id())
            .region(region)
            .type(externalPeerType)
            .build());
  }

  public Optional<PurchaseOrderLine> createPurchaseOrderLine(
      RmcId poId, String partId, String partNumber, String manufacturerPartNumber, String status) {
    RmcId rmcId;
    rmcId =
        purchaseOrderLineRepository
            .savePurchaseOrderLine(
                new PurchaseOrderLineCreateDetails(
                    poId,
                    partId,
                    partNumber,
                    manufacturerPartNumber,
                    0L,
                    status,
                    null,
                    LocalDateTime.now(),
                    RANDOM.nextLong()))
            .id();

    return purchaseOrderLineRepository.findById(rmcId);
  }

  public Optional<PurchaseOrder> createPurchaseOrder(
      String orderRefNumber, String type, String shipToSite, String status) {

    RmcId rmcId;
    rmcId =
        purchaseOrderRepository
            .savePurchaseOrder(
                new PurchaseOrderCreateDetails(
                    orderRefNumber,
                    123,
                    shipToSite,
                    null,
                    "123",
                    PurchaseOrderStatusType.valueOf(status),
                    PurchaseOrderType.valueOf(type),
                    null,
                    Instant.now(),
                    0L,
                    null,
                    null))
            .id();

    return purchaseOrderRepository.findById(rmcId);
  }

  public Optional<PurchaseOrderItem> createPurchaseOrderItem(
      String lineId,
      String status,
      String receivedBy,
      String nonConformanceReason,
      String subInventory,
      String receivedSerialNumber) {

    RmcId rmcId;
    rmcId =
        purchaseOrderItemRepository
            .savePurchaseOrderItem(
                new PurchaseOrderItemCreateDetails(
                    RmcId.fromString(lineId),
                    RANDOM.nextLong(),
                    null,
                    receivedSerialNumber,
                    PurchaseOrderItemStatusType.valueOf(status),
                    null,
                    receivedBy,
                    PurchaseOrderItemNonConformanceReasonType.valueOf(nonConformanceReason),
                    null,
                    0,
                    null,
                    PurchaseOrderItemSubInventoryType.valueOf(subInventory),
                    0L,
                    null,
                    null))
            .id();

    return purchaseOrderItemRepository.findById(rmcId);
  }

  public RackPlatform createRackPlatform(String name, String skPlatformName) {
    return rackPlatformRepository.save(
        new RackPlatform(
            RmcId.generate(RmcIdType.Catalog.RACK_PLATFORM),
            name,
            "model",
            "role",
            "rackProfileId",
            skPlatformName,
            List.of(),
            List.of(),
            List.of(),
            "testOraclePartNumber"));
  }

  public void updateRackDeviceInstanceState(
      RmcId rackInstanceId, RackInstanceLifecycleState state, Long version) {
    rackInstanceRepository.updateState(rackInstanceId, state, version);
  }

  public void addCable(String cableName) {
    cableRepository.save(
        new Cable(RmcId.generate(RmcIdType.Catalog.CABLE), "Copper RJ45 cable", "sku", "red"));
  }

  public DeviceModel buildLogicalAndPhysicalPorts(
      DeviceModel deviceModel,
      List<Integer> idx,
      String portgroup,
      String portNamePrefix,
      DevicePortType devicePortType) {
    List<PortEntity> logicalPorts = new ArrayList<>();
    List<PluggablePort> pluggablePorts = new ArrayList<>();
    for (int i : idx) {
      PortEntity logicalPort =
          createPort(deviceModel, i, portgroup, portNamePrefix, devicePortType);
      PluggablePort pluggablePort =
          createPluggablePort(portNamePrefix + i, i, devicePortType, deviceModel);
      logicalPorts.add(logicalPort.withPluggablePortId(pluggablePort.id()));
      pluggablePorts.add(pluggablePort);
    }
    return deviceModel.with().portChannels(logicalPorts).pluggablePorts(pluggablePorts).build();
  }

  public void hardDeleteAllRackInstances() {
    deviceInstanceRepository.hardDeleteAllDeviceInstances();
    rackInstanceRepository.hardDeleteAllRackInstances();
  }

  public ChangeOrder createAndGetChangeOrder(
      RmcId assetId, String serialNumber, ChangeOrderState state, String airportCode) {
    // set up change orders
    RmcId changeOrderId = RmcId.generate(RmcIdType.CM.CHANGE_ORDER);
    ChangeOrderSourceType createdBy = ChangeOrderSourceType.BURNINATOR;
    ChangeOrder changeOrder =
        ChangeOrder.id(changeOrderId)
            .labels("label1", "label2")
            .targetAssetId(assetId)
            .serialNumber(serialNumber)
            .type(ChangeOrderType.RACK_INGESTION)
            .details(new ChangeOrderDetails(null, null, null, null))
            .priority(2)
            .severity(2)
            .state(state)
            .source(createdBy)
            .isSharedWithCustomer(true)
            .compartmentId("testCompartmentId")
            .region(airportCode)
            .build();
    return changeOrderRepository.save(changeOrder);
  }

  public void createChangeTask(
      RmcId changeTaskId,
      String name,
      RmcId changeOrderId,
      ChangeTaskState state,
      Instant timeCompleted) {
    changeTaskRepository.save(
        new ChangeTask(
            changeTaskId,
            changeOrderId,
            name,
            "details",
            state,
            "runbook",
            null,
            "operatorEmail",
            "probeType",
            0L,
            Instant.now(),
            Instant.now(),
            timeCompleted,
            Instant.now()));
  }
}
