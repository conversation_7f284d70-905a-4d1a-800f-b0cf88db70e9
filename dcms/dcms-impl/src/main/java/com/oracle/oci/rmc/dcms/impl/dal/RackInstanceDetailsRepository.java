package com.oracle.oci.rmc.dcms.impl.dal;

import com.oracle.oci.rmc.dal.jdbc.DataSourceConstants;
import com.oracle.oci.rmc.dal.jdbc.H2Repository;
import com.oracle.oci.rmc.dal.jdbc.OracleRepository;
import com.oracle.oci.rmc.dcms.api.model.common.RackInstanceLifecycleState;
import com.oracle.oci.rmc.dcms.api.model.common.RackInstanceLifecycleSubState;
import com.oracle.oci.rmc.dcms.api.model.entity.RackInstanceDetails;
import com.oracle.oci.rmc.model.RmcId;
import com.oracle.oci.sfw.micronaut.http.pagination.PaginatedList;
import com.oracle.oci.sfw.micronaut.http.pagination.TokenPageQuery;
import io.micronaut.core.annotation.NonNull;
import io.micronaut.core.annotation.Nullable;
import io.micronaut.data.annotation.Query;
import io.micronaut.data.repository.GenericRepository;
import java.util.List;
import java.util.Optional;

public interface RackInstanceDetailsRepository
    extends GenericRepository<RackInstanceDetails, RmcId> {

  String COMMON_QUERY_FOR_DETAILS =
      """
          SELECT rack_instance_details_.*,
          da.serial_number as serial_number,
          cp.manufacturer_name as manufacturer_name,
          rm.rack_platform as rack_platform_name
          FROM dcms_rack_instance rack_instance_details_
          LEFT JOIN dcms_rack_model rm ON rack_instance_details_.rack_model_id = rm.id
          LEFT JOIN dcms_asset da ON rack_instance_details_.asset_id = da.id
          LEFT JOIN cat_part cp ON da.catalog_part_id = cp.id
          """;

  String LIST_FILTERS_RACK_INSTANCE =
      """
          WHERE rack_instance_details_.region = :region AND
            ((rack_instance_details_.lifecycle_state = :lifecycleState) OR (:lifecycleState IS NULL AND (rack_instance_details_.lifecycle_state not in ('DELETED')))) AND
            (rack_instance_details_.lifecycle_sub_state = :lifecycleSubState OR :lifecycleSubState IS NULL) AND
            (UPPER(da.serial_number) = UPPER(:serialNumber) OR :serialNumber IS NULL) AND
            (rack_instance_details_.rack_model_id = :rackModelId OR :rackModelId IS NULL) AND
            (cp.manufacturer_name = :manufacturerName OR :manufacturerName IS NULL) AND
            (rm.rack_platform = :rackPlatform OR :rackPlatform IS NULL)
          """;

  @H2Repository
  interface RackInstanceDetailsRepositoryH2 extends RackInstanceDetailsRepository {}

  @OracleRepository(dataSource = DataSourceConstants.DEFAULT)
  interface RackInstanceDetailsRepositoryOracle extends RackInstanceDetailsRepository {}

  @Query(COMMON_QUERY_FOR_DETAILS + LIST_FILTERS_RACK_INSTANCE)
  PaginatedList<RackInstanceDetails> listWithOptionalFilters(
      @NonNull String region,
      @Nullable RackInstanceLifecycleState lifecycleState,
      @Nullable RackInstanceLifecycleSubState lifecycleSubState,
      @Nullable String serialNumber,
      @Nullable String manufacturerName,
      @Nullable RmcId rackModelId,
      @Nullable String rackPlatform,
      @Nullable TokenPageQuery pageQuery);

  @Query(
      COMMON_QUERY_FOR_DETAILS
          + " WHERE rack_instance_details_.id in (:idList) AND rack_instance_details_.region ="
          + " :region")
  List<RackInstanceDetails> findDetailsByIdInList(List<RmcId> idList, String region);

  @Query(
      COMMON_QUERY_FOR_DETAILS
          + " WHERE rack_instance_details_.id = :rackInstanceId AND rack_instance_details_.region ="
          + " :region")
  Optional<RackInstanceDetails> findByIdAndRegion(RmcId rackInstanceId, String region);

  @Query(COMMON_QUERY_FOR_DETAILS + " WHERE rack_instance_details_.id = :rackInstanceId")
  Optional<RackInstanceDetails> findById(RmcId rackInstanceId);

  @Query(
      COMMON_QUERY_FOR_DETAILS
          + " WHERE rack_instance_details_.region = :region AND"
          + " UPPER(rack_instance_details_.lifecycle_state) not in ('DELETED') AND"
          + " rack_instance_details_.rack_model_id = :rackModelId")
  List<RackInstanceDetails> findRackInstancesByRackModel(
      @NonNull String region, @NonNull RmcId rackModelId);

  @Query(
      COMMON_QUERY_FOR_DETAILS
          + "WHERE  UPPER(rack_instance_details_.lifecycle_state) ="
          + " UPPER(:rackInstanceLifecycleState)")
  List<RackInstanceDetails> findRackInstancesByLifecycleState(
      @NonNull String rackInstanceLifecycleState);

  // Get instances where lifecycle_state INSTALLING and lifecycle_sub_state is null or in 'STARTED'
  // 'NETWORK_READY' state for
  // installing racks
  // And get instances where lifecycle_state IN_SERVICE and lifecycle_sub_state is in
  // 'SINGLE_RACK_TESTED','VALIDATED' for In-service racks but still not hand over
  // ready
  @Query(
      COMMON_QUERY_FOR_DETAILS
          + " WHERE (UPPER(rack_instance_details_.lifecycle_state) = 'INSTALLING' AND"
          + " UPPER(rack_instance_details_.lifecycle_state) not in ('DELETED') AND "
          + " (rack_instance_details_.lifecycle_sub_state is NULL OR"
          + " UPPER(rack_instance_details_.lifecycle_sub_state) in('STARTED','NETWORK_READY'))) OR"
          + " (UPPER(rack_instance_details_.lifecycle_state) = 'IN_SERVICE' AND"
          + " UPPER(rack_instance_details_.lifecycle_sub_state) in"
          + " ('SINGLE_RACK_TESTED','VALIDATED'))")
  List<RackInstanceDetails> findPendingAttestationsRackInstances();
}
