elevationMappings:
  # DHA Spine Pod 1 Passive Fiber Rack
  1457:
    - id: 1
      description: Data Hall E
      source: { elevation: "39", slots: [1A,1B,1C,1D,1E,1F,1G,1H,1I,1J,1K,1L], portType: "LC",  portIndexes: "13:25" }
      destination: { rack: "0818", elevation: "34", slots: [1A,1B,1C,1D,1E,1F,1G,1H,1I,1J,1K,1L], portType: "LC",  portIndexes: "13:25" }
    - id: 2
      description: 3054
      source: { elevation: "29", slots: [ 1C, 1D, 2C, 2D  ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "3054", elevation: "41", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 3
      description: 2354
      source: { elevation: "33", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "2354", elevation: "41", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 4
      description: 3010
      source: { elevation: "31", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "3010", elevation: "41", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 5
      description: 2310
      source: { elevation: "35", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "2310", elevation: "41", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 6
      description: OHR1409A
      source: { elevation: "23", slots: [ 3A, 3B, 4A, 4B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1409A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 7
      description: OHR1414A
      source: { elevation: "23", slots: [ 5A, 5B, 6A, 6B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1414A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 8
      description: OHR1453A
      source: { elevation: "21", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1453A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 9
      description: OHR1458A
      source: { elevation: "21", slots: [ 3A, 3B, 4A, 4B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1458A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 10
      description: OHR1509A
      source: { elevation: "19", slots: [ 3A, 3B, 4A, 4B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1509A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 11
      description: OHR1514A
      source: { elevation: "19", slots: [ 5A, 5B, 6A, 6B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1514A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 12
      description: OHR1553A
      source: { elevation: "17", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1553A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 13
      description: OHR1558A
      source: { elevation: "17", slots: [ 3A, 3B, 4A, 4B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1558A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 14
      description: OHR1609A
      source: { elevation: "15", slots: [ 3A, 3B, 4A, 4B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1609A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 15
      description: OHR1614A
      source: { elevation: "15", slots: [ 5A, 5B, 6A, 6B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1614A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 16
      description: OHR1653A
      source: { elevation: "13", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1653A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 17
      description: OHR1658A
      source: { elevation: "13", slots: [ 3A, 3B, 4A, 4B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1658A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 18
      description: OHR1709A
      source: { elevation: "11", slots: [ 3A, 3B, 4A, 4B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1709A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 19
      description: OHR1714A
      source: { elevation: "11", slots: [ 5A, 5B, 6A, 6B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1714A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 20
      description: OHR1753A
      source: { elevation: "9", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1753A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 21
      description: OHR1758A
      source: { elevation: "9", slots: [ 3A, 3B, 4A, 4B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1758A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 22
      description: OHR1809A
      source: { elevation: "7", slots: [ 3A, 3B, 4A, 4B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1809A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 23
      description: OHR1814A
      source: { elevation: "7", slots: [ 5A, 5B, 6A, 6B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1814A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 24
      description: OHR1853A
      source: { elevation: "5", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1853A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 25
      description: OHR1858A
      source: { elevation: "5", slots: [ 3A, 3B, 4A, 4B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1858A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 26
      description: PF 1-2 tie
      source: { elevation: "37", slots: [ 1A,1B,1C,1D,2A,2B,2C,2D,3A,3B,3C,3D,4A,4B,4C,4D,5A,5B,5C,5D,6A,6B,6C,6D ], portType: "MPO",  portIndexes: "1" }
      destination: { rack: "1410", elevation: "37", slots: [ 1A,1B,1C,1D,2A,2B,2C,2D,3A,3B,3C,3D,4A,4B,4C,4D,5A,5B,5C,5D,6A,6B,6C,6D ], portType: "MPO",  portIndexes: "1" }

  # DHA Spine Pod 2 Passive Fiber Rack
  1410:
    - id: 1
      description: 3054
      source: { elevation: "29", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "3054", elevation: "41", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 2
      description: 2354
      source: { elevation: "33", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "2354", elevation: "41", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 3
      description: 3010
      source: { elevation: "31", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "3010", elevation: "41", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 4
      description: 2310
      source: { elevation: "35", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "2310", elevation: "41", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 5
      description: OHR1409A
      source: { elevation: "23", slots: [ 3C, 3D, 4C, 4D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1409A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 6
      description: OHR1414A
      source: { elevation: "23", slots: [ 5C, 5D, 6C, 6D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1414A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 7
      description: OHR1453A
      source: { elevation: "21", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1453A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 8
      description: OHR1458A
      source: { elevation: "21", slots: [ 3C, 3D, 4C, 4D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1458A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 9
      description: OHR1509A
      source: { elevation: "19", slots: [ 3C, 3D, 4C, 4D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1509A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 10
      description: OHR1514A
      source: { elevation: "19", slots:  [ 5C, 5D, 6C, 6D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1514A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 11
      description: OHR1553A
      source: { elevation: "17", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1553A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 12
      description: OHR1558A
      source: { elevation: "17", slots: [ 3C, 3D, 4C, 4D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1558A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 13
      description: OHR1609A
      source: { elevation: "15", slots: [ 3C, 3D, 4C, 4D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1609A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 14
      description: OHR1614A
      source: { elevation: "15", slots: [ 5C, 5D, 6C, 6D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1614A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 15
      description: OHR1653A
      source: { elevation: "13", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1653A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 16
      description: OHR1658A
      source: { elevation: "13", slots: [ 3C, 3D, 4C, 4D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1658A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 17
      description: OHR1709A
      source: { elevation: "11", slots: [ 3C, 3D, 4C, 4D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1709A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 18
      description: OHR1714A
      source: { elevation: "11", slots: [ 5C, 5D, 6C, 6D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1714A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 19
      description: OHR1753A
      source: { elevation: "9", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1753A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 20
      description: OHR1758A
      source: { elevation: "9", slots: [ 3C, 3D, 4C, 4D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1758A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 21
      description: OHR1809A
      source: { elevation: "7", slots: [ 3C, 3D, 4C, 4D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1809A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 22
      description: OHR1814A
      source: { elevation: "7", slots: [ 5C, 5D, 6C, 6D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1814A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 23
      description: OHR1853A
      source: { elevation: "5", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1853A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 24
      description: OHR1858A
      source: { elevation: "5", slots: [ 3C, 3D, 4C, 4D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1858A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 25
      description: Data Hall E
      source: { elevation: "39", slots: [ 1A,1B,1C,1D,1E,1F,1G,1H,1I,1J,1K,1L ], portType: "LC",  portIndexes: "13:25" }
      destination: { rack: "0814", elevation: "34", slots: [ 1A,1B,1C,1D,1E,1F,1G,1H,1I,1J,1K,1L ], portType: "LC",  portIndexes: "13:25" }
    - id: 26
      description: PF 1-2 tie
      source: { elevation: "37", slots: [ 1A,1B,1C,1D,2A,2B,2C,2D,3A,3B,3C,3D,4A,4B,4C,4D,5A,5B,5C,5D,6A,6B,6C,6D ], portType: "MPO",  portIndexes: "1" }
      destination: { rack: "1457", elevation: "37", slots: [ 1A,1B,1C,1D,2A,2B,2C,2D,3A,3B,3C,3D,4A,4B,4C,4D,5A,5B,5C,5D,6A,6B,6C,6D ], portType: "MPO",  portIndexes: "1" }

  # DHA GPU Pod 1 Passive Fiber Rack
  2310:
    - id: 1
      description: OHR1904A
      source: { elevation: "39", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1904A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 2
      description: OHR1908A
      source: { elevation: "39", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1908A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 3
      description: OHR1914A
      source: { elevation: "39", slots:  [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1914A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 4
      description: OHR2004A
      source: { elevation: "37", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2004A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 5
      description: OHR2008A
      source: { elevation: "37", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2008A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 6
      description: OHR2014A
      source: { elevation: "37", slots:  [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2014A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 7
      description: OHR2104A
      source: { elevation: "35", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2104A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 8
      description: OHR2108A
      source: { elevation: "35", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2108A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 9
      description: OHR2114A
      source: { elevation: "35", slots:  [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2114A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 10
      description: OHR2204A
      source: { elevation: "33", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2204A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 11
      description: OHR2208A
      source: { elevation: "33", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2208A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 12
      description: OHR2214A
      source: { elevation: "33", slots:  [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2214A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 13
      description: OHR2404A
      source: { elevation: "31", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2404A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 14
      description: OHR2408A
      source: { elevation: "31", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2408A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 15
      description: OHR2414A
      source: { elevation: "31", slots:  [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2414A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 16
      description: OHR2504A
      source: { elevation: "29", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2504A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 17
      description: OHR2508A
      source: { elevation: "29", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2508A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 18
      description: OHR2514A
      source: { elevation: "29", slots:  [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2514A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 19
      description: OHR2604A
      source: { elevation: "27", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2604A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 20
      description: OHR2608A
      source: { elevation: "27", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2608A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 21
      description: OHR2614A
      source: { elevation: "27", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2614A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }

  # DHA GPU Pod 2 Passive Fiber Rack
  2354:
    - id: 1
      description: OHR1952A
      source: { elevation: "39", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1952A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 2
      description: OHR1958A
      source: { elevation: "39", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1958A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 3
      description: OHR1962A
      source: { elevation: "39", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR1962A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 4
      description: OHR2052A
      source: { elevation: "37", slots:  [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2052A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 5
      description: OHR2058A
      source: { elevation: "37", slots:  [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2058A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 6
      description: OHR2062A
      source: { elevation: "37", slots:  [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2062A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 7
      description: OHR2152A
      source: { elevation: "35", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2152A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 8
      description: OHR2158A
      source: { elevation: "35", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2158A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 9
      description: OHR2162A
      source: { elevation: "35", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2162A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 10
      description: OHR2252A
      source: { elevation: "33", slots:  [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2252A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 11
      description: OHR2258A
      source: { elevation: "33", slots:  [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2258A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 12
      description: OHR2262A
      source: { elevation: "33", slots:  [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2262A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 13
      description: OHR2452A
      source: { elevation: "31", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2452A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 14
      description: OHR2458A
      source: { elevation: "31", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2458A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 15
      description: OHR2462A
      source: { elevation: "31", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2462A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 16
      description: OHR2552A
      source: { elevation: "29", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2552A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 17
      description: OHR2558A
      source: { elevation: "29", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2558A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 18
      description: OHR2562A
      source: { elevation: "29", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2562A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 19
      description: OHR2652A
      source: { elevation: "27", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2652A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 20
      description: OHR2658A
      source: { elevation: "27", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2658A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 21
      description: OHR2662A
      source: { elevation: "27", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2662A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }

  # DHA GPU Pod 3 Passive Fiber Rack
  3010:
    - id: 1
      description: OHR2704A
      source: { elevation: "39", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2704A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 2
      description: OHR2708A
      source: { elevation: "39", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2708A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 3
      description: OHR2714A
      source: { elevation: "39", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2714A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 4
      description: OHR2804A
      source: { elevation: "37", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2804A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 5
      description: OHR2808A
      source: { elevation: "37", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2808A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 6
      description: OHR2814A
      source: { elevation: "37", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2814A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 7
      description: OHR2904A
      source: { elevation: "35", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2904A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 8
      description: OHR2908A
      source: { elevation: "35", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2908A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 9
      description: OHR2914A
      source: { elevation: "35", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2914A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 10
      description: OHR3104A
      source: { elevation: "33", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3104A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 11
      description: OHR3109A
      source: { elevation: "33", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3109A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 12
      description: OHR3114A
      source: { elevation: "33", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3114A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 13
      description: OHR3204A
      source: { elevation: "31", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3204A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 14
      description: OHR3208A
      source: { elevation: "31", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3208A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 15
      description: OHR3214A
      source: { elevation: "31", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3214A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 16
      description: OHR3304A
      source: { elevation: "29", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3304A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 17
      description: OHR3308A
      source: { elevation: "29", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3308A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 18
      description: OHR3314A
      source: { elevation: "29", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3314A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 19
      description: OHR3404A
      source: { elevation: "27", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3404A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 20
      description: OHR3408A
      source: { elevation: "27", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3408A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 21
      description: OHR3414A
      source: { elevation: "27", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3414A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }

  # DHA GPU Pod 4 Passive Fiber Rack
  3054:
    - id: 1
      description: OHR2752A
      source: { elevation: "39", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2752A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 2
      description: OHR2758A
      source: { elevation: "39", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2758A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 3
      description: OHR2762A
      source: { elevation: "39", slots:  [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2762A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 4
      description: OHR2852A
      source: { elevation: "37", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2852A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 5
      description: OHR2858A
      source: { elevation: "37", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2858A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 6
      description: OHR2862A
      source: { elevation: "37", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2862A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 7
      description: OHR2952A
      source: { elevation: "35", slots:  [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2952A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 8
      description: OHR2958A
      source: { elevation: "35", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2958A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 9
      description: OHR2962A
      source: { elevation: "35", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR2962A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 10
      description: OHR3152A
      source: { elevation: "33", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3152A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 11
      description: OHR3157A
      source: { elevation: "33", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3157A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 12
      description: OHR3162A
      source: { elevation: "33", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3162A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 13
      description: OHR3252A
      source: { elevation: "31", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3252A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 14
      description: OHR3258A
      source: { elevation: "31", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3258A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 15
      description: OHR3262A
      source: { elevation: "31", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3262A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 16
      description: OHR3352A
      source: { elevation: "29", slots:  [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3352A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 17
      description: OHR3358A
      source: { elevation: "29", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3358A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 18
      description: OHR3362A
      source: { elevation: "29", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3362A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 19
      description: OHR3452A
      source: { elevation: "27", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3452A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 20
      description: OHR3458A
      source: { elevation: "27", slots:  [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3458A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 21
      description: OHR3462A
      source: { elevation: "27", slots:  [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3462A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }

  # DHB Spine Pod 1 Passive Fiber Rack
  3857:
    - id: 1
      description: Data Hall E
      source: { elevation: "39", slots: [ 1A,1B,1C,1D,1E,1F,1G,1H,1I,1J,1K,1L ], portType: "LC",  portIndexes: "13:25" }
      destination: { rack: "0818", elevation: "30", slots: [ 1A,1B,1C,1D,1E,1F,1G,1H,1I,1J,1K,1L ], portType: "LC",  portIndexes: "13:25" }
    - id: 2
      description: PF 1-2 tie
      source: { elevation: "37", slots: [ 1A,1B,1C,1D,2A,2B,2C,2D,3A,3B,3C,3D,4A,4B,4C,4D,5A,5B,5C,5D,6A,6B,6C,6D ], portType: "MPO",  portIndexes: "1" }
      destination: { rack: "3810", elevation: "37", slots: [ 1A,1B,1C,1D,2A,2B,2C,2D,3A,3B,3C,3D,4A,4B,4C,4D,5A,5B,5C,5D,6A,6B,6C,6D ], portType: "MPO",  portIndexes: "1" }
    - id: 3
      description: OHR3809A
      source: { elevation: "23", slots: [ 3C, 3D, 4C, 4D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3809A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 4
      description: OHR3814A
      source: { elevation: "23", slots: [ 5C, 5D, 6C, 6D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3814A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 5
      description: OHR3853A
      source: { elevation: "21", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3853A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 6
      description: OHR3858A
      source: { elevation: "21", slots: [ 3C, 3D, 4C, 4D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3858A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 7
      description: OHR3909A
      source: { elevation: "19", slots: [ 3C, 3D, 4C, 4D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3909A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 8
      description: OHR3914A
      source: { elevation: "19", slots: [ 5C, 5D, 6C, 6D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3914A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 9
      description: OHR3953A
      source: { elevation: "17", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3953A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 10
      description: OHR3958A
      source: { elevation: "17", slots: [ 3C, 3D, 4C, 4D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3958A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 11
      description: OHR4009A
      source: { elevation: "15", slots: [ 3C, 3D, 4C, 4D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4009A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 12
      description: OHR4014A
      source: { elevation: "15", slots: [ 5C, 5D, 6C, 6D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4014A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 13
      description: OHR4053A
      source: { elevation: "13", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4053A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 14
      description: OHR4058A
      source: { elevation: "13", slots: [ 3C, 3D, 4C, 4D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4058A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 15
      description: OHR4109A
      source: { elevation: "11", slots: [ 3C, 3D, 4C, 4D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4109A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 16
      description: OHR4114A
      source: { elevation: "11", slots: [ 5C, 5D, 6C, 6D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4114A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 17
      description: OHR4153A
      source: { elevation: "9", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4153A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 18
      description: OHR4158A
      source: { elevation: "9", slots: [ 3C, 3D, 4C, 4D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4158A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 19
      description: OHR4209A
      source: { elevation: "7", slots: [ 3C, 3D, 4C, 4D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4209A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 20
      description: OHR4214A
      source: { elevation: "7", slots: [ 5C, 5D, 6C, 6D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4214A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 21
      description: OHR4253A
      source: { elevation: "5", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4253A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 22
      description: OHR4258A
      source: { elevation: "5", slots: [ 3C, 3D, 4C, 4D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4258A", elevation: "1", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 23
      description: 4756
      source: { elevation: "33", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "4756", elevation: "41", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 24
      description: 4712
      source: { elevation: "35", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "4712", elevation: "41", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 25
      description: 5456
      source: { elevation: "29", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "5456", elevation: "41", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 26
      description: 5412
      source: { elevation: "31", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "5412", elevation: "41", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }

  # DHB Spine Pod 2 Passive Fiber Rack
  3810:
    - id: 1
      description: Data Hall E
      source: { elevation: "39", slots: [ 1A,1B,1C,1D,1E,1F,1G,1H,1I,1J,1K,1L ], portType: "LC",  portIndexes: "13:25" }
      destination: { rack: "0814", elevation: "30", slots: [ 1A,1B,1C,1D,1E,1F,1G,1H,1I,1J,1K,1L ], portType: "LC",  portIndexes: "13:25" }
    - id: 2
      description: 4756
      source: { elevation: "33", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "4756", elevation: "41", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 3
      description: 4712
      source: { elevation: "35", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "4712", elevation: "41", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 4
      description: 5456
      source: { elevation: "29", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "5456", elevation: "41", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 5
      description: 5412
      source: { elevation: "31", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "5412", elevation: "41", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 6
      description: OHR3809A
      source: { elevation: "23", slots: [ 3A, 3B, 4A, 4B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3809A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 7
      description: OHR3814A
      source: { elevation: "23", slots: [ 5A, 5B, 6A, 6B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3814A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 8
      description: OHR3853A
      source: { elevation: "21", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3853A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 9
      description: OHR3858A
      source: { elevation: "21", slots: [ 3A, 3B, 4A, 4B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3858A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 10
      description: OHR3909A
      source: { elevation: "19", slots: [ 3A, 3B, 4A, 4B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3909A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 11
      description: OHR3914A
      source: { elevation: "19", slots: [ 5A, 5B, 6A, 6B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3914A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 12
      description: OHR3953A
      source: { elevation: "17", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3953A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 13
      description: OHR3958A
      source: { elevation: "17", slots: [ 3A, 3B, 4A, 4B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR3958A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 14
      description: OHR4009A
      source: { elevation: "15", slots: [ 3A, 3B, 4A, 4B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4009A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 15
      description: OHR4014A
      source: { elevation: "15", slots: [ 5A, 5B, 6A, 6B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4014A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 16
      description: OHR4053A
      source: { elevation: "13", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4053A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 17
      description: OHR4058A
      source: { elevation: "13", slots: [ 3A, 3B, 4A, 4B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4058A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 18
      description: OHR4109A
      source: { elevation: "11", slots:  [ 3A, 3B, 4A, 4B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4109A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 19
      description: OHR4114A
      source: { elevation: "11", slots: [ 5A, 5B, 6A, 6B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4114A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 20
      description: OHR4153A
      source: { elevation: "9", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4153A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 21
      description: OHR4158A
      source: { elevation: "9", slots: [ 3A, 3B, 4A, 4B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4158A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 22
      description: OHR4209A
      source: { elevation: "7", slots: [ 3A, 3B, 4A, 4B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4209A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 23
      description: OHR4214A
      source: { elevation: "7", slots: [ 5A, 5B, 6A, 6B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4214A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 24
      description: OHR4253A
      source: { elevation: "5", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4253A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 25
      description: OHR4258A
      source: { elevation: "5", slots: [ 3A, 3B, 4A, 4B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4258A", elevation: "1", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 26
      description: PF 1-2 tie
      source: { elevation: "37", slots: [ 1A,1B,1C,1D,2A,2B,2C,2D,3A,3B,3C,3D,4A,4B,4C,4D,5A,5B,5C,5D,6A,6B,6C,6D ], portType: "MPO",  portIndexes: "1" }
      destination: { rack: "3857", elevation: "37", slots: [ 1A,1B,1C,1D,2A,2B,2C,2D,3A,3B,3C,3D,4A,4B,4C,4D,5A,5B,5C,5D,6A,6B,6C,6D ], portType: "MPO",  portIndexes: "1" }

  # DHB GPU Pod 1 Passive Fiber Rack
  4712:
    - id: 1
      description: OHR4304A
      source: { elevation: "39", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4304A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 2
      description: OHR4308A
      source: { elevation: "39", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4308A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 3
      description: OHR4314A
      source: { elevation: "39", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4314A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 4
      description: OHR4404A
      source: { elevation: "37", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4404A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 5
      description: OHR4408A
      source: { elevation: "37", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4408A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 6
      description: OHR4414A
      source: { elevation: "37", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4414A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 7
      description: OHR4504A
      source: { elevation: "35", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4504A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 8
      description: OHR4508A
      source: { elevation: "35", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4508A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 9
      description: OHR4514A
      source: { elevation: "35", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4514A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 10
      description: OHR4604A
      source: { elevation: "33", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4604A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 11
      description: OHR4608A
      source: { elevation: "33", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4608A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 12
      description: OHR4614A
      source: { elevation: "33", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4614A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 13
      description: OHR4804A
      source: { elevation: "31", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4804A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 14
      description: OHR4808A
      source: { elevation: "31", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4808A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 15
      description: OHR4814A
      source: { elevation: "31", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4814A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 16
      description: OHR4904A
      source: { elevation: "29", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4904A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 17
      description: OHR4908A
      source: { elevation: "29", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4908A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 18
      description: OHR4914A
      source: { elevation: "29", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4914A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 19
      description: OHR5004A
      source: { elevation: "27", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5004A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 20
      description: OHR5008A
      source: { elevation: "27", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5008A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 21
      description: OHR5014A
      source: { elevation: "27", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5014A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
  # DHB GPU Pod 2 Passive Fiber Rack
  4756:
    - id: 1
      description: OHR4352A
      source: { elevation: "39", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4352A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 2
      description: OHR4358A
      source: { elevation: "39", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4358A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 3
      description: OHR4362A
      source: { elevation: "39", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4362A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 4
      description: OHR4452A
      source: { elevation: "37", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4452A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 5
      description: OHR4458A
      source: { elevation: "37", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4458A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 6
      description: OHR4462A
      source: { elevation: "37", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4462A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 7
      description: OHR4552A
      source: { elevation: "35", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4552A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 8
      description: OHR4558A
      source: { elevation: "35", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4558A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 9
      description: OHR4562A
      source: { elevation: "35", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4562A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 10
      description: OHR4652A
      source: { elevation: "33", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4652A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 11
      description: OHR4658A
      source: { elevation: "33", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4658A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 12
      description: OHR4662A
      source: { elevation: "33", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4662A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 13
      description: OHR4852A
      source: { elevation: "31", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4852A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 14
      description: OHR4858A
      source: { elevation: "31", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4858A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 15
      description: OHR4862A
      source: { elevation: "31", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4862A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 16
      description: OHR4952A
      source: { elevation: "29", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4952A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 17
      description: OHR4958A
      source: { elevation: "29", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4958A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 18
      description: OHR4962A
      source: { elevation: "29", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR4962A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 19
      description: OHR5052A
      source: { elevation: "27", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5052A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 20
      description: OHR5058A
      source: { elevation: "27", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5058A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 21
      description: OHR5062A
      source: { elevation: "27", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5062A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
  # DHB GPU Pod 3 Passive Fiber Rack
  5412:
    - id: 1
      description: OHR5104A
      source: { elevation: "39", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5104A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 2
      description: OHR5108A
      source: { elevation: "39", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5108A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 3
      description: OHR5114A
      source: { elevation: "39", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5114A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 4
      description: OHR5204A
      source: { elevation: "37", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5204A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 5
      description: OHR5208A
      source: { elevation: "37", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5208A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 6
      description: OHR5214A
      source: { elevation: "37", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5214A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 7
      description: OHR5304A
      source: { elevation: "35", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5304A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 8
      description: OHR5308A
      source: { elevation: "35", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5308A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 9
      description: OHR5314A
      source: { elevation: "35", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5314A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 10
      description: OHR5504A
      source: { elevation: "33", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5504A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 11
      description: OHR5507A
      source: { elevation: "33", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5507A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 12
      description: OHR5514A
      source: { elevation: "33", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5514A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 13
      description: OHR5604A
      source: { elevation: "31", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5604A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 14
      description: OHR5608A
      source: { elevation: "31", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5608A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 15
      description: OHR5614A
      source: { elevation: "31", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5614A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 16
      description: OHR5704A
      source: { elevation: "29", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5704A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 17
      description: OHR5708A
      source: { elevation: "29", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5708A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 18
      description: OHR5714A
      source: { elevation: "29", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5714A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 19
      description: OHR5804A
      source: { elevation: "27", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5804A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 20
      description: OHR5808A
      source: { elevation: "27", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5808A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 21
      description: OHR5814A
      source: { elevation: "27", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5814A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }

  # DHB GPU Pod 4 Passive Fiber Rack
  5456:
    - id: 1
      description: OHR5152A
      source: { elevation: "39", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5152A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 2
      description: OHR5158A
      source: { elevation: "39", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5158A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 3
      description: OHR5162A
      source: { elevation: "39", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5162A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 4
      description: OHR5252A
      source: { elevation: "37", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5252A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 5
      description: OHR5258A
      source: { elevation: "37", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5258A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 6
      description: OHR5262A
      source: { elevation: "37", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5262A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 7
      description: OHR5352A
      source: { elevation: "35", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5352A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 8
      description: OHR5358A
      source: { elevation: "35", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5358A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 9
      description: OHR5362A
      source: { elevation: "35", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5362A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 10
      description: OHR5552A
      source: { elevation: "33", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5552A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 11
      description: OHR5559A
      source: { elevation: "33", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5559A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 12
      description: OHR5562A
      source: { elevation: "33", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5562A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 13
      description: OHR5652A
      source: { elevation: "31", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5652A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 14
      description: OHR5658A
      source: { elevation: "31", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5658A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 15
      description: OHR5662A
      source: { elevation: "31", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5662A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 16
      description: OHR5752A
      source: { elevation: "29", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5752A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 17
      description: OHR5758A
      source: { elevation: "29", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5758A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 18
      description: OHR5762A
      source: { elevation: "29", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5762A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 19
      description: OHR5852A
      source: { elevation: "27", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5852A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 20
      description: OHR5858A
      source: { elevation: "27", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5858A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 21
      description: OHR5862A
      source: { elevation: "27", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR5862A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
  # DHC Spine Pod 1 Fiber Passive Rack
  6254:
    - id: 1
      description: Data Hall E
      source: { elevation: "39", slots: [ 1A,1B,1C,1D,1E,1F,1G,1H,1I,1J,1K,1L ], portType: "LC", portIndexes: "13:25" }
      destination: { rack: "0818", elevation: "26", slots: [ 1A,1B,1C,1D,1E,1F,1G,1H,1I,1J,1K,1L ], portType: "LC", portIndexes: "13:25" }
    - id: 2
      description: PF 1-2 Tie
      source: { elevation: "37", slots: [ 1A,1B,1C,1D,2A,2B,2C,2D,3A,3B,3C,3D,4A,4B,4C,4D,5A,5B,5C,5D,6A,6B,6C,6D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "6210", elevation: "37", slots: [ 1A,1B,1C,1D,2A,2B,2C,2D,3A,3B,3C,3D,4A,4B,4C,4D,5A,5B,5C,5D,6A,6B,6C,6D ], portType: "MPO", portIndexes: "1" }
    - id: 3
      description: 7858
      source: { elevation: "29", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "7858", elevation: "41", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 4
      description: 7158
      source: { elevation: "33", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "7158", elevation: "41", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 5
      description: 7812
      source: { elevation: "31", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "7812", elevation: "41", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 6
      description: 7112
      source: { elevation: "35", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "7112", elevation: "41", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
  # DHC Spine Pod 2 Fiber Passive Rack
  6210:
    - id: 1
      description: Data Hall E
      source: { elevation: "39", slots: [ 1A,1B,1C,1D,1E,1F,1G,1H,1I,1J,1K,1L ], portType: "LC", portIndexes: "13:25" }
      destination: { rack: "0814", elevation: "26", slots: [ 1A,1B,1C,1D,1E,1F,1G,1H,1I,1J,1K,1L ], portType: "LC", portIndexes: "13:25" }
    - id: 2
      description: PF 1-2 Tie
      source: { elevation: "37", slots: [ 1A,1B,1C,1D,2A,2B,2C,2D,3A,3B,3C,3D,4A,4B,4C,4D,5A,5B,5C,5D,6A,6B,6C,6D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "6254", elevation: "37", slots: [ 1A,1B,1C,1D,2A,2B,2C,2D,3A,3B,3C,3D,4A,4B,4C,4D,5A,5B,5C,5D,6A,6B,6C,6D ], portType: "MPO", portIndexes: "1" }
    - id: 3
      description: 7858
      source: { elevation: "29", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "7858", elevation: "41", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 4
      description: 7158
      source: { elevation: "33", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "7158", elevation: "41", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 5
      description: 7812
      source: { elevation: "31", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "7812", elevation: "41", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 6
      description: 7112
      source: { elevation: "35", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "7112", elevation: "41", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
  # DHC GPU Pod 1 Passive Fiber Rack
  7112:
    - id: 1
      description: OHR6704A
      source: { elevation: "39", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR6704A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 2
      description: OHR6707A
      source: { elevation: "39", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR6707A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 3
      description: OHR6714A
      source: { elevation: "39", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR6714A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 4
      description: OHR6804A
      source: { elevation: "37", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR6804A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 5
      description: OHR6808A
      source: { elevation: "37", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR6808A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 6
      description: OHR6814A
      source: { elevation: "37", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR6814A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 7
      description: OHR6904A
      source: { elevation: "35", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR6904A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 8
      description: OHR6908A
      source: { elevation: "35", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR6908A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 9
      description: OHR6914A
      source: { elevation: "35", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR6914A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 10
      description: OHR7004A
      source: { elevation: "33", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7004A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 11
      description: OHR7008A
      source: { elevation: "33", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7008A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 12
      description: OHR7014A
      source: { elevation: "33", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7014A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 13
      description: OHR7204A
      source: { elevation: "31", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7204A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 14
      description: OHR7208A
      source: { elevation: "31", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7208A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 15
      description: OHR7214A
      source: { elevation: "31", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7214A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 16
      description: OHR7304A
      source: { elevation: "29", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7304A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 17
      description: OHR7308A
      source: { elevation: "29", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7308A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 18
      description: OHR7314A
      source: { elevation: "29", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7314A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 19
      description: OHR7404A
      source: { elevation: "27", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7404A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 20
      description: OHR7408A
      source: { elevation: "27", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7408A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 21
      description: OHR7414A
      source: { elevation: "27", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7414A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
  # DHC GPU Pod 2 Passive Fiber Rack
  7158:
    - id: 1
      description: OHR6754A
      source: { elevation: "39", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR6754A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 2
      description: OHR6759A
      source: { elevation: "39", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR6759A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 3
      description: OHR6764A
      source: { elevation: "39", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR6764A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 4
      description: OHR6854A
      source: { elevation: "37", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR6854A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 5
      description: OHR6860A
      source: { elevation: "37", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR6860A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 6
      description: OHR6864A
      source: { elevation: "37", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR6864A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 7
      description: OHR6954A
      source: { elevation: "35", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR6954A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 8
      description: OHR6960A
      source: { elevation: "35", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR6960A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 9
      description: OHR6964A
      source: { elevation: "35", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR6964A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 10
      description: OHR7054A
      source: { elevation: "33", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7054A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 11
      description: OHR7060A
      source: { elevation: "33", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7060A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 12
      description: OHR7064A
      source: { elevation: "33", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7064A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 13
      description: OHR7254A
      source: { elevation: "31", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7254A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 14
      description: OHR7260A
      source: { elevation: "31", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7260A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 15
      description: OHR7264A
      source: { elevation: "31", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7264A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 16
      description: OHR7354A
      source: { elevation: "29", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7354A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 17
      description: OHR7360A
      source: { elevation: "29", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7360A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 18
      description: OHR7364A
      source: { elevation: "29", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7364A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 19
      description: OHR7454A
      source: { elevation: "27", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7454A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 20
      description: OHR7460A
      source: { elevation: "27", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7460A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 21
      description: OHR7464A
      source: { elevation: "27", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7464A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
  # DHC GPU Pod 3 Passive Fiber Rack
  7812:
    - id: 1
      description: OHR7504A
      source: { elevation: "39", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7504A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 2
      description: OHR7508A
      source: { elevation: "39", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7508A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 3
      description: OHR7514A
      source: { elevation: "39", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7514A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 4
      description: OHR7604A
      source: { elevation: "37", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7604A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 5
      description: OHR7608A
      source: { elevation: "37", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7608A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 6
      description: OHR7614A
      source: { elevation: "37", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7614A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 7
      description: OHR7704A
      source: { elevation: "35", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7704A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 8
      description: OHR7708A
      source: { elevation: "35", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7708A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 9
      description: OHR7714A
      source: { elevation: "35", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7714A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 10
      description: OHR7904A
      source: { elevation: "33", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7904A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 11
      description: OHR7907A
      source: { elevation: "33", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7907A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 12
      description: OHR7914A
      source: { elevation: "33", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7914A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 13
      description: OHR8004A
      source: { elevation: "31", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR8004A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 14
      description: OHR8008A
      source: { elevation: "31", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR8008A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 15
      description: OHR8014A
      source: { elevation: "31", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR8014A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 16
      description: OHR8104A
      source: { elevation: "29", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR8104A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 17
      description: OHR8108A
      source: { elevation: "29", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR8108A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 18
      description: OHR8114A
      source: { elevation: "29", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR8114A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 19
      description: OHR8204A
      source: { elevation: "27", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR8204A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 20
      description: OHR8208A
      source: { elevation: "27", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR8208A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 21
      description: OHR8214A
      source: { elevation: "27", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR8214A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
  # DHC GPU Pod 4 Passive Fiber Rack
  7858:
    - id: 1
      description: OHR7554A
      source: { elevation: "39", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7554A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 2
      description: OHR7560A
      source: { elevation: "39", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7560A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 3
      description: OHR7564A
      source: { elevation: "39", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7564A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 4
      description: OHR7654A
      source: { elevation: "37", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7654A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 5
      description: OHR7660A
      source: { elevation: "37", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7660A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 6
      description: OHR7664A
      source: { elevation: "37", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7664A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 7
      description: OHR7754A
      source: { elevation: "35", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7754A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 8
      description: OHR7760A
      source: { elevation: "35", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7760A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 9
      description: OHR7764A
      source: { elevation: "35", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7764A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 10
      description: OHR7954A
      source: { elevation: "33", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7954A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 11
      description: OHR7959A
      source: { elevation: "33", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7959A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 12
      description: OHR7964A
      source: { elevation: "33", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR7964A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 13
      description: OHR8054A
      source: { elevation: "31", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR8054A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 14
      description: OHR8060A
      source: { elevation: "31", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR8060A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 15
      description: OHR8064A
      source: { elevation: "31", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR8064A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 16
      description: OHR8154A
      source: { elevation: "29", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR8154A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 17
      description: OHR8160A
      source: { elevation: "29", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR8160A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 18
      description: OHR8164A
      source: { elevation: "29", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR8164A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 19
      description: OHR8254A
      source: { elevation: "27", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR8254A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 20
      description: OHR8260A
      source: { elevation: "27", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR8260A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 21
      description: OHR8264A
      source: { elevation: "27", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR8264A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }

  # DHD Spine Pod 1 Fiber Passive Rack
  8654:
    - id: 1
      description: Data Hall E
      source: { elevation: "39", slots: [1A,1B,1C,1D,1E,1F,1G,1H,1I,1J,1K,1L], portType: "LC",  portIndexes: "13:25" }
      destination: { rack: "0818", elevation: "22", slots: [1A,1B,1C,1D,1E,1F,1G,1H,1I,1J,1K,1L], portType: "LC",  portIndexes: "13:25" }
    - id: 2
      description: PF 1-2 tie
      source: { elevation: "37", slots: [ 1A,1B,1C,1D,2A,2B,2C,2D,3A,3B,3C,3D,4A,4B,4C,4D,5A,5B,5C,5D,6A,6B,6C,6D ], portType: "MPO",  portIndexes: "1" }
      destination: { rack: "8610", elevation: "37", slots: [ 1A,1B,1C,1D,2A,2B,2C,2D,3A,3B,3C,3D,4A,4B,4C,4D,5A,5B,5C,5D,6A,6B,6C,6D ], portType: "MPO",  portIndexes: "1" }
    - id: 3
      description: 9561
      source: { elevation: "33", slots: [1C, 1D, 2C, 2D], portType: "MPO", portIndexes: "1" }
      destination: { rack: "9561", elevation: "41", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 4
      description: 9512
      source: { elevation: "35", slots: [1C, 1D, 2C, 2D], portType: "MPO", portIndexes: "1" }
      destination: { rack: "9512", elevation: "41", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 5
      description: 10261
      source: { elevation: "29", slots: [1C, 1D, 2C, 2D], portType: "MPO", portIndexes: "1" }
      destination: { rack: "10261", elevation: "41", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }
    - id: 6
      description: 10212
      source: { elevation: "31", slots: [1C, 1D, 2C, 2D], portType: "MPO", portIndexes: "1" }
      destination: { rack: "10212", elevation: "41", slots: [ 1C, 1D, 2C, 2D ], portType: "MPO", portIndexes: "1" }

  # DHD Spine Pod 2 Fiber Passive Rack
  8610:
    - id: 1
      description: Data Hall E
      source: { elevation: "39", slots: [1A,1B,1C,1D,1E,1F,1G,1H,1I,1J,1K,1L], portType: "LC",  portIndexes: "13:25" }
      destination: { rack: "0814", elevation: "22", slots: [1A,1B,1C,1D,1E,1F,1G,1H,1I,1J,1K,1L], portType: "LC",  portIndexes: "13:25" }
    - id: 2
      description: PF 1-2 tie
      source: { elevation: "37", slots: [ 1A,1B,1C,1D,2A,2B,2C,2D,3A,3B,3C,3D,4A,4B,4C,4D,5A,5B,5C,5D,6A,6B,6C,6D ], portType: "MPO",  portIndexes: "1" }
      destination: { rack: "8654", elevation: "37", slots: [ 1A,1B,1C,1D,2A,2B,2C,2D,3A,3B,3C,3D,4A,4B,4C,4D,5A,5B,5C,5D,6A,6B,6C,6D ], portType: "MPO",  portIndexes: "1" }
    - id: 3
      description: 9561
      source: { elevation: "33", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "9561", elevation: "41", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 4
      description: 9512
      source: { elevation: "35", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "9512", elevation: "41", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 5
      description: 10261
      source: { elevation: "29", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "10261", elevation: "41", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
    - id: 6
      description: 10212
      source: { elevation: "31", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "10212", elevation: "41", slots: [ 1A, 1B, 2A, 2B ], portType: "MPO", portIndexes: "1" }
  # DHD GPU Pod 1 Passive Fiber Rack
  9512:
    - id: 1
      description: OHR9104A
      source: { elevation: "39", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9104A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 2
      description: OHR9107A
      source: { elevation: "39", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9107A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 3
      description: OHR9114A
      source: { elevation: "39", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9114A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 4
      description: OHR9204A
      source: { elevation: "37", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9204A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 5
      description: OHR9208A
      source: { elevation: "37", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9208A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 6
      description: OHR9214A
      source: { elevation: "37", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9214A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 7
      description: OHR9304A
      source: { elevation: "35", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9304A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 8
      description: OHR9308A
      source: { elevation: "35", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9308A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 9
      description: OHR9314A
      source: { elevation: "35", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9314A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 10
      description: OHR9404A
      source: { elevation: "33", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9404A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 11
      description: OHR9408A
      source: { elevation: "33", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9408A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 12
      description: OHR9414A
      source: { elevation: "33", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9414A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 13
      description: OHR9604A
      source: { elevation: "31", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9604A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 14
      description: OHR9608A
      source: { elevation: "31", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9608A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 15
      description: OHR9614A
      source: { elevation: "31", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9614A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 16
      description: OHR9704A
      source: { elevation: "29", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9704A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 17
      description: OHR9708A
      source: { elevation: "29", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9708A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 18
      description: OHR9714A
      source: { elevation: "29", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9714A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 19
      description: OHR9804A
      source: { elevation: "27", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9804A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 20
      description: OHR9808A
      source: { elevation: "27", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9808A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 21
      description: OHR9814A
      source: { elevation: "27", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9814A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
  # DHD GPU Pod 2 Passive Fiber Rack
  9561:
    - id: 1
      description: OHR9157A
      source: { elevation: "39", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9157A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 2
      description: OHR9162A
      source: { elevation: "39", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9162A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 3
      description: OHR9167A
      source: { elevation: "39", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9167A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 4
      description: OHR9257A
      source: { elevation: "37", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9257A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 5
      description: OHR9263A
      source: { elevation: "37", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9263A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 6
      description: OHR9267A
      source: { elevation: "37", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9267A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 7
      description: OHR9357A
      source: { elevation: "35", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9357A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 8
      description: OHR9363A
      source: { elevation: "35", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9363A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 9
      description: OHR9367A
      source: { elevation: "35", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9367A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 10
      description: OHR9457A
      source: { elevation: "33", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9457A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 11
      description: OHR9463A
      source: { elevation: "33", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9463A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 12
      description: OHR9467A
      source: { elevation: "33", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9467A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 13
      description: OHR9657A
      source: { elevation: "31", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9657A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 14
      description: OHR9663A
      source: { elevation: "31", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9663A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 15
      description: OHR9667A
      source: { elevation: "31", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9667A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 16
      description: OHR9757A
      source: { elevation: "29", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9757A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 17
      description: OHR9763A
      source: { elevation: "29", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9763A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 18
      description: OHR9767A
      source: { elevation: "29", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9767A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 19
      description: OHR9857A
      source: { elevation: "27", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9857A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 20
      description: OHR9863A
      source: { elevation: "27", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9863A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 21
      description: OHR9867A
      source: { elevation: "27", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9867A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
  # DHD GPU Pod 3 Passive Fiber Rack
  10212:
    - id: 1
      description: OHR9904A
      source: { elevation: "39", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9904A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 2
      description: OHR9908A
      source: { elevation: "39", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9908A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 3
      description: OHR9914A
      source: { elevation: "39", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9914A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 4
      description: OHR10004A
      source: { elevation: "37", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10004A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 5
      description: OHR10007A
      source: { elevation: "37", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10007A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 6
      description: OHR10014A
      source: { elevation: "37", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10014A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 7
      description: OHR10104A
      source: { elevation: "35", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10104A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 8
      description: OHR10108A
      source: { elevation: "35", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10108A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 9
      description: OHR10114A
      source: { elevation: "35", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10114A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 10
      description: OHR10304A
      source: { elevation: "33", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10304A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 11
      description: OHR10308A
      source: { elevation: "33", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10308A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 12
      description: OHR10314A
      source: { elevation: "33", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10314A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 13
      description: OHR10404A
      source: { elevation: "31", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10404A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 14
      description: OHR10408A
      source: { elevation: "31", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10408A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 15
      description: OHR10414A
      source: { elevation: "31", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10414A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 16
      description: OHR10504A
      source: { elevation: "29", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10504A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 17
      description: OHR10508A
      source: { elevation: "29", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10508A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 18
      description: OHR10514A
      source: { elevation: "29", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10514A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 19
      description: OHR10604A
      source: { elevation: "27", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10604A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 20
      description: OHR10608A
      source: { elevation: "27", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10608A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 21
      description: OHR10614A
      source: { elevation: "27", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10614A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
  # DHD GPU Pod 4 Passive Fiber Rack
  10261:
    - id: 1
      description: OHR9957A
      source: { elevation: "39", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9957A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 2
      description: OHR9963A
      source: { elevation: "39", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9963A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 3
      description: OHR9967A
      source: { elevation: "39", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR9967A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 4
      description: OHR10057A
      source: { elevation: "37", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10057A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 5
      description: OHR10062A
      source: { elevation: "37", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10062A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 6
      description: OHR10067A
      source: { elevation: "37", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10067A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 7
      description: OHR10157A
      source: { elevation: "35", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10157A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 8
      description: OHR10163A
      source: { elevation: "35", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10163A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 9
      description: OHR10167A
      source: { elevation: "35", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10167A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 10
      description: OHR10357A
      source: { elevation: "33", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10357A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 11
      description: OHR10362A
      source: { elevation: "33", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10362A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 12
      description: OHR10367A
      source: { elevation: "33", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10367A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 13
      description: OHR10457A
      source: { elevation: "31", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10457A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 14
      description: OHR10463A
      source: { elevation: "31", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10463A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 15
      description: OHR10467A
      source: { elevation: "31", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10467A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 16
      description: OHR10557A
      source: { elevation: "29", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10557A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 17
      description: OHR10563A
      source: { elevation: "29", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10563A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 18
      description: OHR10567A
      source: { elevation: "29", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10567A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 19
      description: OHR10657A
      source: { elevation: "27", slots: [ 1A,1B,1C,1D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10657A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 20
      description: OHR10663A
      source: { elevation: "27", slots: [ 3A,3B,3C,3D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10663A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
    - id: 21
      description: OHR10667A
      source: { elevation: "27", slots: [ 5A,5B,5C,5D ], portType: "MPO", portIndexes: "1" }
      destination: { rack: "OHR10667A", elevation: "1", slots: [ 4A,4B,4C,4D ], portType: "MPO", portIndexes: "1" }
