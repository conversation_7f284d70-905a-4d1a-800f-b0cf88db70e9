package com.oracle.oci.rmc.dcms.impl.service.test;

import static com.oracle.oci.rmc.dcms.impl.util.CreateEntityTestHelper.TEST_USER;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.oracle.oci.rmc.auditevent.model.AuditEventContext;
import com.oracle.oci.rmc.auditevent.service.AuditEventContextHolder;
import com.oracle.oci.rmc.dcms.api.model.entity.DeviceInstance;
import com.oracle.oci.rmc.dcms.api.model.entity.DeviceInstanceDetails;
import com.oracle.oci.rmc.dcms.api.model.entity.DeviceModel;
import com.oracle.oci.rmc.dcms.api.model.entity.RackInstanceDetails;
import com.oracle.oci.rmc.dcms.api.model.entity.RackModel;
import com.oracle.oci.rmc.dcms.api.model.entity.RegionEntity;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.Asset;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetCreationDetailsBuilder;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetLifecycleState;
import com.oracle.oci.rmc.dcms.api.service.DeviceInstanceAdminService;
import com.oracle.oci.rmc.dcms.api.service.DeviceInstanceService;
import com.oracle.oci.rmc.dcms.api.service.DeviceModelService;
import com.oracle.oci.rmc.dcms.api.service.dto.DeviceInstanceFilter;
import com.oracle.oci.rmc.dcms.api.service.inventory.AssetService;
import com.oracle.oci.rmc.dcms.impl.dal.DeviceInstanceRepository;
import com.oracle.oci.rmc.dcms.impl.dal.testutil.DcmsRepositoryTestUtil;
import com.oracle.oci.rmc.dcms.impl.util.CreateEntityTestHelper;
import com.oracle.oci.rmc.model.RmcId;
import com.oracle.oci.rmc.model.RmcIdType;
import com.oracle.oci.sfw.micronaut.http.pagination.PageQuery;
import com.oracle.pic.commons.exceptions.server.RenderableException;
import io.micronaut.context.annotation.Requires;
import io.micronaut.test.annotation.TransactionMode;
import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

@MicronautTest(transactionMode = TransactionMode.SINGLE_TRANSACTION)
@Requires(property = "database.type", value = "ORACLE")
public class DeviceInstanceAdminServiceTest {
  private final DeviceInstanceAdminService deviceInstanceAdminService;
  private final DcmsRepositoryTestUtil repositoryTestUtil;
  private final DeviceInstanceRepository deviceInstanceRepository;
  private final CreateEntityTestHelper createEntityTestHelper;
  private final AssetService assetService;
  private final DeviceModelService deviceModelService;
  private final DeviceInstanceService deviceInstanceService;
  private String oraclePartNumber;
  private String manufacturerPartNumber;
  private String serialNumber;

  public DeviceInstanceAdminServiceTest(
      DeviceInstanceAdminService deviceInstanceAdminService,
      DcmsRepositoryTestUtil repositoryTestUtil,
      DeviceInstanceRepository deviceInstanceRepository,
      CreateEntityTestHelper createEntityTestHelper,
      AssetService assetService,
      DeviceModelService deviceModelService,
      DeviceInstanceService deviceInstanceService) {
    this.deviceInstanceAdminService = deviceInstanceAdminService;
    this.repositoryTestUtil = repositoryTestUtil;
    this.deviceInstanceRepository = deviceInstanceRepository;
    this.createEntityTestHelper = createEntityTestHelper;
    this.assetService = assetService;
    this.deviceModelService = deviceModelService;
    this.deviceInstanceService = deviceInstanceService;
  }

  @BeforeEach
  void setup() {
    AuditEventContextHolder.setContext(new AuditEventContext(TEST_USER));
    oraclePartNumber = "oraclePN" + UUID.randomUUID();
    manufacturerPartNumber = "mnfPN" + UUID.randomUUID();
    serialNumber = "SN";
    repositoryTestUtil.createRackParentEntities();
  }

  @Test
  public void testAdminUpdateDeviceInstance_update_asset_id() {
    var region = createEntityTestHelper.createRegionEntity();
    var airportCode = region.airportCode();
    // Create parent asset
    var rootRackAsset =
        assetService.createAsset(
            assetService.buildAsset(
                airportCode,
                null,
                oraclePartNumber,
                manufacturerPartNumber,
                serialNumber,
                null,
                null,
                "TEST-COMPARTMENT-OCID",
                null,
                null,
                null,
                AssetLifecycleState.IN_TRANSIT,
                null,
                null));
    RackModel rackModel = repositoryTestUtil.createAndGetComputeRackModel();
    RackInstanceDetails rackInstance =
        repositoryTestUtil.createRackInstance(region, rootRackAsset, rackModel);
    // create device model for the rack
    DeviceModel deviceModel =
        repositoryTestUtil.createTierZeroDeviceInRack(rackModel, "device_platform", "elevation");

    // Create child asset
    var childRackAsset =
        assetService.createAsset(
            assetService.buildAsset(
                airportCode,
                null,
                oraclePartNumber + "1",
                manufacturerPartNumber + "1",
                serialNumber + "1",
                rootRackAsset.id().toString(),
                deviceModel.elevation(),
                "TEST-COMPARTMENT-OCID",
                null,
                null,
                null,
                AssetLifecycleState.INSTALLED,
                null,
                null));

    DeviceInstance deviceInstance =
        repositoryTestUtil.createDeviceInstance(region, rackInstance, childRackAsset, deviceModel);
    assertEquals(deviceInstance.assetId(), childRackAsset.id());
    // updating device instance with non-existent asset should throw an exception
    assertThrows(
        RenderableException.class,
        () ->
            deviceInstanceAdminService.updateDeviceInstance(
                airportCode, deviceInstance.id(), RmcId.generate(RmcIdType.Inventory.ASSET)));
    // Create child asset
    var anotherChildRackAsset =
        assetService.createAsset(
            assetService.buildAsset(
                airportCode,
                null,
                oraclePartNumber + "2",
                manufacturerPartNumber + "2",
                serialNumber + "2",
                rootRackAsset.id().toString(),
                deviceModel.elevation(),
                "TEST-COMPARTMENT-OCID",
                null,
                null,
                null,
                AssetLifecycleState.INSTALLED,
                null,
                null));

    deviceInstanceAdminService.updateDeviceInstance(
        airportCode, deviceInstance.id(), anotherChildRackAsset.id());
    assertEquals(
        deviceInstanceRepository.findById(deviceInstance.id()).get().assetId(),
        anotherChildRackAsset.id());
  }

  @Test
  void testCreatedDeviceInstanceUsingDeviceModelId() {
    RegionEntity regionEntity = repositoryTestUtil.getRegionEntity();
    var region = regionEntity.airportCode();
    var pageQuery = PageQuery.fromEmptyToken(10);
    var airportCode = repositoryTestUtil.getRegionEntity().airportCode();
    RackModel rackModel = repositoryTestUtil.createAndGetComputeRackModel();
    // Create RackAsset
    Asset rackAsset =
        repositoryTestUtil.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(regionEntity.id())
                .catalogPartId(
                    repositoryTestUtil.createCatalogPart("partnumber", "partnumber").id())
                .serialNumber("serialNumber")
                .build());

    repositoryTestUtil.createRackInstance(regionEntity, rackAsset, rackModel);
    var deviceModel =
        deviceModelService.createDeviceModel(
            airportCode,
            "patch-e2-2c-compute",
            rackModel.rackPosition().rackNumber(),
            "displayName",
            "elevation",
            "Switch",
            "deviceOpn");

    // findAllWithOptionalFilters empty
    DeviceInstanceFilter filter = new DeviceInstanceFilter(region, null, null, null, null, null);
    var emptyList = deviceInstanceService.listDeviceInstances(filter, pageQuery);
    assertTrue(emptyList.getResults().isEmpty());

    repositoryTestUtil.createAsset(
        AssetCreationDetailsBuilder.builder()
            .regionId(regionEntity.id())
            .catalogPartId(repositoryTestUtil.createCatalogPart("partnumber", "partnumber").id())
            .serialNumber("serial")
            .elevation("elevation")
            .parentAssetId(rackAsset.id())
            .build());
    // create
    var created = deviceInstanceAdminService.createDeviceInstance(region, deviceModel.id());
    assertNotNull(created);
    assertNotNull(created.timeCreated());
    assertEquals(created.timeCreated(), created.timeUpdated());
    assertEquals(0L, created.version());

    // get
    DeviceInstanceDetails fetchedDeviceInstance =
        deviceInstanceService.getDeviceInstance(region, RmcId.fromString(created.id()));
    assertEquals(created, fetchedDeviceInstance);

    // if we want to create the device instance again for same model
    // get the exception
    Exception exception =
        assertThrows(
            RenderableException.class,
            () -> deviceInstanceAdminService.createDeviceInstance(region, deviceModel.id()));
    assertTrue(
        exception.getMessage().contains("Already have device instance for the device model"));
  }

  @Test
  void testCreatedDeviceInstanceFailsWhenNoRackInstanceFound() {
    RegionEntity regionEntity = repositoryTestUtil.getRegionEntity();
    var region = regionEntity.airportCode();
    var pageQuery = PageQuery.fromEmptyToken(10);
    var airportCode = repositoryTestUtil.getRegionEntity().airportCode();
    RackModel rackModel = repositoryTestUtil.createAndGetComputeRackModel();
    // Create RackAsset
    Asset rackAsset =
        repositoryTestUtil.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(regionEntity.id())
                .catalogPartId(
                    repositoryTestUtil.createCatalogPart("partnumber", "partnumber").id())
                .serialNumber("serialNumber")
                .build());

    var deviceModel =
        deviceModelService.createDeviceModel(
            airportCode,
            "patch-e2-2c-compute",
            rackModel.rackPosition().rackNumber(),
            "displayName",
            "elevation",
            "Switch",
            "deviceOpn");

    // findAllWithOptionalFilters empty
    DeviceInstanceFilter filter = new DeviceInstanceFilter(region, null, null, null, null, null);
    var emptyList = deviceInstanceService.listDeviceInstances(filter, pageQuery);
    assertTrue(emptyList.getResults().isEmpty());
    Exception exception =
        assertThrows(
            RenderableException.class,
            () -> deviceInstanceAdminService.createDeviceInstance(region, deviceModel.id()));
    assertTrue(exception.getMessage().contains("No rack instance found for the rack model"));
  }

  @Test
  void testCreatedDeviceInstanceThrowsExceptionWhenCorrespondingDeviceAssetNotFound() {
    RegionEntity regionEntity = repositoryTestUtil.getRegionEntity();
    var region = regionEntity.airportCode();
    var pageQuery = PageQuery.fromEmptyToken(10);
    var airportCode = repositoryTestUtil.getRegionEntity().airportCode();
    RackModel rackModel = repositoryTestUtil.createAndGetComputeRackModel();
    // Create RackAsset
    Asset rackAsset =
        repositoryTestUtil.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(regionEntity.id())
                .catalogPartId(
                    repositoryTestUtil.createCatalogPart("partnumber", "partnumber").id())
                .serialNumber("serialNumber")
                .build());

    repositoryTestUtil.createRackInstance(regionEntity, rackAsset, rackModel);
    var deviceModel =
        deviceModelService.createDeviceModel(
            airportCode,
            "patch-e2-2c-compute",
            rackModel.rackPosition().rackNumber(),
            "displayName",
            "elevation",
            "Switch",
            "deviceOpn");

    // findAllWithOptionalFilters empty
    DeviceInstanceFilter filter = new DeviceInstanceFilter(region, null, null, null, null, null);
    var emptyList = deviceInstanceService.listDeviceInstances(filter, pageQuery);
    assertTrue(emptyList.getResults().isEmpty());

    Exception exception =
        assertThrows(
            RenderableException.class,
            () -> deviceInstanceAdminService.createDeviceInstance(region, deviceModel.id()));
    assertTrue(exception.getMessage().contains("No rack asset found in the elevation"));
  }

  @Test
  void testSoftDeleteDeviceInstance() {
    RegionEntity regionEntity = repositoryTestUtil.getRegionEntity();
    var region = regionEntity.airportCode();
    var airportCode = repositoryTestUtil.getRegionEntity().airportCode();
    RackModel rackModel = repositoryTestUtil.createAndGetComputeRackModel();
    // Create RackAsset
    Asset rackAsset =
        repositoryTestUtil.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(regionEntity.id())
                .catalogPartId(
                    repositoryTestUtil.createCatalogPart("partnumber", "partnumber").id())
                .serialNumber("serialNumber")
                .build());

    repositoryTestUtil.createRackInstance(regionEntity, rackAsset, rackModel);
    var deviceModel =
        deviceModelService.createDeviceModel(
            airportCode,
            "patch-e2-2c-compute",
            rackModel.rackPosition().rackNumber(),
            "displayName",
            "elevation",
            "Switch",
            "deviceOpn");

    repositoryTestUtil.createAsset(
        AssetCreationDetailsBuilder.builder()
            .regionId(regionEntity.id())
            .catalogPartId(repositoryTestUtil.createCatalogPart("partnumber", "partnumber").id())
            .serialNumber("serial")
            .elevation("elevation")
            .parentAssetId(rackAsset.id())
            .build());
    // create
    var created = deviceInstanceAdminService.createDeviceInstance(region, deviceModel.id());
    assertNotNull(created);
    assertNotNull(created.timeCreated());
    assertEquals(created.timeCreated(), created.timeUpdated());
    assertEquals(0L, created.version());

    // get
    DeviceInstanceDetails fetchedDeviceInstance =
        deviceInstanceService.getDeviceInstance(region, RmcId.fromString(created.id()));
    assertEquals(created, fetchedDeviceInstance);

    deviceInstanceAdminService.softDeleteDeviceInstance(
        region, RmcId.fromString(created.id()), null);

    DeviceInstanceDetails deletedDeviceInstance =
        deviceInstanceService.getDeviceInstance(region, RmcId.fromString(created.id()));
    assertEquals(deletedDeviceInstance.lifecycleState(), "DELETED");
  }
}
