package com.oracle.oci.rmc.dcms.impl.dal.inventory;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.oracle.oci.rmc.auditevent.dal.AuditEvent;
import com.oracle.oci.rmc.auditevent.dal.AuditEventRepository;
import com.oracle.oci.rmc.auditevent.model.EventStatus;
import com.oracle.oci.rmc.auditevent.util.InventoryAuditEventConstants;
import com.oracle.oci.rmc.catalog.api.model.entity.Part;
import com.oracle.oci.rmc.dal.jdbc.DataSourceConstants;
import com.oracle.oci.rmc.dal.jdbc.H2Repository;
import com.oracle.oci.rmc.dal.jdbc.OracleRepository;
import com.oracle.oci.rmc.dcms.api.model.entity.AssetLocation;
import com.oracle.oci.rmc.dcms.api.model.entity.AssetLocationType;
import com.oracle.oci.rmc.dcms.api.model.entity.RackInstance;
import com.oracle.oci.rmc.dcms.api.model.entity.RackInstanceDetails;
import com.oracle.oci.rmc.dcms.api.model.entity.RackModel;
import com.oracle.oci.rmc.dcms.api.model.entity.RegionEntity;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.Asset;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetAdditionalProperties;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetAdditionalPropertiesBuilder;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetCreationDetailsBuilder;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetLifecycleState;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetReservation;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetSubInventory;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetTransfer;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetUpdateDetailsBuilder;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.MacAddress;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.MacAddressBuilder;
import com.oracle.oci.rmc.dcms.api.service.RegionService;
import com.oracle.oci.rmc.dcms.impl.dal.inventory.asset.AssetRepository;
import com.oracle.oci.rmc.dcms.impl.dal.testutil.DcmsRepositoryTestUtil;
import com.oracle.oci.rmc.dcms.impl.util.CreateEntityTestHelper;
import com.oracle.oci.rmc.model.RmcId;
import com.oracle.oci.rmc.model.RmcIdType;
import io.micronaut.context.annotation.Requires;
import io.micronaut.core.annotation.Nullable;
import io.micronaut.data.annotation.Query;
import io.micronaut.data.exceptions.DataAccessException;
import io.micronaut.data.repository.CrudRepository;
import io.micronaut.test.annotation.TransactionMode;
import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import jakarta.transaction.Transactional;
import java.sql.SQLException;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@MicronautTest(transactionMode = TransactionMode.SINGLE_TRANSACTION)
@Requires(property = "database.type", value = "ORACLE")
public class AssetRepositoryTest {

  private static final Logger LOG = LoggerFactory.getLogger(AssetRepositoryTest.class);
  public static final String TEST_USER = "test-user";
  private final DcmsRepositoryTestUtil util;
  private final TestAssetRepository repository;
  private final RegionService regionService;
  private final CreateEntityTestHelper createEntityTestHelper;
  private final AssetRepository assetRepository;
  private final AuditEventRepository auditEventRepository;

  public AssetRepositoryTest(
      DcmsRepositoryTestUtil util,
      TestAssetRepository testAssetRepository,
      RegionService regionService,
      CreateEntityTestHelper createEntityTestHelper,
      AssetRepository assetRepository,
      AuditEventRepository auditEventRepository) {
    this.util = util;
    this.repository = testAssetRepository;
    this.regionService = regionService;
    this.createEntityTestHelper = createEntityTestHelper;
    this.assetRepository = assetRepository;
    this.auditEventRepository = auditEventRepository;
  }

  @AfterEach
  void cleanup() {
    auditEventRepository.deleteByEventName(InventoryAuditEventConstants.CREATE_ASSET);
    auditEventRepository.deleteByEventName(InventoryAuditEventConstants.UPDATE_ASSET);
    auditEventRepository.deleteByEventName(InventoryAuditEventConstants.DELETE_ASSET);
    auditEventRepository.deleteByEventName(InventoryAuditEventConstants.MOVE_ASSET_TO_RACK);
    auditEventRepository.deleteByEventName(InventoryAuditEventConstants.MOVE_ASSET_TO_LOCATION);
    auditEventRepository.deleteByEventName(InventoryAuditEventConstants.MARK_MISSING_ASSET);
    auditEventRepository.deleteByEventName(InventoryAuditEventConstants.ACQUIRE_ASSET);
    auditEventRepository.deleteByEventName(InventoryAuditEventConstants.RETURN_ASSET);
    auditEventRepository.deleteByEventName(InventoryAuditEventConstants.DISPOSE_ASSET);
    auditEventRepository.deleteByEventName(InventoryAuditEventConstants.DESTROY_ASSET);
  }

  // CREATE ASSET TEST CASES
  @Test
  void testAssetCrudlSprocs() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    var region = createEntityTestHelper.createRegionEntity();
    String airportCode = region.airportCode();
    String regionId = regionService.getRegionByAirportCodeOrFail(airportCode).id();
    final String oraclePartNumber = "7338187";
    final String manufacturerPartNumber = "35096952";
    util.createCatalogPart(oraclePartNumber, manufacturerPartNumber);

    // Test Create
    Optional<Asset> createdAsset =
        createAsset(
            regionId,
            "**********",
            oraclePartNumber,
            manufacturerPartNumber,
            null,
            null,
            "oracle-tag-xyz",
            "E2:7F:1C:A3:B8:D5",
            uniqueAuditCreatedById);
    assertTrue(createdAsset.isPresent());
    Asset createdAssetObj = createdAsset.get();
    assertEquals("**********", createdAssetObj.serialNumber());
    assertEquals(0, createdAssetObj.version());
    assertEquals("E2:7F:1C:A3:B8:D5", createdAssetObj.additionalProperties().macAddress());

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.CREATE_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.SUCCESS, auditEvents.get(0).eventResponse().eventStatus());

    // Test update
    RmcId assetId = createdAssetObj.id();
    String assetId1 = assetId.toString();
    assetRepository.updateAsset(
        RmcId.fromString(assetId1),
        regionId,
        AssetUpdateDetailsBuilder.builder()
            .serialNumber("HS00410765")
            .oracleTag(null)
            .isDisabled(true)
            .updatedBy(uniqueAuditCreatedById)
            .build(),
        0L);
    Optional<Asset> updatedAsset = assetRepository.findById(RmcId.fromString(assetId.toString()));
    assertTrue(updatedAsset.isPresent());
    Asset updatedAssetObj = updatedAsset.get();
    assertEquals("HS00410765", updatedAssetObj.serialNumber());
    assertEquals(1, updatedAssetObj.version());
    assertTrue(updatedAssetObj.isDisabled());

    // check audit
    auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.UPDATE_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.SUCCESS, auditEvents.get(0).eventResponse().eventStatus());

    // Test delete
    String assetId3 = assetId.toString();
    assetRepository.deleteAsset(
        RmcId.fromString(assetId3), regionId, String.valueOf(1L), uniqueAuditCreatedById);
    Optional<Asset> deletedAsset = assetRepository.findById(RmcId.fromString(assetId.toString()));
    assertTrue(deletedAsset.isPresent());
    Asset deletedAssetObj = deletedAsset.get();
    assertEquals(AssetLifecycleState.DELETED, deletedAssetObj.lifecycleState());
    assertEquals(2, deletedAssetObj.version());

    // check audit
    auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.DELETE_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.SUCCESS, auditEvents.get(0).eventResponse().eventStatus());

    var createException =
        assertThrows(
            RuntimeException.class,
            () -> {
              String assetId2 = assetId.toString();
              assetRepository.deleteAsset(
                  RmcId.fromString(assetId2), regionId, String.valueOf(2L), uniqueAuditCreatedById);
            });
    var sqlEx = (SQLException) createException.getCause();
    assertEquals(20001, sqlEx.getErrorCode());
    assertTrue(
        sqlEx
            .getMessage()
            .contains("Invalid state transition attempted on Asset: DELETED to DELETED"));

    // check audit
    auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.DELETE_ASSET);
    assertEquals(2, auditEvents.size());
    assertLatestAuditEvent(EventStatus.FAILED, InventoryAuditEventConstants.DELETE_ASSET);

    // Test asset with invalid asset location
    AssetLocation alDestruction = util.createAssetLocation(regionId, AssetLocationType.DESTRUCTION);
    var createException2 =
        assertThrows(
            RuntimeException.class,
            () ->
                createAsset(
                    regionId,
                    "**********",
                    oraclePartNumber,
                    manufacturerPartNumber,
                    alDestruction.id(),
                    null,
                    "oracle-tag-xyz",
                    "E2:7F:1C:A3:B8:D5",
                    uniqueAuditCreatedById));
    var sqlEx2 = (SQLException) createException2.getCause();
    assertEquals(20001, sqlEx2.getErrorCode());
    assertTrue(
        sqlEx2
            .getMessage()
            .contains(
                "Asset cannot be created in asset location type: "
                    + AssetLocationType.DESTRUCTION.toString()));

    // check audit
    auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.CREATE_ASSET);
    assertEquals(2, auditEvents.size());
    assertLatestAuditEvent(EventStatus.FAILED, InventoryAuditEventConstants.CREATE_ASSET);
  }

  @Test
  void testUpdateAssetWithChildAssetsFail() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    RegionEntity region = util.createRegion();
    AssetLocation location = util.createAssetLocation(region.id());

    final Asset created1 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .locationId(location.id())
                .serialNumber("123")
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .build());
    final Asset created2 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .locationId(location.id())
                .serialNumber("234")
                .parentAssetId(created1.id())
                .build());

    // Test update parent asset
    var ex =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.updateAsset(
                    created1.id(),
                    created1.regionId(),
                    AssetUpdateDetailsBuilder.builder()
                        .lifecycleState(AssetLifecycleState.MISSING)
                        .updatedBy(uniqueAuditCreatedById)
                        .build(),
                    0L));
    var sqlEx = (SQLException) ex.getCause();
    assertEquals(20001, sqlEx.getErrorCode());
    assertTrue(sqlEx.getMessage().contains("Please use mark_missing_asset"));

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.UPDATE_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.FAILED, auditEvents.get(0).eventResponse().eventStatus());

    // make sure they are still in original lifecycle state, no operation occurred
    assertEquals(
        created1.lifecycleState(), repository.findById(created1.id()).get().lifecycleState());

    // Test update child asset
    var ex1 =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.updateAsset(
                    created2.id(),
                    created2.regionId(),
                    AssetUpdateDetailsBuilder.builder()
                        .lifecycleState(AssetLifecycleState.AVAILABLE)
                        .updatedBy(uniqueAuditCreatedById)
                        .build(),
                    0L));
    var sqlEx1 = (SQLException) ex1.getCause();
    assertEquals(20001, sqlEx1.getErrorCode());
    assertTrue(
        sqlEx1.getMessage().contains("Invalid state transition attempted on Asset with parent id"));

    // check audit
    auditEvents =
        createEntityTestHelper
            .listAuditEventsByUpdatedByAndEventName(
                uniqueAuditCreatedById, InventoryAuditEventConstants.UPDATE_ASSET)
            .stream()
            .filter(
                auditEvent -> auditEvent.eventResponse().eventStatus().equals(EventStatus.FAILED))
            .toList();
    assertEquals(2, auditEvents.size());

    // make sure they are still in original lifecycle state, no operation occurred
    assertEquals(
        created2.lifecycleState(), repository.findById(created2.id()).get().lifecycleState());
  }

  @Test
  void testDeleteAssetWithChildAssets() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    RegionEntity region = util.createRegion();
    AssetLocation location = util.createAssetLocation(region.id());
    final Asset created1 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .locationId(location.id())
                .serialNumber("123")
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .build());
    final Asset created2 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .locationId(location.id())
                .serialNumber("234")
                .parentAssetId(created1.id())
                .build());

    // Test delete child asset
    var ex =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.deleteAsset(
                    created2.id(),
                    created2.regionId(),
                    String.valueOf(0L),
                    uniqueAuditCreatedById));
    var sqlEx = (SQLException) ex.getCause();
    assertEquals(20001, sqlEx.getErrorCode());
    assertTrue(sqlEx.getMessage().contains("Cannot delete child asset"));

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.DELETE_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.FAILED, auditEvents.get(0).eventResponse().eventStatus());

    // make sure they are still in original lifecycle state, no operation occurred
    assertEquals(
        created2.lifecycleState(), repository.findById(created2.id()).get().lifecycleState());

    // Delete parent asset
    assetRepository.deleteAsset(
        created1.id(), created1.regionId(), String.valueOf(0L), uniqueAuditCreatedById);
    // check audit
    auditEvents =
        createEntityTestHelper
            .listAuditEventsByUpdatedByAndEventName(
                uniqueAuditCreatedById, InventoryAuditEventConstants.DELETE_ASSET)
            .stream()
            .filter(
                auditEvent -> auditEvent.eventResponse().eventStatus().equals(EventStatus.SUCCESS))
            .toList();
    assertEquals(2, auditEvents.size());

    // Check asset state
    Optional<Asset> updatedChildAsset = assetRepository.findById(created2.id());
    assertTrue(updatedChildAsset.isPresent());
    assertEquals(AssetLifecycleState.DELETED, updatedChildAsset.get().lifecycleState());
    Optional<Asset> updatedAsset = assetRepository.findById(created1.id());
    assertTrue(updatedAsset.isPresent());
    Asset updatedAssetObj = updatedAsset.get();
    assertEquals(AssetLifecycleState.DELETED, updatedAssetObj.lifecycleState());
    assertEquals(1, updatedAssetObj.version());
  }

  @Test
  void testUpdateAssetWithBurnInMacAddresses() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    var region = createEntityTestHelper.createRegionEntity();
    String airportCode = region.airportCode();
    String regionId = regionService.getRegionByAirportCodeOrFail(airportCode).id();
    final String oraclePartNumber = "7338187";
    final String manufacturerPartNumber = "35096952";
    util.createCatalogPart(oraclePartNumber, manufacturerPartNumber);

    // Test Create
    Optional<Asset> createdAsset =
        createAsset(
            regionId,
            "**********",
            oraclePartNumber,
            manufacturerPartNumber,
            null,
            null,
            "oracle-tag-xyz",
            "E2:7F:1C:A3:B8:D5",
            uniqueAuditCreatedById);
    assertTrue(createdAsset.isPresent());
    Asset createdAssetObj = createdAsset.get();
    assertEquals("**********", createdAssetObj.serialNumber());
    assertEquals(0, createdAssetObj.version());
    assertEquals("E2:7F:1C:A3:B8:D5", createdAssetObj.additionalProperties().macAddress());

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.CREATE_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.SUCCESS, auditEvents.get(0).eventResponse().eventStatus());

    // Test update
    RmcId assetId = createdAssetObj.id();
    String assetId1 = assetId.toString();
    List<MacAddress> burnInMacAddresses =
        List.of(
            MacAddressBuilder.builder().macAddress("address1").ipAddress("0.0.0.0").build(),
            MacAddressBuilder.builder().macAddress("address2").ipAddress("*******").build());
    assetRepository.updateAsset(
        RmcId.fromString(assetId1),
        regionId,
        AssetUpdateDetailsBuilder.builder()
            .burnInMacAddresses(burnInMacAddresses)
            .oracleTag(null)
            .updatedBy(uniqueAuditCreatedById)
            .build(),
        0L);
    Optional<Asset> updatedAsset = assetRepository.findById(RmcId.fromString(assetId.toString()));
    assertTrue(updatedAsset.isPresent());
    Asset updatedAssetObj = updatedAsset.get();
    assertEquals(2, updatedAssetObj.burnInMacAddresses().size());
    assertEquals(1, updatedAssetObj.version());
  }

  @Test
  void testCreateAssetWithoutSerialNumber() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Serial Number: required parameter
    var region = createEntityTestHelper.createRegionEntity();
    String airportCode = region.airportCode();
    String regionId = regionService.getRegionByAirportCodeOrFail(airportCode).id();
    final String oraclePartNumber = "7338187";
    final String manufacturerPartNumber = "35096952";
    util.createCatalogPart(oraclePartNumber, manufacturerPartNumber);

    assertThrows(
        IllegalArgumentException.class,
        () ->
            createAsset(
                regionId,
                null,
                "7338187",
                "35096952",
                null,
                null,
                "oracle-tag-xyz",
                "",
                uniqueAuditCreatedById));
  }

  @Test
  void testCreateAssetWithoutRegion() {
    final String oraclePartNumber = "7338187";
    final String manufacturerPartNumber = "35096952";
    util.createCatalogPart(oraclePartNumber, manufacturerPartNumber);
    assertThrows(
        IllegalArgumentException.class,
        () ->
            createAsset(
                null,
                "SerialNumber",
                "7338187",
                "35096952",
                null,
                null,
                "oracle-tag-xyz",
                "",
                TEST_USER));
  }

  @Test
  void testCreateAssetWithInvalidLocation() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    var region = createEntityTestHelper.createRegionEntity();
    String airportCode = region.airportCode();
    String regionId = regionService.getRegionByAirportCodeOrFail(airportCode).id();
    final String oraclePartNumber = "7338187";
    final String manufacturerPartNumber = "35096952";
    util.createCatalogPart(oraclePartNumber, manufacturerPartNumber);
    var createException =
        assertThrows(
            RuntimeException.class,
            () ->
                createAsset(
                    regionId,
                    "SerialNumber",
                    "7338187",
                    "35096952",
                    RmcId.generate(RmcIdType.Inventory.ASSET_LOCATION), // unknown location ID
                    null,
                    "oracle-tag-xyz",
                    "",
                    uniqueAuditCreatedById));
    var sqlEx = (SQLException) createException.getCause();
    LOG.info("Validating exception: {}", sqlEx.getMessage());
    assertEquals(20001, sqlEx.getErrorCode());

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.CREATE_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.FAILED, auditEvents.get(0).eventResponse().eventStatus());
  }

  @Test
  void testCreateAssetWithInvalidParentAssetId() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    var region = createEntityTestHelper.createRegionEntity();
    String airportCode = region.airportCode();
    String regionId = regionService.getRegionByAirportCodeOrFail(airportCode).id();
    final String oraclePartNumber = "7338187";
    final String manufacturerPartNumber = "35096952";
    util.createCatalogPart(oraclePartNumber, manufacturerPartNumber);
    var createException =
        assertThrows(
            RuntimeException.class,
            () ->
                createAsset(
                    regionId,
                    "SerialNumber",
                    oraclePartNumber,
                    manufacturerPartNumber,
                    null,
                    RmcId.generate(RmcIdType.Inventory.ASSET), // invalid asset id
                    "oracle-tag-xyz",
                    "",
                    uniqueAuditCreatedById));
    var sqlEx = (SQLException) createException.getCause();
    assertEquals(20001, sqlEx.getErrorCode());
    assertTrue(sqlEx.getMessage().contains("Parent asset not found."));

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.CREATE_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.FAILED, auditEvents.get(0).eventResponse().eventStatus());
  }

  @Test
  void testCreateAssetWithInvalidPartNumber() {
    var region = createEntityTestHelper.createRegionEntity();
    String airportCode = region.airportCode();
    String regionId = regionService.getRegionByAirportCodeOrFail(airportCode).id();
    var createException =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.saveAsset(
                    AssetCreationDetailsBuilder.builder()
                        .regionId(regionId)
                        .serialNumber("SerialNumber")
                        .catalogPartId(RmcId.generate(RmcIdType.Catalog.PART)) // invalid part
                        .oracleTag("oracle-tag-xyz")
                        .lifecycleState(AssetLifecycleState.AVAILABLE)
                        .subInventory(AssetSubInventory.GENERAL_SPARES)
                        .additionalProperties(
                            AssetAdditionalPropertiesBuilder.builder().macAddress("").build())
                        .createdBy("AssetRepositoryTest.createAsset()")
                        .build()));

    var sqlEx = (SQLException) createException.getCause();
    LOG.info("Validating exception: {}", sqlEx.getMessage());
    assertEquals(20001, sqlEx.getErrorCode());
    assertThat(sqlEx.getMessage()).contains("No part record found matching given part id.");
  }

  // @Test - TODO uncomment after addressing tech debt IDNA-2454
  void testCreateAssetWithDuplicateAssets() {
    var region = createEntityTestHelper.createRegionEntity();
    String airportCode = region.airportCode();
    String regionId = regionService.getRegionByAirportCodeOrFail(airportCode).id();
    final String oraclePartNumber = "7338187";
    final String manufacturerPartNumber = "35096952";
    util.createCatalogPart(oraclePartNumber, manufacturerPartNumber);

    // Test Create
    Optional<Asset> createdAsset =
        createAsset(
            regionId,
            "**********",
            oraclePartNumber,
            manufacturerPartNumber,
            null,
            null,
            "oracle-tag-xyz",
            "E2:7F:1C:A3:B8:D5",
            TEST_USER);
    assertTrue(createdAsset.isPresent());

    var createException =
        assertThrows(
            RuntimeException.class,
            () ->
                createAsset(
                    regionId,
                    "**********",
                    oraclePartNumber,
                    manufacturerPartNumber,
                    null,
                    null,
                    "oracle-tag-xyz",
                    "E2:7F:1C:A3:B8:D5",
                    TEST_USER));
    var sqlEx = (SQLException) createException.getCause();
    assertEquals(00001, sqlEx.getErrorCode());
    assertTrue(sqlEx.getMessage().contains("unique constraint (ADMIN.ASSET_UNIQUENESS) violated"));
  }

  // UPDATE ASSET TEST CASES
  @Test
  void testUpdateAssetSerialNumber() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    var region = createEntityTestHelper.createRegionEntity();
    String airportCode = region.airportCode();
    String regionId = regionService.getRegionByAirportCodeOrFail(airportCode).id();
    final String oraclePartNumber = "7338187";
    final String manufacturerPartNumber = "35096952";
    util.createCatalogPart(oraclePartNumber, manufacturerPartNumber);

    // Test Create
    Optional<Asset> createdAsset =
        createAsset(
            regionId,
            "**********",
            oraclePartNumber,
            manufacturerPartNumber,
            null,
            null,
            "oracle-tag-xyz",
            "E2:7F:1C:A3:B8:D5",
            uniqueAuditCreatedById);
    assertTrue(createdAsset.isPresent());
    Asset createdAssetObj = createdAsset.get();
    assertEquals("**********", createdAssetObj.serialNumber());
    assertEquals(AssetLifecycleState.AVAILABLE, createdAssetObj.lifecycleState());

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.CREATE_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.SUCCESS, auditEvents.get(0).eventResponse().eventStatus());

    // Assert
    String assetId = createdAssetObj.id().toString();
    assetRepository.updateAsset(
        RmcId.fromString(assetId),
        regionId,
        AssetUpdateDetailsBuilder.builder()
            .serialNumber("SS00410765")
            .oracleTag(null)
            .updatedBy(uniqueAuditCreatedById)
            .build(),
        0L);
    Optional<Asset> updatedAsset =
        assetRepository.findById(RmcId.fromString(createdAssetObj.id().toString()));
    assertTrue(updatedAsset.isPresent());
    Asset updatedAssetObj = updatedAsset.get();
    assertEquals("SS00410765", updatedAssetObj.serialNumber());
    assertEquals(1, updatedAssetObj.version());

    // check audit
    auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.UPDATE_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.SUCCESS, auditEvents.get(0).eventResponse().eventStatus());
  }

  @Test
  void testAssetCreationLifecycleState() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    var region = createEntityTestHelper.createRegionEntity();
    String airportCode = region.airportCode();
    String regionId = regionService.getRegionByAirportCodeOrFail(airportCode).id();
    String oraclePartNumber = "7338189";
    String manufacturerPartNumber = "35096959";
    // create first part
    util.createCatalogPart(oraclePartNumber, manufacturerPartNumber);

    // Creating parent asset
    Optional<Asset> createdAsset =
        createAsset(
            regionId,
            "AK00410769",
            oraclePartNumber,
            manufacturerPartNumber,
            null,
            null,
            "oracle-tag-xyz",
            "",
            uniqueAuditCreatedById);
    assertTrue(createdAsset.isPresent());

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.CREATE_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.SUCCESS, auditEvents.get(0).eventResponse().eventStatus());

    Asset createdAssetObj = createdAsset.get();

    Optional<Asset> updatedAsset = assetRepository.findById(createdAssetObj.id());
    assertTrue(updatedAsset.isPresent());
    Asset updatedAssetObj = updatedAsset.get();
    assertEquals(AssetLifecycleState.AVAILABLE, updatedAssetObj.lifecycleState());

    // create second part
    String oraclePartNumber2 = "8187339";
    String manufacturerPartNumber2 = "69350959";
    util.createCatalogPart(oraclePartNumber2, manufacturerPartNumber2);

    // Set parentAssetId to assetId1
    Optional<Asset> createdAsset2 =
        createAsset(
            regionId,
            "SK00410769",
            oraclePartNumber2,
            manufacturerPartNumber2,
            null,
            createdAssetObj.id(),
            "oracle-tag-xyz",
            "",
            uniqueAuditCreatedById);
    assertTrue(createdAsset2.isPresent());

    // check audit
    auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.CREATE_ASSET);
    assertEquals(2, auditEvents.size());
    assertLatestAuditEvent(EventStatus.SUCCESS, InventoryAuditEventConstants.CREATE_ASSET);

    // Child Asset LifecycleState is INSTALLED.
    Asset createdAssetObj2 = createdAsset2.get();
    assertEquals(updatedAssetObj.rackInstanceId(), createdAssetObj2.rackInstanceId());
    assertEquals(AssetLifecycleState.INSTALLED, createdAssetObj2.lifecycleState());
  }

  @Test
  void testAssetStateMoveFromPendingReturnToAvailableHappyPath() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    var region = createEntityTestHelper.createRegionEntity();
    String airportCode = region.airportCode();
    String regionId = regionService.getRegionByAirportCodeOrFail(airportCode).id();
    final String oraclePartNumber = "7338187";
    final String manufacturerPartNumber = "35096952";

    AssetLocation location1 = util.createAssetLocation(region.id());
    RmcId rackInstanceId = RmcId.generate(RmcIdType.DCMS.DEVICE_INSTANCE);

    var parentAsset =
        createAssetAllParams(
                regionId,
                "**********",
                oraclePartNumber,
                manufacturerPartNumber,
                rackInstanceId,
                location1.id(),
                null,
                AssetLifecycleState.INSTALLED,
                "oracle-tag-xyz",
                "E2:7F:1C:A3:B8:D5",
                "1",
                uniqueAuditCreatedById)
            .get();

    assertEquals(AssetLifecycleState.INSTALLED, parentAsset.lifecycleState());
    assertEquals(location1.id(), parentAsset.locationId());
    repository.updateAssetLifecycle(
        parentAsset.id(), AssetLifecycleState.PENDING_RETURN.toString());
    var fetchedParentAsset = assetRepository.findById(parentAsset.id()).get();
    assertEquals(AssetLifecycleState.PENDING_RETURN, fetchedParentAsset.lifecycleState());

    final var childAsset =
        createAssetwithLifecycleState(
                regionId,
                "SK00410765",
                "8187337",
                "69350952",
                location1.id(),
                parentAsset.id(),
                AssetLifecycleState.INSTALLED,
                "oracle-tag-xyz",
                "E2:7F:1C:A3:B8:D5",
                uniqueAuditCreatedById)
            .get();

    assertEquals(AssetLifecycleState.INSTALLED, childAsset.lifecycleState());
    AssetLocation location2 = util.createAssetLocation(region.id(), AssetLocationType.SPARE);
    repository.updateAssetLifecycle(childAsset.id(), AssetLifecycleState.PENDING_RETURN.toString());
    var fetchedChildAsset = assetRepository.findById(childAsset.id()).get();
    assertEquals(AssetLifecycleState.PENDING_RETURN, fetchedChildAsset.lifecycleState());

    String lifecycleState = AssetLifecycleState.AVAILABLE.toString();
    assetRepository.moveAssetToLocation(
        parentAsset.regionId(),
        parentAsset.id(),
        parentAsset.version(),
        location2.id(),
        lifecycleState,
        uniqueAuditCreatedById,
        parentAsset.subInventory().toString());
    fetchedParentAsset = assetRepository.findById(parentAsset.id()).get();
    assertEquals(AssetLifecycleState.AVAILABLE, fetchedParentAsset.lifecycleState());
    fetchedChildAsset = assetRepository.findById(childAsset.id()).get();
    assertEquals(AssetLifecycleState.INSTALLED, fetchedChildAsset.lifecycleState());
  }

  @Test
  void testAssetStateMoveFromPendingReturnToAvailableChildStateNotInstalled() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    var region = createEntityTestHelper.createRegionEntity();
    String airportCode = region.airportCode();
    String regionId = regionService.getRegionByAirportCodeOrFail(airportCode).id();
    final String oraclePartNumber = "7338187";
    final String manufacturerPartNumber = "35096952";

    AssetLocation location1 = util.createAssetLocation(region.id());
    RmcId rackInstanceId = RmcId.generate(RmcIdType.DCMS.DEVICE_INSTANCE);

    var parentAsset =
        createAssetAllParams(
                regionId,
                "**********",
                oraclePartNumber,
                manufacturerPartNumber,
                rackInstanceId,
                location1.id(),
                null,
                AssetLifecycleState.PENDING_RETURN,
                "oracle-tag-xyz",
                "E2:7F:1C:A3:B8:D5",
                "1",
                uniqueAuditCreatedById)
            .get();

    assertEquals(AssetLifecycleState.PENDING_RETURN, parentAsset.lifecycleState());
    assertEquals(location1.id(), parentAsset.locationId());

    final var childAsset =
        createAssetwithLifecycleState(
                regionId,
                "SK00410765",
                "8187337",
                "69350952",
                location1.id(),
                parentAsset.id(),
                AssetLifecycleState.INSTALLED,
                "oracle-tag-xyz",
                "E2:7F:1C:A3:B8:D5",
                uniqueAuditCreatedById)
            .get();

    AssetLocation location2 = util.createAssetLocation(region.id(), AssetLocationType.SPARE);

    String lifecycleState = AssetLifecycleState.AVAILABLE.toString();
    repository.updateAssetLifecycle(childAsset.id(), AssetLifecycleState.PENDING_RETURN.toString());

    assetRepository.moveAssetToLocation(
        parentAsset.regionId(),
        parentAsset.id(),
        parentAsset.version(),
        location2.id(),
        lifecycleState,
        uniqueAuditCreatedById,
        parentAsset.subInventory().toString());
    var fetchedParentAsset = assetRepository.findById(parentAsset.id()).get();
    assertEquals(AssetLifecycleState.AVAILABLE, fetchedParentAsset.lifecycleState());
    var fetchedChildAsset = assetRepository.findById(childAsset.id()).get();
    assertEquals(AssetLifecycleState.INSTALLED, fetchedChildAsset.lifecycleState());
  }

  @Test
  void testChildAssetRackInstanceId() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    var region = createEntityTestHelper.createRegionEntity();
    String airportCode = region.airportCode();
    String regionId = regionService.getRegionByAirportCodeOrFail(airportCode).id();
    String oraclePartNumber = "7338187";
    String manufacturerPartNumber = "35096952";
    // create first part
    util.createCatalogPart(oraclePartNumber, manufacturerPartNumber);

    // Creating asset and setting up rackInstance id
    Optional<Asset> createdAsset =
        createAsset(
            regionId,
            "**********",
            oraclePartNumber,
            manufacturerPartNumber,
            null,
            null,
            "oracle-tag-xyz",
            "",
            uniqueAuditCreatedById);
    assertTrue(createdAsset.isPresent());

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.CREATE_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.SUCCESS, auditEvents.get(0).eventResponse().eventStatus());

    Asset createdAssetObj = createdAsset.get();
    RmcId rackId = RmcId.generate(RmcIdType.DCMS.DEVICE_INSTANCE);
    assetRepository.moveAssetToRack(
        createdAssetObj.regionId(),
        createdAssetObj.id(),
        createdAssetObj.version(),
        rackId,
        "elevation0",
        AssetLifecycleState.INSTALLED.toString(),
        uniqueAuditCreatedById);

    // check audit
    auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.MOVE_ASSET_TO_RACK);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.SUCCESS, auditEvents.get(0).eventResponse().eventStatus());

    Optional<Asset> updatedAsset = assetRepository.findById(createdAssetObj.id());
    assertTrue(updatedAsset.isPresent());
    Asset updatedAssetObj = updatedAsset.get();
    assertEquals(rackId, updatedAssetObj.rackInstanceId());
    // create second part
    String oraclePartNumber2 = "8187337";
    String manufacturerPartNumber2 = "69350952";
    util.createCatalogPart(oraclePartNumber2, manufacturerPartNumber2);

    // Set parentAssetId to assetId1
    Optional<Asset> createdAsset2 =
        createAsset(
            regionId,
            "SK00410765",
            oraclePartNumber2,
            manufacturerPartNumber2,
            null,
            createdAssetObj.id(),
            "oracle-tag-xyz",
            "",
            uniqueAuditCreatedById);
    assertTrue(createdAsset2.isPresent());

    // check audit
    auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.CREATE_ASSET);
    assertEquals(2, auditEvents.size());
    assertLatestAuditEvent(EventStatus.SUCCESS, InventoryAuditEventConstants.CREATE_ASSET);

    // Equal rack instance id's
    Asset createdAssetObj2 = createdAsset2.get();
    assertEquals(updatedAssetObj.rackInstanceId(), createdAssetObj2.rackInstanceId());
  }

  Optional<Asset> createAsset(
      String regionId,
      String serialNumber,
      String oraclePartNumber,
      String manufacturerPartNumber,
      RmcId locationId,
      RmcId parentAssetId,
      String oracleTag,
      String macAddress,
      String createdBy) {

    Part part = util.createCatalogPart(oraclePartNumber, manufacturerPartNumber);
    var defaultState =
        parentAssetId == null ? AssetLifecycleState.AVAILABLE : AssetLifecycleState.INSTALLED;

    AssetAdditionalProperties additionalProperties =
        macAddress == null
            ? null
            : AssetAdditionalPropertiesBuilder.builder().macAddress(macAddress).build();

    return Optional.of(
        assetRepository.saveAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(regionId)
                .serialNumber(serialNumber)
                .catalogPartId(part.id())
                .locationId(locationId)
                .parentAssetId(parentAssetId)
                .oracleTag(oracleTag)
                .lifecycleState(defaultState)
                .subInventory(AssetSubInventory.GENERAL_SPARES)
                .additionalProperties(additionalProperties)
                .createdBy(createdBy)
                .build()));
  }

  Optional<Asset> createAssetwithLifecycleState(
      String regionId,
      String serialNumber,
      String oraclePartNumber,
      String manufacturerPartNumber,
      RmcId locationId,
      RmcId parentAssetId,
      AssetLifecycleState lifecycleState,
      String oracleTag,
      String macAddress,
      String createdBy) {

    Part part = util.createCatalogPart(oraclePartNumber, manufacturerPartNumber);

    AssetAdditionalProperties additionalProperties =
        macAddress == null
            ? null
            : AssetAdditionalPropertiesBuilder.builder().macAddress(macAddress).build();

    return Optional.of(
        assetRepository.saveAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(regionId)
                .serialNumber(serialNumber)
                .catalogPartId(part.id())
                .locationId(locationId)
                .parentAssetId(parentAssetId)
                .oracleTag(oracleTag)
                .lifecycleState(lifecycleState)
                .subInventory(AssetSubInventory.GENERAL_SPARES)
                .additionalProperties(additionalProperties)
                .createdBy(createdBy)
                .build()));
  }

  Optional<Asset> createAssetAllParams(
      String regionId,
      String serialNumber,
      String oraclePartNumber,
      String manufacturerPartNumber,
      RmcId rackInstanceId,
      RmcId locationId,
      RmcId parentAssetId,
      AssetLifecycleState lifecycleState,
      String oracleTag,
      String macAddress,
      String elevation,
      String createdBy) {

    Part part = util.createCatalogPart(oraclePartNumber, manufacturerPartNumber);

    AssetAdditionalProperties additionalProperties =
        macAddress == null
            ? null
            : AssetAdditionalPropertiesBuilder.builder().macAddress(macAddress).build();

    return Optional.of(
        assetRepository.saveAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(regionId)
                .serialNumber(serialNumber)
                .catalogPartId(part.id())
                .locationId(locationId)
                .parentAssetId(parentAssetId)
                .oracleTag(oracleTag)
                .elevation(elevation)
                .rackInstanceId(rackInstanceId)
                .lifecycleState(lifecycleState)
                .subInventory(AssetSubInventory.GENERAL_SPARES)
                .additionalProperties(additionalProperties)
                .createdBy(createdBy)
                .build()));
  }

  void testUpdateAsset() {

    // Trying to update asset with a "Non-Available state (DELETED)"
    var region = createEntityTestHelper.createRegionEntity();
    String airportCode = region.airportCode();
    String regionId = regionService.getRegionByAirportCodeOrFail(airportCode).id();
    Optional<Asset> createdAsset =
        createAsset(
            regionId,
            "**********",
            "7338187",
            "35096952",
            null,
            null,
            "oracle-tag-xyz",
            "",
            TEST_USER);
    assertTrue(createdAsset.isPresent());
    RmcId assetId = createdAsset.get().id();

    String assetId3 = assetId.toString();
    assetRepository.deleteAsset(
        RmcId.fromString(assetId3), regionId, String.valueOf(0L), TEST_USER);

    var createException =
        assertThrows(
            RuntimeException.class,
            () -> {
              String assetId1 = assetId.toString();
              assetRepository.updateAsset(
                  RmcId.fromString(assetId1),
                  regionId,
                  AssetUpdateDetailsBuilder.builder()
                      .serialNumber("HS0092740")
                      .oracleTag(null)
                      .updatedBy(TEST_USER)
                      .build(),
                  1L);
            });
    var sqlEx = (SQLException) createException.getCause();
    assertEquals(20001, sqlEx.getErrorCode());
    assertTrue(
        sqlEx
            .getMessage()
            .contains(
                "Serial Number updates are only allowed when asset is in an available state"));

    // Updates not allowed when asset is already in rack
    Optional<Asset> createdAsset2 =
        createAsset(
            regionId,
            "SK00410765",
            "7338187",
            "35096952",
            null,
            null,
            "oracle-tag-xyz",
            null,
            TEST_USER);
    assertTrue(createdAsset2.isPresent());
    RmcId assetId2 = createdAsset2.get().id();

    Asset assetToUpdate =
        createdAsset2.get().withRackInstanceId(RmcId.generate(RmcIdType.DCMS.RACK_INSTANCE));
    assetRepository.updateAsset(
        assetToUpdate.id(),
        assetToUpdate.regionId(),
        AssetUpdateDetailsBuilder.builder()
            .serialNumber(assetToUpdate.serialNumber())
            .oracleTag(assetToUpdate.oracleTag())
            .updatedBy(TEST_USER)
            .build(),
        0L);

    var createException2 =
        assertThrows(
            RuntimeException.class,
            () -> {
              String assetId1 = assetId2.toString();
              assetRepository.updateAsset(
                  RmcId.fromString(assetId1),
                  regionId,
                  AssetUpdateDetailsBuilder.builder()
                      .serialNumber("**********")
                      .oracleTag(null)
                      .updatedBy(TEST_USER)
                      .build(),
                  2L);
            });
    var sqlEx2 = (SQLException) createException2.getCause();
    assertEquals(20001, sqlEx2.getErrorCode());
    assertTrue(sqlEx2.getMessage().contains("Failed to update because asset is already in rack"));
  }

  @Test
  void testIsValidStateTransition() {
    // Test a few state transitions

    // Transitions from AVAILABLE
    assertEquals(1, repository.isAssetStateTransitionValid("AVAILABLE", "ACQUIRED"));
    assertEquals(1, repository.isAssetStateTransitionValid("AVAILABLE", "MISSING"));
    assertEquals(1, repository.isAssetStateTransitionValid("AVAILABLE", "IN_TRANSIT"));
    assertEquals(1, repository.isAssetStateTransitionValid("AVAILABLE", "INSTALLED"));
    assertEquals(0, repository.isAssetStateTransitionValid("AVAILABLE", "DISPOSED"));
    assertEquals(0, repository.isAssetStateTransitionValid("AVAILABLE", "DESTROYED"));
    assertEquals(0, repository.isAssetStateTransitionValid("AVAILABLE", "RETURNED"));
    assertEquals(0, repository.isAssetStateTransitionValid("AVAILABLE", "PENDING_VALIDATION"));

    // Transitions from ACQUIRED
    assertEquals(1, repository.isAssetStateTransitionValid("ACQUIRED", "MISSING"));
    assertEquals(1, repository.isAssetStateTransitionValid("ACQUIRED", "INSTALLED"));
    assertEquals(0, repository.isAssetStateTransitionValid("ACQUIRED", "DELETED"));
    assertEquals(0, repository.isAssetStateTransitionValid("ACQUIRED", "DISPOSED"));

    // Transitions from INSTALLED
    assertEquals(1, repository.isAssetStateTransitionValid("INSTALLED", "FAILED"));
    assertEquals(1, repository.isAssetStateTransitionValid("INSTALLED", "PENDING_DESTRUCTION"));
    assertEquals(0, repository.isAssetStateTransitionValid("INSTALLED", "DELETED"));
    assertEquals(0, repository.isAssetStateTransitionValid("INSTALLED", "DISPOSED"));
    assertEquals(1, repository.isAssetStateTransitionValid("INSTALLED", "AVAILABLE"));

    // Transitions from IN_TRANSIT
    assertEquals(1, repository.isAssetStateTransitionValid("IN_TRANSIT", "MISSING"));
    assertEquals(1, repository.isAssetStateTransitionValid("IN_TRANSIT", "AVAILABLE"));
    assertEquals(1, repository.isAssetStateTransitionValid("IN_TRANSIT", "RECEIVED"));
    assertEquals(1, repository.isAssetStateTransitionValid("IN_TRANSIT", "RETURNED"));
    assertEquals(1, repository.isAssetStateTransitionValid("IN_TRANSIT", "DELETED"));
    assertEquals(0, repository.isAssetStateTransitionValid("IN_TRANSIT", "DISPOSED"));

    // Transitions from RECEIVED
    assertEquals(1, repository.isAssetStateTransitionValid("RECEIVED", "MISSING"));
    assertEquals(1, repository.isAssetStateTransitionValid("RECEIVED", "AVAILABLE"));
    assertEquals(1, repository.isAssetStateTransitionValid("RECEIVED", "INSTALLED"));
    assertEquals(1, repository.isAssetStateTransitionValid("RECEIVED", "DELETED"));
    assertEquals(0, repository.isAssetStateTransitionValid("RECEIVED", "DISPOSED"));

    // Transitions from PENDING_DISPOSAL
    assertEquals(1, repository.isAssetStateTransitionValid("PENDING_DISPOSAL", "DISPOSED"));
    assertEquals(1, repository.isAssetStateTransitionValid("PENDING_DISPOSAL", "MISSING"));
    assertEquals(0, repository.isAssetStateTransitionValid("PENDING_DISPOSAL", "DELETED"));
    assertEquals(0, repository.isAssetStateTransitionValid("PENDING_DISPOSAL", "DESTROYED"));

    // Transitions from PENDING_DESTRUCTION
    assertEquals(1, repository.isAssetStateTransitionValid("PENDING_DESTRUCTION", "DESTROYED"));
    assertEquals(1, repository.isAssetStateTransitionValid("PENDING_DESTRUCTION", "MISSING"));
    assertEquals(0, repository.isAssetStateTransitionValid("PENDING_DESTRUCTION", "DELETED"));
    assertEquals(0, repository.isAssetStateTransitionValid("PENDING_DESTRUCTION", "DISPOSED"));

    // Transitions from PENDING_RETURN
    assertEquals(1, repository.isAssetStateTransitionValid("PENDING_RETURN", "RETURNED"));
    assertEquals(1, repository.isAssetStateTransitionValid("PENDING_RETURN", "MISSING"));
    assertEquals(0, repository.isAssetStateTransitionValid("PENDING_RETURN", "DELETED"));
    assertEquals(0, repository.isAssetStateTransitionValid("PENDING_RETURN", "DISPOSED"));

    // Transitions from PENDING_VALIDATION
    assertEquals(1, repository.isAssetStateTransitionValid("PENDING_VALIDATION", "AVAILABLE"));
    assertEquals(
        1, repository.isAssetStateTransitionValid("PENDING_VALIDATION", "PENDING_DISPOSAL"));
    assertEquals(0, repository.isAssetStateTransitionValid("PENDING_VALIDATION", "DESTROYED"));
    assertEquals(0, repository.isAssetStateTransitionValid("PENDING_VALIDATION", "DISPOSED"));

    // Transitions from MISSING
    assertEquals(1, repository.isAssetStateTransitionValid("MISSING", "AVAILABLE"));
    assertEquals(1, repository.isAssetStateTransitionValid("MISSING", "ACQUIRED"));
    assertEquals(0, repository.isAssetStateTransitionValid("MISSING", "DESTROYED"));
    assertEquals(0, repository.isAssetStateTransitionValid("MISSING", "DISPOSED"));

    // Transitions from FAILED
    assertEquals(1, repository.isAssetStateTransitionValid("FAILED", "AVAILABLE"));
    assertEquals(1, repository.isAssetStateTransitionValid("FAILED", "PENDING_DESTRUCTION"));
    assertEquals(1, repository.isAssetStateTransitionValid("FAILED", "PENDING_DISPOSAL"));
    assertEquals(1, repository.isAssetStateTransitionValid("FAILED", "PENDING_RETURN"));
    assertEquals(0, repository.isAssetStateTransitionValid("FAILED", "DESTROYED"));
    assertEquals(0, repository.isAssetStateTransitionValid("FAILED", "DISPOSED"));

    // Terminal states
    assertEquals(0, repository.isAssetStateTransitionValid("DELETED", "AVAILABLE"));
    assertEquals(0, repository.isAssetStateTransitionValid("RETURNED", "MISSING"));
    assertEquals(0, repository.isAssetStateTransitionValid("DISPOSED", "FAILED"));
    assertEquals(0, repository.isAssetStateTransitionValid("DESTROYED", "DELETED"));

    // Invalid states
    assertEquals(0, repository.isAssetStateTransitionValid("UNKNOWN", "AVAILABLE"));
    assertEquals(0, repository.isAssetStateTransitionValid("RETURNED", "UNKNOWN"));
    assertEquals(0, repository.isAssetStateTransitionValid("UNKNOWN", "UNKNOWN"));
  }

  // MARK MISSING TEST CASES

  @Test
  void testMarkMissingAsset() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    Asset created =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .build());
    Asset retrieved = repository.findById(created.id()).get();

    // Act
    assetRepository.markMissingAsset(
        retrieved.regionId(), retrieved.id(), retrieved.version(), uniqueAuditCreatedById);

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.MARK_MISSING_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.SUCCESS, auditEvents.get(0).eventResponse().eventStatus());

    // Assert
    Asset updated = repository.findById(retrieved.id()).get();
    assertEquals(AssetLifecycleState.MISSING, updated.lifecycleState());
    assertEquals(retrieved.version() + 1, updated.version());
  }

  @Test
  void testMarkMissingAsset_notFound() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    Asset retrieved = createDummyAssetObject();
    var ex =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.markMissingAsset(
                    retrieved.regionId(),
                    retrieved.id(),
                    retrieved.version(),
                    uniqueAuditCreatedById));
    var sqlEx = (SQLException) ex.getCause();
    assertEquals(20001, sqlEx.getErrorCode());
    assertTrue(sqlEx.getMessage().contains("No records found for given region, id and version"));

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.MARK_MISSING_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.FAILED, auditEvents.get(0).eventResponse().eventStatus());
  }

  @Test
  void testMarkMissingAsset_invalidState() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    Asset created =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .lifecycleState(AssetLifecycleState.DISPOSED)
                .build());
    Asset retrieved = repository.findById(created.id()).get();

    // Act & Assert
    var ex =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.markMissingAsset(
                    retrieved.regionId(),
                    retrieved.id(),
                    retrieved.version(),
                    uniqueAuditCreatedById));
    var sqlEx = (SQLException) ex.getCause();
    assertEquals(20001, sqlEx.getErrorCode());
    assertTrue(sqlEx.getMessage().contains("Invalid state transition attempted on Asset"));

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.MARK_MISSING_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.FAILED, auditEvents.get(0).eventResponse().eventStatus());
  }

  @Test
  void testMarkMissingAssetWithChildAssets() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    RegionEntity region = util.createRegion();
    AssetLocation location = util.createAssetLocation(region.id());
    // happy path
    final Asset created1 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .locationId(location.id())
                .serialNumber("123")
                .build());
    final Asset created2 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .locationId(location.id())
                .serialNumber("234")
                .parentAssetId(created1.id())
                .build());
    final Asset created3 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .locationId(location.id())
                .serialNumber("345")
                .parentAssetId(created2.id())
                .build());

    assetRepository.markMissingAsset(
        created1.regionId(), created1.id(), created1.version(), uniqueAuditCreatedById);

    assertEquals(
        AssetLifecycleState.MISSING, repository.findById(created1.id()).get().lifecycleState());
    assertEquals(
        AssetLifecycleState.MISSING, repository.findById(created2.id()).get().lifecycleState());
    assertEquals(
        AssetLifecycleState.MISSING, repository.findById(created3.id()).get().lifecycleState());

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper
            .listAuditEventsByUpdatedByAndEventName(
                uniqueAuditCreatedById, InventoryAuditEventConstants.MARK_MISSING_ASSET)
            .stream()
            .filter(
                auditEvent -> auditEvent.eventResponse().eventStatus().equals(EventStatus.SUCCESS))
            .toList();
    assertEquals(3, auditEvents.size());

    // mark child asset as missing should fail

    final Asset created4 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .locationId(location.id())
                .catalogPartId(util.createCatalogPart("XYZ", "XYZ").id())
                .serialNumber("123")
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .build());

    final Asset created5 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .locationId(location.id())
                .catalogPartId(util.createCatalogPart("XYZ", "XYZ").id())
                .serialNumber("345")
                .parentAssetId(created4.id())
                .build());

    var ex =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.markMissingAsset(
                    created5.regionId(),
                    created5.id(),
                    created5.version(),
                    uniqueAuditCreatedById));
    var sqlEx = (SQLException) ex.getCause();
    assertEquals(20001, sqlEx.getErrorCode());
    assertTrue(sqlEx.getMessage().contains("Cannot mark child asset"));

    auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.MARK_MISSING_ASSET);
    assertEquals(4, auditEvents.size());
    assertLatestAuditEvent(EventStatus.FAILED, InventoryAuditEventConstants.MARK_MISSING_ASSET);

    // make sure they are still in original lifecycle state, no operation occurred
    assertEquals(
        created4.lifecycleState(), repository.findById(created4.id()).get().lifecycleState());
    assertEquals(
        created5.lifecycleState(), repository.findById(created5.id()).get().lifecycleState());
  }

  // DISPOSE/DESTROY/RETURN TEST CASES

  @Test
  void testDisposeAsset() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    RegionEntity region = util.createRegion();
    AssetLocation location = util.createAssetLocation(region.id());
    Asset created =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.PENDING_DISPOSAL)
                .locationId(location.id())
                .build());
    Asset retrieved = repository.findById(created.id()).get();

    // Act
    assetRepository.disposeAsset(
        retrieved.regionId(), retrieved.id(), retrieved.version(), uniqueAuditCreatedById);

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.DISPOSE_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.SUCCESS, auditEvents.get(0).eventResponse().eventStatus());

    // Assert
    Asset updated = repository.findById(retrieved.id()).get();
    assertEquals(AssetLifecycleState.DISPOSED, updated.lifecycleState());
    assertEquals(retrieved.version() + 1, updated.version());
    assertNull(updated.locationId());
  }

  @Test
  void testDisposeAsset_notFound() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    Asset retrieved = createDummyAssetObject();
    var ex =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.disposeAsset(
                    retrieved.regionId(),
                    retrieved.id(),
                    retrieved.version(),
                    uniqueAuditCreatedById));
    var sqlEx = (SQLException) ex.getCause();
    assertEquals(20001, sqlEx.getErrorCode());
    assertTrue(sqlEx.getMessage().contains("No records found for given region, id and version"));

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.DISPOSE_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.FAILED, auditEvents.get(0).eventResponse().eventStatus());
  }

  @Test
  void testDisposeAsset_invalidState() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    Asset created =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .lifecycleState(AssetLifecycleState.PENDING_DESTRUCTION)
                .build());
    Asset retrieved = repository.findById(created.id()).get();

    // Act & Assert
    var ex =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.disposeAsset(
                    retrieved.regionId(),
                    retrieved.id(),
                    retrieved.version(),
                    uniqueAuditCreatedById));
    var sqlEx = (SQLException) ex.getCause();
    assertEquals(20001, sqlEx.getErrorCode());
    assertTrue(sqlEx.getMessage().contains("Invalid state transition attempted on Asset"));

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.DISPOSE_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.FAILED, auditEvents.get(0).eventResponse().eventStatus());
  }

  @Test
  void testDisposeAsset_reservedAsset() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    RegionEntity region = util.createRegion();
    var part = util.createPart("opn-1", null, RmcId.generate(RmcIdType.Catalog.PART_CATEGORY));
    AssetReservation reservation = util.createAssetReservation(region.id(), part.id());

    Asset created =
        util.createAssetAndAcquireReservationOrTransfer(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .build(),
            reservation.id(),
            null);
    Asset retrieved = repository.findById(created.id()).get();

    // Act & Assert
    var ex =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.disposeAsset(
                    retrieved.regionId(),
                    retrieved.id(),
                    retrieved.version(),
                    uniqueAuditCreatedById));
    var sqlEx = (SQLException) ex.getCause();
    assertEquals(20001, sqlEx.getErrorCode());
    assertTrue(
        sqlEx
            .getMessage()
            .contains(
                "Asset "
                    + retrieved.id().toString()
                    + " cannot be set to DISPOSED when reservationId or transferId is set"));

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.DISPOSE_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.FAILED, auditEvents.get(0).eventResponse().eventStatus());
  }

  @Test
  void testDisposeAssetWithChildAssets() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    RegionEntity region = util.createRegion();
    AssetLocation location = util.createAssetLocation(region.id());
    // happy path

    final Asset created1 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .locationId(location.id())
                .catalogPartId(util.createCatalogPart("XYZ", "XYZ").id())
                .serialNumber("123")
                .lifecycleState(AssetLifecycleState.PENDING_DISPOSAL)
                .build());

    final Asset created2 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .locationId(location.id())
                .catalogPartId(util.createCatalogPart("XYZ", "XYZ").id())
                .serialNumber("234")
                .parentAssetId(created1.id())
                .build());

    final Asset created3 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .locationId(location.id())
                .catalogPartId(util.createCatalogPart("XYZ", "XYZ").id())
                .serialNumber("345")
                .parentAssetId(created2.id())
                .build());

    assetRepository.disposeAsset(
        created1.regionId(), created1.id(), created1.version(), uniqueAuditCreatedById);

    assertEquals(
        AssetLifecycleState.DISPOSED, repository.findById(created1.id()).get().lifecycleState());
    assertEquals(
        AssetLifecycleState.DISPOSED, repository.findById(created2.id()).get().lifecycleState());
    assertEquals(
        AssetLifecycleState.DISPOSED, repository.findById(created3.id()).get().lifecycleState());

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper
            .listAuditEventsByUpdatedByAndEventName(
                uniqueAuditCreatedById, InventoryAuditEventConstants.DISPOSE_ASSET)
            .stream()
            .filter(
                auditEvent -> auditEvent.eventResponse().eventStatus().equals(EventStatus.SUCCESS))
            .toList();
    assertEquals(3, auditEvents.size());

    // Dispose child asset failed

    final Asset created4 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .locationId(location.id())
                .catalogPartId(util.createCatalogPart("XYZ", "XYZ").id())
                .serialNumber("123")
                .lifecycleState(AssetLifecycleState.PENDING_DISPOSAL)
                .build());

    final Asset created5 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .locationId(location.id())
                .catalogPartId(util.createCatalogPart("XYZ", "XYZ").id())
                .serialNumber("234")
                .parentAssetId(created4.id())
                .build());

    var ex1 =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.disposeAsset(
                    created5.regionId(),
                    created5.id(),
                    created5.version(),
                    uniqueAuditCreatedById));
    var sqlEx1 = (SQLException) ex1.getCause();
    assertEquals(20001, sqlEx1.getErrorCode());
    assertTrue(sqlEx1.getMessage().contains("Invalid state transition attempted on Asset"));

    // check audit
    auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.DISPOSE_ASSET);
    assertEquals(4, auditEvents.size());
    assertLatestAuditEvent(EventStatus.FAILED, InventoryAuditEventConstants.DISPOSE_ASSET);

    // make sure they are still in original lifecycle state, no operation occurred
    assertEquals(
        created4.lifecycleState(), repository.findById(created4.id()).get().lifecycleState());
    assertEquals(
        created5.lifecycleState(), repository.findById(created5.id()).get().lifecycleState());
  }

  @Test
  void testDestroyAsset() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    RegionEntity region = util.createRegion();
    AssetLocation location = util.createAssetLocation(region.id());
    Asset created =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.PENDING_DESTRUCTION)
                .locationId(location.id())
                .build());
    Asset retrieved = repository.findById(created.id()).get();

    // Act
    assetRepository.destroyAsset(
        retrieved.regionId(), retrieved.id(), retrieved.version(), uniqueAuditCreatedById);

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.DESTROY_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.SUCCESS, auditEvents.get(0).eventResponse().eventStatus());

    // Assert
    Asset updated = repository.findById(retrieved.id()).get();
    assertEquals(AssetLifecycleState.DESTROYED, updated.lifecycleState());
    assertEquals(retrieved.version() + 1, updated.version());
    assertNull(updated.locationId());
  }

  @Test
  void testDestroyAsset_notFound() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    Asset retrieved = createDummyAssetObject();
    var ex =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.destroyAsset(
                    retrieved.regionId(),
                    retrieved.id(),
                    retrieved.version(),
                    uniqueAuditCreatedById));
    var sqlEx = (SQLException) ex.getCause();
    assertEquals(20001, sqlEx.getErrorCode());
    assertTrue(sqlEx.getMessage().contains("No records found for given region, id and version"));

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.DESTROY_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.FAILED, auditEvents.get(0).eventResponse().eventStatus());
  }

  @Test
  void testDestroyAsset_invalidState() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    Asset created =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .lifecycleState(AssetLifecycleState.PENDING_DISPOSAL)
                .build());

    Asset retrieved = repository.findById(created.id()).get();
    // Act & Assert
    var ex =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.destroyAsset(
                    retrieved.regionId(),
                    retrieved.id(),
                    retrieved.version(),
                    uniqueAuditCreatedById));
    var sqlEx = (SQLException) ex.getCause();
    assertEquals(20001, sqlEx.getErrorCode());
    assertTrue(sqlEx.getMessage().contains("Invalid state transition attempted on Asset"));

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.DESTROY_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.FAILED, auditEvents.get(0).eventResponse().eventStatus());
  }

  @Test
  void testDestroyAsset_reservedAsset() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    RegionEntity region = util.createRegion();

    Asset created =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .locationId(util.createAssetLocation(region.id()).id())
                .build());
    var transfer = util.createAssetTransfer(region.id(), created.id());
    util.acquireAssetTransfer(transfer.id(), created.id());

    Asset retrieved = repository.findById(created.id()).get();

    // Act & Assert
    var ex =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.destroyAsset(
                    retrieved.regionId(),
                    retrieved.id(),
                    retrieved.version(),
                    uniqueAuditCreatedById));
    var sqlEx = (SQLException) ex.getCause();
    assertEquals(20001, sqlEx.getErrorCode());
    assertTrue(
        sqlEx
            .getMessage()
            .contains(
                "Asset "
                    + retrieved.id().toString()
                    + " cannot be set to DESTROYED when reservationId or transferId is set"));

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.DESTROY_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.FAILED, auditEvents.get(0).eventResponse().eventStatus());
  }

  @Test
  void testDestroyAssetWithChildAssets() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    RegionEntity region = util.createRegion();
    AssetLocation location = util.createAssetLocation(region.id());
    // happy path

    final Asset created1 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .locationId(location.id())
                .catalogPartId(util.createCatalogPart("XYZ", "XYZ").id())
                .serialNumber("123")
                .lifecycleState(AssetLifecycleState.PENDING_DESTRUCTION)
                .build());

    final Asset created2 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .locationId(location.id())
                .catalogPartId(util.createCatalogPart("XYZ", "XYZ").id())
                .serialNumber("234")
                .parentAssetId(created1.id())
                .build());

    final Asset created3 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .locationId(location.id())
                .catalogPartId(util.createCatalogPart("XYZ", "XYZ").id())
                .serialNumber("345")
                .parentAssetId(created2.id())
                .build());

    assetRepository.destroyAsset(
        created1.regionId(), created1.id(), created1.version(), uniqueAuditCreatedById);

    assertEquals(
        AssetLifecycleState.DESTROYED, repository.findById(created1.id()).get().lifecycleState());
    assertEquals(
        AssetLifecycleState.DESTROYED, repository.findById(created2.id()).get().lifecycleState());
    assertEquals(
        AssetLifecycleState.DESTROYED, repository.findById(created3.id()).get().lifecycleState());

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper
            .listAuditEventsByUpdatedByAndEventName(
                uniqueAuditCreatedById, InventoryAuditEventConstants.DESTROY_ASSET)
            .stream()
            .filter(
                auditEvent -> auditEvent.eventResponse().eventStatus().equals(EventStatus.SUCCESS))
            .toList();
    assertEquals(3, auditEvents.size());

    // Destroy child asset should fail

    final Asset created4 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .locationId(location.id())
                .catalogPartId(util.createCatalogPart("XYZ", "XYZ").id())
                .serialNumber("123")
                .lifecycleState(AssetLifecycleState.PENDING_DESTRUCTION)
                .build());

    final Asset created5 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .locationId(location.id())
                .catalogPartId(util.createCatalogPart("XYZ", "XYZ").id())
                .serialNumber("345")
                .parentAssetId(created4.id())
                .build());

    var ex1 =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.destroyAsset(
                    created5.regionId(),
                    created5.id(),
                    created5.version(),
                    uniqueAuditCreatedById));
    var sqlEx1 = (SQLException) ex1.getCause();
    assertEquals(20001, sqlEx1.getErrorCode());
    assertTrue(sqlEx1.getMessage().contains("Invalid state transition attempted on Asset"));

    auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.DESTROY_ASSET);
    assertEquals(4, auditEvents.size());
    assertLatestAuditEvent(EventStatus.FAILED, InventoryAuditEventConstants.DESTROY_ASSET);

    // make sure they are still in original lifecycle state, no operation occurred
    assertEquals(
        created4.lifecycleState(), repository.findById(created4.id()).get().lifecycleState());
    assertEquals(
        created5.lifecycleState(), repository.findById(created5.id()).get().lifecycleState());
  }

  @Test
  void testReturnAsset() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    RegionEntity region = util.createRegion();
    AssetLocation location = util.createAssetLocation(region.id());
    Asset created =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.PENDING_RETURN)
                .locationId(location.id())
                .build());
    Asset retrieved = repository.findById(created.id()).get();

    // Act
    assetRepository.returnAsset(
        retrieved.regionId(), retrieved.id(), retrieved.version(), uniqueAuditCreatedById);

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.RETURN_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.SUCCESS, auditEvents.get(0).eventResponse().eventStatus());

    // Assert
    Asset updated = repository.findById(retrieved.id()).get();
    assertEquals(AssetLifecycleState.RETURNED, updated.lifecycleState());
    assertEquals(retrieved.version() + 1, updated.version());
    assertNull(updated.locationId());
  }

  @Test
  void testReturnAsset_notFound() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    Asset retrieved = createDummyAssetObject();
    var ex =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.returnAsset(
                    retrieved.regionId(),
                    retrieved.id(),
                    retrieved.version(),
                    uniqueAuditCreatedById));
    var sqlEx = (SQLException) ex.getCause();
    assertEquals(20001, sqlEx.getErrorCode());
    assertTrue(sqlEx.getMessage().contains("No records found for given region, id and version"));

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.RETURN_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.FAILED, auditEvents.get(0).eventResponse().eventStatus());
  }

  @Test
  void testReturnAsset_invalidState() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    Asset created =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .lifecycleState(AssetLifecycleState.PENDING_DISPOSAL)
                .build());
    Asset retrieved = repository.findById(created.id()).get();

    // Act & Assert
    var ex =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.returnAsset(
                    retrieved.regionId(),
                    retrieved.id(),
                    retrieved.version(),
                    uniqueAuditCreatedById));
    var sqlEx = (SQLException) ex.getCause();
    assertEquals(20001, sqlEx.getErrorCode());
    assertTrue(sqlEx.getMessage().contains("Invalid state transition attempted on Asset"));

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.RETURN_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.FAILED, auditEvents.get(0).eventResponse().eventStatus());
  }

  @Test
  void testReturnAsset_reservedAsset() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    RegionEntity region = util.createRegion();

    var part = util.createCatalogPart("opn-1", null);
    AssetReservation reservation = util.createAssetReservation(region.id(), part.id());

    Asset created =
        util.createAssetAndAcquireReservationOrTransfer(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .build(),
            reservation.id(),
            null);

    Asset retrieved = repository.findById(created.id()).get();

    // Act & Assert
    var ex =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.returnAsset(
                    retrieved.regionId(),
                    retrieved.id(),
                    retrieved.version(),
                    uniqueAuditCreatedById));
    var sqlEx = (SQLException) ex.getCause();
    assertEquals(20001, sqlEx.getErrorCode());
    assertTrue(
        sqlEx
            .getMessage()
            .contains(
                "Asset "
                    + retrieved.id().toString()
                    + " cannot be set to RETURNED when reservationId or transferId is set"));

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.RETURN_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.FAILED, auditEvents.get(0).eventResponse().eventStatus());
  }

  @Test
  void testReturnAssetWithChildAssets() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    RegionEntity region = util.createRegion();
    AssetLocation location = util.createAssetLocation(region.id());
    // happy path

    final Asset created1 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .locationId(location.id())
                .catalogPartId(util.createCatalogPart("XYZ", "XYZ").id())
                .serialNumber(UUID.randomUUID().toString())
                .lifecycleState(AssetLifecycleState.PENDING_RETURN)
                .build());

    final Asset created2 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .locationId(location.id())
                .catalogPartId(util.createCatalogPart("XYZ", "XYZ").id())
                .serialNumber(UUID.randomUUID().toString())
                .parentAssetId(created1.id())
                .build());

    final Asset created3 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .locationId(location.id())
                .catalogPartId(util.createCatalogPart("XYZ", "XYZ").id())
                .serialNumber(UUID.randomUUID().toString())
                .parentAssetId(created2.id())
                .build());

    // Act
    assetRepository.returnAsset(
        created1.regionId(), created1.id(), created1.version(), uniqueAuditCreatedById);

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.RETURN_ASSET);
    assertEquals(3, auditEvents.size());
    assertEquals(
        3,
        auditEvents.stream()
            .filter(entry -> entry.eventResponse().eventStatus().equals(EventStatus.SUCCESS))
            .toList()
            .size());

    assertEquals(
        AssetLifecycleState.RETURNED, repository.findById(created1.id()).get().lifecycleState());
    assertEquals(
        AssetLifecycleState.RETURNED, repository.findById(created2.id()).get().lifecycleState());
    assertEquals(
        AssetLifecycleState.RETURNED, repository.findById(created3.id()).get().lifecycleState());

    // Destroy child asset should fail

    final Asset created4 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .locationId(location.id())
                .catalogPartId(util.createCatalogPart("XYZ", "XYZ").id())
                .serialNumber(UUID.randomUUID().toString())
                .lifecycleState(AssetLifecycleState.PENDING_RETURN)
                .build());

    final Asset created5 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .locationId(location.id())
                .catalogPartId(util.createCatalogPart("XYZ", "XYZ").id())
                .serialNumber(UUID.randomUUID().toString())
                .parentAssetId(created4.id())
                .build());

    var ex1 =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.returnAsset(
                    created5.regionId(),
                    created5.id(),
                    created5.version(),
                    uniqueAuditCreatedById));
    var sqlEx1 = (SQLException) ex1.getCause();
    assertEquals(20001, sqlEx1.getErrorCode());
    assertTrue(sqlEx1.getMessage().contains("Invalid state transition attempted on Asset"));

    // check audit
    auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.RETURN_ASSET);
    assertEquals(4, auditEvents.size());
    assertLatestAuditEvent(EventStatus.FAILED, InventoryAuditEventConstants.RETURN_ASSET);

    // make sure they are still in original lifecycle state, no operation occurred
    assertEquals(
        created4.lifecycleState(), repository.findById(created4.id()).get().lifecycleState());
    assertEquals(
        created5.lifecycleState(), repository.findById(created5.id()).get().lifecycleState());
  }

  // MOVE ASSET TEST CASES

  @Test
  void testMoveAssetToLocationPendingDestruction() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    RegionEntity region = util.createRegion();
    AssetLocation location = util.createAssetLocation(region.id());
    Asset asset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .locationId(location.id())
                .build());

    Asset retrieved = repository.findById(asset.id()).get();
    assertEquals(AssetLifecycleState.AVAILABLE, retrieved.lifecycleState());
    assertEquals(location.id(), retrieved.locationId());

    // create second part
    String oraclePartNumber2 = "8187337";
    String manufacturerPartNumber2 = "69350952";
    Part part2 = util.createCatalogPart(oraclePartNumber2, manufacturerPartNumber2);

    // Create one child asset in non IN_TRANSIT state
    final Asset childAsset1 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .serialNumber("SK00410765")
                .catalogPartId(part2.id())
                .parentAssetId(asset.id())
                .createdBy(uniqueAuditCreatedById)
                .build());
    final Asset childAsset2 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .serialNumber("SK00410766")
                .catalogPartId(part2.id())
                .parentAssetId(asset.id())
                .createdBy(uniqueAuditCreatedById)
                .build());

    AssetLocation locationNew = util.createAssetLocation(region.id());

    // Act
    String lifecycleState = AssetLifecycleState.PENDING_DESTRUCTION.toString();
    assetRepository.moveAssetToLocation(
        retrieved.regionId(),
        retrieved.id(),
        retrieved.version(),
        locationNew.id(),
        lifecycleState,
        uniqueAuditCreatedById,
        retrieved.subInventory().toString());

    // Assert
    Asset updated = repository.findById(retrieved.id()).get();
    assertEquals(AssetLifecycleState.PENDING_DESTRUCTION, updated.lifecycleState());
    assertEquals(retrieved.version() + 1, updated.version());
    assertEquals(locationNew.id(), updated.locationId());
    assertNotNull(updated.additionalProperties().retiredDate());

    // retired date changes not propagated to the child
    Asset childAssetUpdated1 = repository.findById(childAsset1.id()).get();
    assertNull(childAssetUpdated1.additionalProperties());

    Asset childAssetUpdated2 = repository.findById(childAsset2.id()).get();
    assertNull(childAssetUpdated2.additionalProperties());

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.MOVE_ASSET_TO_LOCATION);
    assertEquals(3, auditEvents.size());
    assertEquals(EventStatus.SUCCESS, auditEvents.get(0).eventResponse().eventStatus());
  }

  @Test
  void testMoveAssetToLocation() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    RegionEntity region = util.createRegion();
    AssetLocation location = util.createAssetLocation(region.id());
    Asset asset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .locationId(location.id())
                .build());
    AssetLocation locationNew = util.createAssetLocation(region.id());

    Asset retrieved = repository.findById(asset.id()).get();
    assertEquals(AssetLifecycleState.AVAILABLE, retrieved.lifecycleState());
    assertEquals(location.id(), retrieved.locationId());

    // Act
    @Nullable String lifecycleState = AssetLifecycleState.AVAILABLE.toString();
    assetRepository.moveAssetToLocation(
        retrieved.regionId(),
        retrieved.id(),
        retrieved.version(),
        locationNew.id(),
        lifecycleState,
        uniqueAuditCreatedById,
        retrieved.subInventory().toString());

    // Assert
    Asset updated = repository.findById(retrieved.id()).get();
    assertEquals(AssetLifecycleState.AVAILABLE, updated.lifecycleState());
    assertEquals(retrieved.version() + 1, updated.version());
    assertEquals(locationNew.id(), updated.locationId());
    assertNull(updated.additionalProperties());

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.MOVE_ASSET_TO_LOCATION);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.SUCCESS, auditEvents.get(0).eventResponse().eventStatus());
  }

  @Test
  void testMoveAssetToLocationWithRackInstanceIdExist() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    RegionEntity region = util.createRegion();
    AssetLocation location = util.createAssetLocation(region.id());
    RackInstance rackInstance =
        util.createRackInstance(
            region.airportCode(), "mockSerialNumber", "mockManufacturerNumber", null);
    Asset asset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.IN_TRANSIT)
                .locationId(location.id())
                .rackInstanceId(rackInstance.id())
                .build());

    assetRepository.setRackInstanceOnAsset(
        region.id(), asset.id(), rackInstance.id(), asset.version() + 1, uniqueAuditCreatedById);

    repository.findAll();
    AssetLocation locationNew = util.createAssetLocation(region.id());

    Asset retrieved = repository.findById(asset.id()).get();
    assertEquals(AssetLifecycleState.IN_TRANSIT, retrieved.lifecycleState());
    assertEquals(location.id(), retrieved.locationId());

    // Act
    String lifecycleState = AssetLifecycleState.RECEIVED.toString();
    assetRepository.moveAssetToLocation(
        retrieved.regionId(),
        retrieved.id(),
        retrieved.version(),
        locationNew.id(),
        lifecycleState,
        uniqueAuditCreatedById,
        retrieved.subInventory().toString());

    // Assert
    Asset updated = repository.findById(retrieved.id()).get();
    assertEquals(AssetLifecycleState.RECEIVED, updated.lifecycleState());
    assertEquals(retrieved.version() + 1, updated.version());
    assertEquals(locationNew.id(), updated.locationId());
    assertNull(updated.additionalProperties());

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.MOVE_ASSET_TO_LOCATION);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.SUCCESS, auditEvents.get(0).eventResponse().eventStatus());
  }

  @Test
  void testMoveAssetNotFound() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    RegionEntity region = util.createRegion();
    AssetLocation location = util.createAssetLocation(region.id());
    Asset asset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .locationId(location.id())
                .build());
    AssetLocation locationNew = util.createAssetLocation(region.id());

    Asset retrieved = repository.findById(asset.id()).get();
    assertEquals(AssetLifecycleState.AVAILABLE, retrieved.lifecycleState());
    assertEquals(location.id(), retrieved.locationId());

    // Assert
    var ex1 =
        assertThrows(
            DataAccessException.class,
            () -> {
              RmcId id = RmcId.generate(RmcIdType.Inventory.ASSET);
              @Nullable String lifecycleState = AssetLifecycleState.AVAILABLE.toString();
              assetRepository.moveAssetToLocation(
                  retrieved.regionId(),
                  id,
                  retrieved.version(),
                  locationNew.id(),
                  lifecycleState,
                  uniqueAuditCreatedById,
                  retrieved.subInventory().toString());
            });
    var sqlEx1 = (SQLException) ex1.getCause();
    assertEquals(20001, sqlEx1.getErrorCode());
    assertTrue(sqlEx1.getMessage().contains("No records found for given region, id and version"));

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.MOVE_ASSET_TO_LOCATION);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.FAILED, auditEvents.get(0).eventResponse().eventStatus());
  }

  @Test
  void testMoveAsset_install() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    util.createRackParentEntities();
    RegionEntity region = util.getRegionEntity();

    AssetLocation location = util.createAssetLocation(region.id());
    Asset componentAsset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .locationId(location.id())
                .build());

    Asset retrieved = repository.findById(componentAsset.id()).get();
    assertEquals(AssetLifecycleState.AVAILABLE, retrieved.lifecycleState());
    assertEquals(location.id(), retrieved.locationId());
    assertNull(retrieved.rackInstanceId());

    // Act
    Asset rackAsset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .build());
    RackModel rackModel = util.createAndGetComputeRackModel();
    RackInstance rackInstance =
        util.createRackInstance(region.airportCode(), rackAsset.id(), rackModel.id());
    @Nullable String lifecycleState = AssetLifecycleState.INSTALLED.toString();
    assetRepository.moveAssetToRack(
        retrieved.regionId(),
        retrieved.id(),
        retrieved.version(),
        rackInstance.id(),
        "test-elevation",
        lifecycleState,
        uniqueAuditCreatedById);

    // Assert
    Asset updated = repository.findById(retrieved.id()).get();
    assertEquals(AssetLifecycleState.INSTALLED, updated.lifecycleState());
    assertEquals(retrieved.version() + 1, updated.version());
    assertNull(updated.locationId());
    assertEquals(rackInstance.id(), updated.rackInstanceId());

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.MOVE_ASSET_TO_RACK);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.SUCCESS, auditEvents.get(0).eventResponse().eventStatus());
  }

  @Test
  void testMoveAsset_validChildState() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    util.createRackParentEntities();
    RegionEntity region = util.getRegionEntity();

    AssetLocation location = util.createAssetLocation(region.id());
    Asset componentAsset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .locationId(location.id())
                .build());

    Asset retrieved = repository.findById(componentAsset.id()).get();
    assertEquals(AssetLifecycleState.AVAILABLE, retrieved.lifecycleState());
    assertEquals(location.id(), retrieved.locationId());
    assertNull(retrieved.rackInstanceId());

    // Create one child asset with valid state
    util.createAsset(
        AssetCreationDetailsBuilder.builder()
            .regionId(region.id())
            .parentAssetId(retrieved.id())
            .createdBy(uniqueAuditCreatedById)
            .build());
    util.createAsset(
        AssetCreationDetailsBuilder.builder()
            .regionId(region.id())
            .parentAssetId(retrieved.id())
            .createdBy(uniqueAuditCreatedById)
            .build());

    // Act
    Asset rackAsset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .build());
    RackModel rackModel = util.createAndGetComputeRackModel();
    RackInstance rackInstance =
        util.createRackInstance(region.airportCode(), rackAsset.id(), rackModel.id());

    @Nullable String lifecycleState = AssetLifecycleState.INSTALLED.toString();
    assetRepository.moveAssetToRack(
        retrieved.regionId(),
        retrieved.id(),
        retrieved.version(),
        rackInstance.id(),
        "test-elevation",
        lifecycleState,
        uniqueAuditCreatedById);

    // Assert
    Asset updated = repository.findById(retrieved.id()).get();
    assertEquals(AssetLifecycleState.INSTALLED, updated.lifecycleState());
    assertEquals(retrieved.version() + 1, updated.version());
    assertNull(updated.locationId());
    assertEquals(rackInstance.id(), updated.rackInstanceId());

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.MOVE_ASSET_TO_RACK);
    assertEquals(3, auditEvents.size());
    assertEquals(EventStatus.SUCCESS, auditEvents.get(0).eventResponse().eventStatus());
  }

  @Test
  void testMoveAsset_badParams() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    RegionEntity region = util.createRegion();
    AssetLocation location = util.createAssetLocation(region.id());
    Asset componentAsset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .locationId(location.id())
                .build());
    Asset retrieved = repository.findById(componentAsset.id()).get();

    // invalid lifecycle state for location move
    var ex1 =
        assertThrows(
            DataAccessException.class,
            () -> {
              @Nullable String lifecycleState = AssetLifecycleState.ACQUIRED.toString();
              assetRepository.moveAssetToLocation(
                  retrieved.regionId(),
                  retrieved.id(),
                  retrieved.version(),
                  location.id(),
                  lifecycleState,
                  uniqueAuditCreatedById,
                  retrieved.subInventory().toString());
            });
    var sqlEx1 = (SQLException) ex1.getCause();
    assertEquals(20001, sqlEx1.getErrorCode());
    assertTrue(
        sqlEx1.getMessage().contains("Invalid lifecycle state specified for move to location"));

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.MOVE_ASSET_TO_LOCATION);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.FAILED, auditEvents.get(0).eventResponse().eventStatus());

    // invalid lifecycle state for rack move
    var ex2 =
        assertThrows(
            DataAccessException.class,
            () -> {
              @Nullable String lifecycleState = AssetLifecycleState.ACQUIRED.toString();
              assetRepository.moveAssetToRack(
                  retrieved.regionId(),
                  retrieved.id(),
                  retrieved.version(),
                  RmcId.generate(RmcIdType.DCMS.RACK_INSTANCE),
                  "elevation",
                  lifecycleState,
                  uniqueAuditCreatedById);
            });
    var sqlEx2 = (SQLException) ex2.getCause();
    assertEquals(20001, sqlEx1.getErrorCode());
    assertTrue(sqlEx2.getMessage().contains("Invalid lifecycle state specified for move to rack"));

    // check audit
    auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.MOVE_ASSET_TO_RACK);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.FAILED, auditEvents.get(0).eventResponse().eventStatus());

    // Empty locationId
    var var3 =
        assertThrows(
            IllegalArgumentException.class,
            () -> {
              @Nullable String lifecycleState = AssetLifecycleState.AVAILABLE.toString();
              assetRepository.moveAssetToLocation(
                  retrieved.regionId(),
                  retrieved.id(),
                  retrieved.version(),
                  null,
                  lifecycleState,
                  uniqueAuditCreatedById,
                  retrieved.subInventory().toString());
            });

    // Empty rackInstanceId
    assertThrows(
        IllegalArgumentException.class,
        () -> {
          @Nullable String lifecycleState = AssetLifecycleState.INSTALLED.toString();
          assetRepository.moveAssetToRack(
              retrieved.regionId(),
              retrieved.id(),
              retrieved.version(),
              null,
              "dummy-elevation",
              lifecycleState,
              uniqueAuditCreatedById);
        });

    // LocationId and rackId specified
    Asset rackAsset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .build());

    // create rack model
    RackModel rackModel = util.createRackModelWithParentEntities(region);

    RackInstance rackInstance =
        util.createRackInstance(region.airportCode(), rackAsset.id(), rackModel.id());

    // state=PENDING_DESTRUCTION for moving to a rack
    var ex8 =
        assertThrows(
            DataAccessException.class,
            () -> {
              @Nullable String lifecycleState = AssetLifecycleState.PENDING_DESTRUCTION.toString();
              assetRepository.moveAssetToRack(
                  retrieved.regionId(),
                  retrieved.id(),
                  retrieved.version(),
                  rackInstance.id(),
                  "elevation",
                  lifecycleState,
                  uniqueAuditCreatedById);
            });
    var sqlEx8 = (SQLException) ex8.getCause();
    assertEquals(20001, sqlEx8.getErrorCode());
    assertTrue(sqlEx8.getMessage().contains("Invalid lifecycle state specified for move to rack"));

    // check audit
    auditEvents =
        createEntityTestHelper
            .listAuditEventsByUpdatedByAndEventName(
                uniqueAuditCreatedById, InventoryAuditEventConstants.MOVE_ASSET_TO_RACK)
            .stream()
            .filter(
                auditEvent -> auditEvent.eventResponse().eventStatus().equals(EventStatus.FAILED))
            .toList();
    assertEquals(2, auditEvents.size());

    // ReservationId exists on asset and state is not INSTALLED
    var part = util.createCatalogPart("opn-1", null);
    AssetReservation reservation = util.createAssetReservation(region.id(), part.id());

    Asset reservedAsset =
        util.createAssetAndAcquireReservationOrTransfer(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .build(),
            reservation.id(),
            null);
    var ex9 =
        assertThrows(
            DataAccessException.class,
            () -> {
              @Nullable String lifecycleState = AssetLifecycleState.PENDING_DISPOSAL.toString();
              assetRepository.moveAssetToLocation(
                  reservedAsset.regionId(),
                  reservedAsset.id(),
                  reservedAsset.version(),
                  location.id(),
                  lifecycleState,
                  uniqueAuditCreatedById,
                  reservedAsset.subInventory().toString());
            });
    var sqlEx9 = (SQLException) ex9.getCause();
    assertEquals(20001, sqlEx9.getErrorCode());
    assertThat(sqlEx9.getMessage())
        .contains(
            "Cannot move Asset "
                + reservedAsset.id().toString()
                + " to PENDING_DISPOSAL when reservationId is set");

    // check audit
    auditEvents =
        createEntityTestHelper
            .listAuditEventsByUpdatedByAndEventName(
                uniqueAuditCreatedById, InventoryAuditEventConstants.MOVE_ASSET_TO_LOCATION)
            .stream()
            .filter(
                auditEvent -> auditEvent.eventResponse().eventStatus().equals(EventStatus.FAILED))
            .toList();
    assertEquals(2, auditEvents.size());

    // AssetLocation location = util.createAssetLocation(region.id()).id();
    // Create a spare (non-reserved) asset in the location, so we can transfer another away
    RmcId partId =
        util.createCatalogPart(UUID.randomUUID().toString(), UUID.randomUUID().toString()).id();
    util.createAsset(
        AssetCreationDetailsBuilder.builder()
            .regionId(region.id())
            .lifecycleState(AssetLifecycleState.AVAILABLE)
            .catalogPartId(partId)
            .locationId(location.id())
            .build());

    // TransferId exists on asset and state is not AVAILABLE
    Asset transferAsset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .locationId(location.id())
                .catalogPartId(partId)
                .build());
    var transfer = util.createAssetTransfer(region.id(), transferAsset.id());
    util.acquireAssetTransfer(transfer.id(), transferAsset.id());

    var ex10 =
        assertThrows(
            DataAccessException.class,
            () -> {
              @Nullable String lifecycleState = AssetLifecycleState.PENDING_RETURN.toString();
              assetRepository.moveAssetToLocation(
                  transferAsset.regionId(),
                  transferAsset.id(),
                  transferAsset.version() + 1,
                  location.id(),
                  lifecycleState,
                  uniqueAuditCreatedById,
                  transferAsset.subInventory().toString());
            });
    var sqlEx10 = (SQLException) ex10.getCause();
    assertEquals(20001, sqlEx10.getErrorCode());
    assertTrue(
        sqlEx10
            .getMessage()
            .contains(
                "Cannot move Asset "
                    + transferAsset.id().toString()
                    + " to PENDING_RETURN when transferId is set"));

    // check audit
    auditEvents =
        createEntityTestHelper
            .listAuditEventsByUpdatedByAndEventName(
                uniqueAuditCreatedById, InventoryAuditEventConstants.MOVE_ASSET_TO_LOCATION)
            .stream()
            .filter(
                auditEvent -> auditEvent.eventResponse().eventStatus().equals(EventStatus.FAILED))
            .toList();
    assertEquals(3, auditEvents.size());

    // Invalid state transition
    Asset asset2 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.ACQUIRED)
                .build());
    var ex11 =
        assertThrows(
            DataAccessException.class,
            () -> {
              @Nullable String lifecycleState = AssetLifecycleState.PENDING_RETURN.toString();
              assetRepository.moveAssetToLocation(
                  asset2.regionId(),
                  asset2.id(),
                  asset2.version(),
                  location.id(),
                  lifecycleState,
                  uniqueAuditCreatedById,
                  asset2.subInventory().toString());
            });
    var sqlEx11 = (SQLException) ex11.getCause();
    assertEquals(20001, sqlEx11.getErrorCode());
    assertTrue(sqlEx11.getMessage().contains("Invalid state transition attempted"));

    // check audit
    auditEvents =
        createEntityTestHelper
            .listAuditEventsByUpdatedByAndEventName(
                uniqueAuditCreatedById, InventoryAuditEventConstants.MOVE_ASSET_TO_LOCATION)
            .stream()
            .filter(
                auditEvent -> auditEvent.eventResponse().eventStatus().equals(EventStatus.FAILED))
            .toList();
    assertEquals(4, auditEvents.size());

    // Asset not found
    Asset dummyAsset = createDummyAssetObject();
    var ex12 =
        assertThrows(
            DataAccessException.class,
            () -> {
              @Nullable String lifecycleState = AssetLifecycleState.PENDING_RETURN.toString();
              assetRepository.moveAssetToLocation(
                  dummyAsset.regionId(),
                  dummyAsset.id(),
                  dummyAsset.version(),
                  location.id(),
                  lifecycleState,
                  uniqueAuditCreatedById,
                  dummyAsset.subInventory().toString());
            });
    var sqlEx12 = (SQLException) ex12.getCause();
    assertEquals(20001, sqlEx12.getErrorCode());
    assertTrue(sqlEx12.getMessage().contains("No records found for given region, id and version"));

    // check audit
    auditEvents =
        createEntityTestHelper
            .listAuditEventsByUpdatedByAndEventName(
                uniqueAuditCreatedById, InventoryAuditEventConstants.MOVE_ASSET_TO_LOCATION)
            .stream()
            .filter(
                auditEvent -> auditEvent.eventResponse().eventStatus().equals(EventStatus.FAILED))
            .toList();
    assertEquals(5, auditEvents.size());
  }

  @Test
  void testMoveAssetToLocationInReceivedStateUpdateLocation() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    var region = createEntityTestHelper.createRegionEntity();
    String airportCode = region.airportCode();
    String regionId = regionService.getRegionByAirportCodeOrFail(airportCode).id();
    final String oraclePartNumber = "7338187";
    final String manufacturerPartNumber = "35096952";

    AssetLocation location1 = util.createAssetLocation(region.id());
    RmcId rackInstanceId = RmcId.generate(RmcIdType.DCMS.DEVICE_INSTANCE);

    var parentAsset =
        createAssetAllParams(
                regionId,
                "**********",
                oraclePartNumber,
                manufacturerPartNumber,
                rackInstanceId,
                location1.id(),
                null,
                AssetLifecycleState.RECEIVED,
                "oracle-tag-xyz",
                "E2:7F:1C:A3:B8:D5",
                "1",
                uniqueAuditCreatedById)
            .get();

    assertEquals(AssetLifecycleState.RECEIVED, parentAsset.lifecycleState());
    assertEquals(location1.id(), parentAsset.locationId());

    final var childAsset =
        createAssetwithLifecycleState(
                regionId,
                "SK00410765",
                "8187337",
                "69350952",
                location1.id(),
                parentAsset.id(),
                AssetLifecycleState.INSTALLED,
                "oracle-tag-xyz",
                "E2:7F:1C:A3:B8:D5",
                uniqueAuditCreatedById)
            .get();

    AssetLocation location2 = util.createAssetLocation(region.id(), AssetLocationType.SPARE);

    String lifecycleState = AssetLifecycleState.RECEIVED.toString();
    assetRepository.moveAssetToLocation(
        parentAsset.regionId(),
        parentAsset.id(),
        parentAsset.version(),
        location2.id(),
        lifecycleState,
        uniqueAuditCreatedById,
        parentAsset.subInventory().toString());
    var fetchedParentAsset = assetRepository.findById(parentAsset.id()).get();
    assertEquals(AssetLifecycleState.RECEIVED, fetchedParentAsset.lifecycleState());
    assertEquals(location2.id(), fetchedParentAsset.locationId());
    assertEquals(parentAsset.elevation(), fetchedParentAsset.elevation());
    assertEquals(parentAsset.rackInstanceId(), fetchedParentAsset.rackInstanceId());
    var fetchedChildAsset = assetRepository.findById(childAsset.id()).get();
    assertEquals(AssetLifecycleState.INSTALLED, fetchedChildAsset.lifecycleState());
    assertEquals(location2.id(), fetchedChildAsset.locationId());
  }

  @Test
  void testMoveAssetToLocationWithChildAssetState() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    var region = createEntityTestHelper.createRegionEntity();
    String airportCode = region.airportCode();
    String regionId = regionService.getRegionByAirportCodeOrFail(airportCode).id();
    final String oraclePartNumber = "7338187";
    final String manufacturerPartNumber = "35096952";

    AssetLocation location1 = util.createAssetLocation(region.id());

    // Create Asset
    var parentAsset =
        createAssetwithLifecycleState(
                regionId,
                "**********",
                oraclePartNumber,
                manufacturerPartNumber,
                location1.id(),
                null,
                AssetLifecycleState.RECEIVED,
                "oracle-tag-xyz",
                "E2:7F:1C:A3:B8:D5",
                uniqueAuditCreatedById)
            .get();
    assertEquals(AssetLifecycleState.RECEIVED, parentAsset.lifecycleState());
    assertEquals(location1.id(), parentAsset.locationId());

    var childAsset =
        createAssetwithLifecycleState(
                regionId,
                "SK00410765",
                "8187337",
                "69350952",
                location1.id(),
                parentAsset.id(),
                AssetLifecycleState.INSTALLED,
                "oracle-tag-xyz",
                "E2:7F:1C:A3:B8:D5",
                uniqueAuditCreatedById)
            .get();

    AssetLocation location2 = util.createAssetLocation(region.id(), AssetLocationType.SPARE);
    String lifecycleState = AssetLifecycleState.RECEIVED.toString();

    assetRepository.moveAssetToLocation(
        parentAsset.regionId(),
        parentAsset.id(),
        parentAsset.version(),
        location2.id(),
        lifecycleState,
        uniqueAuditCreatedById,
        parentAsset.subInventory().toString());

    // Assert Child asset lifecycle state same as before move
    Asset updatedChild = repository.findById(childAsset.id()).get();
    assertEquals(childAsset.lifecycleState(), updatedChild.lifecycleState());
    assertEquals(location2.id(), updatedChild.locationId());

    // Assert parent asset
    Asset updated = repository.findById(parentAsset.id()).get();
    assertEquals(lifecycleState, updated.lifecycleState().toString());
    assertEquals(parentAsset.version() + 1, updated.version());
    assertEquals(location2.id(), updated.locationId());
  }

  @Test
  void testMoveAssetToLocationInReceivedStateInvalidToState() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    var region = createEntityTestHelper.createRegionEntity();
    String airportCode = region.airportCode();
    String regionId = regionService.getRegionByAirportCodeOrFail(airportCode).id();
    final String oraclePartNumber = "7338187";
    final String manufacturerPartNumber = "35096952";

    AssetLocation location1 = util.createAssetLocation(region.id());

    // Create Asset
    var parentAsset =
        createAssetwithLifecycleState(
                regionId,
                "**********",
                oraclePartNumber,
                manufacturerPartNumber,
                location1.id(),
                null,
                AssetLifecycleState.RECEIVED,
                "oracle-tag-xyz",
                "E2:7F:1C:A3:B8:D5",
                uniqueAuditCreatedById)
            .get();
    assertEquals(AssetLifecycleState.RECEIVED, parentAsset.lifecycleState());
    assertEquals(location1.id(), parentAsset.locationId());

    AssetLocation location2 = util.createAssetLocation(region.id(), AssetLocationType.SPARE);
    String lifecycleState = AssetLifecycleState.AVAILABLE.toString();
    var ex =
        assertThrows(
            RuntimeException.class,
            () ->
                assetRepository.moveAssetToLocation(
                    parentAsset.regionId(),
                    parentAsset.id(),
                    parentAsset.version(),
                    location2.id(),
                    lifecycleState,
                    uniqueAuditCreatedById,
                    parentAsset.subInventory().toString()));
    var sqlEx = (SQLException) ex.getCause();
    assertEquals(20001, sqlEx.getErrorCode());
    assertTrue(
        sqlEx
            .getMessage()
            .contains(
                "Asset in RECEIVED state can only be kept in RECEIVED state. Trying to move to: "
                    + lifecycleState.toString()));
  }

  @Test
  void testMoveAssetToLocationFromInTransitToReceived() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    var region = createEntityTestHelper.createRegionEntity();
    String airportCode = region.airportCode();
    String regionId = regionService.getRegionByAirportCodeOrFail(airportCode).id();
    final String oraclePartNumber = "7338187";
    final String manufacturerPartNumber = "35096952";
    util.createCatalogPart(oraclePartNumber, manufacturerPartNumber);

    // Create Asset
    Optional<Asset> createdAsset =
        createAssetwithLifecycleState(
            regionId,
            "**********",
            oraclePartNumber,
            manufacturerPartNumber,
            null,
            null,
            AssetLifecycleState.IN_TRANSIT,
            "oracle-tag-xyz",
            "E2:7F:1C:A3:B8:D5",
            uniqueAuditCreatedById);
    assertTrue(createdAsset.isPresent());
    Asset retrieved = createdAsset.get();
    assertEquals(AssetLifecycleState.IN_TRANSIT, retrieved.lifecycleState());

    AssetLocation location = util.createAssetLocation(region.id());

    @Nullable String lifecycleState = AssetLifecycleState.RECEIVED.toString();
    assetRepository.moveAssetToLocation(
        retrieved.regionId(),
        retrieved.id(),
        retrieved.version(),
        location.id(),
        lifecycleState,
        uniqueAuditCreatedById,
        retrieved.subInventory().toString());
    retrieved = util.getAsset(retrieved.id());
    assertEquals(AssetLifecycleState.RECEIVED, retrieved.lifecycleState());

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.MOVE_ASSET_TO_LOCATION);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.SUCCESS, auditEvents.get(0).eventResponse().eventStatus());
  }

  @Test
  void testMoveAssetFromGeneraToFCOSubInventory() {
    // MOVE ASSET TEST CASES
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    RegionEntity region = util.createRegion();
    AssetLocation location = util.createAssetLocation(region.id());
    Asset asset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .locationId(location.id())
                .subInventory(AssetSubInventory.GENERAL_SPARES)
                .build());
    AssetLocation locationNew = util.createAssetLocation(region.id());

    Asset retrieved = repository.findById(asset.id()).get();
    assertEquals(AssetLifecycleState.AVAILABLE, retrieved.lifecycleState());
    assertEquals(location.id(), retrieved.locationId());

    // Act
    @Nullable String subInventory = AssetSubInventory.FCO_SPARES.toString();
    assetRepository.moveAssetToLocation(
        retrieved.regionId(),
        retrieved.id(),
        retrieved.version(),
        locationNew.id(),
        retrieved.lifecycleState().toString(),
        uniqueAuditCreatedById,
        subInventory);

    // Assert
    Asset updated = repository.findById(retrieved.id()).get();
    assertEquals(subInventory, updated.subInventory().toString());
    assertEquals(retrieved.version() + 1, updated.version());
    assertEquals(locationNew.id(), updated.locationId());

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.MOVE_ASSET_TO_LOCATION);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.SUCCESS, auditEvents.get(0).eventResponse().eventStatus());
  }

  @Test
  void testMoveAssetFromAcquiredToInvalidState() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    var region = createEntityTestHelper.createRegionEntity();
    String airportCode = region.airportCode();
    String regionId = regionService.getRegionByAirportCodeOrFail(airportCode).id();
    final String oraclePartNumber = "7338187";
    final String manufacturerPartNumber = "35096952";
    var part = util.createCatalogPart(oraclePartNumber, manufacturerPartNumber);

    // Create Asset
    Optional<Asset> createdAsset =
        createAsset(
            regionId,
            "**********",
            oraclePartNumber,
            manufacturerPartNumber,
            null,
            null,
            "oracle-tag-xyz",
            "E2:7F:1C:A3:B8:D5",
            uniqueAuditCreatedById);
    assertTrue(createdAsset.isPresent());
    Asset retrieved = createdAsset.get();
    assertEquals(AssetLifecycleState.AVAILABLE, retrieved.lifecycleState());
    AssetReservation reservation = util.createAssetReservation(region.id(), part.id());

    // Acquire
    assetRepository.acquireAsset(
        retrieved.regionId(),
        retrieved.id(),
        retrieved.version(),
        reservation.id(),
        null,
        uniqueAuditCreatedById,
        retrieved.subInventory().toString());

    // check audit
    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.ACQUIRE_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.SUCCESS, auditEvents.get(0).eventResponse().eventStatus());

    // Move to PENDING_DISPOSAL
    AssetLocation location = util.createAssetLocation(region.id());
    var ex1 =
        assertThrows(
            DataAccessException.class,
            () -> {
              @Nullable String lifecycleState = AssetLifecycleState.PENDING_DISPOSAL.toString();
              assetRepository.moveAssetToLocation(
                  retrieved.regionId(),
                  retrieved.id(),
                  retrieved.version() + 1,
                  location.id(),
                  lifecycleState,
                  uniqueAuditCreatedById,
                  retrieved.subInventory().toString());
            });
    var sqlEx1 = (SQLException) ex1.getCause();
    assertEquals(20001, sqlEx1.getErrorCode());
    assertTrue(
        sqlEx1
            .getMessage()
            .contains("Cannot move Asset " + retrieved.id().toString() + " to PENDING_DISPOSAL"));

    // check audit
    auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, "MOVE_ASSET_TO_LOCATION");
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.FAILED, auditEvents.get(0).eventResponse().eventStatus());

    // Move to PENDING_DESTRUCTION
    var ex2 =
        assertThrows(
            DataAccessException.class,
            () -> {
              @Nullable String lifecycleState = AssetLifecycleState.PENDING_DESTRUCTION.toString();
              assetRepository.moveAssetToLocation(
                  retrieved.regionId(),
                  retrieved.id(),
                  retrieved.version() + 1,
                  location.id(),
                  lifecycleState,
                  uniqueAuditCreatedById,
                  retrieved.subInventory().toString());
            });
    var sqlEx2 = (SQLException) ex2.getCause();
    assertEquals(20001, sqlEx2.getErrorCode());
    assertTrue(
        sqlEx2
            .getMessage()
            .contains(
                "Cannot move Asset " + retrieved.id().toString() + " to PENDING_DESTRUCTION"));

    // check audit
    auditEvents =
        createEntityTestHelper
            .listAuditEventsByUpdatedByAndEventName(
                uniqueAuditCreatedById, "MOVE_ASSET_TO_LOCATION")
            .stream()
            .filter(
                auditEvent -> auditEvent.eventResponse().eventStatus().equals(EventStatus.FAILED))
            .toList();
    assertEquals(2, auditEvents.size());

    // Move to PENDING_RETURN
    var ex3 =
        assertThrows(
            DataAccessException.class,
            () -> {
              @Nullable String lifecycleState = AssetLifecycleState.PENDING_RETURN.toString();
              assetRepository.moveAssetToLocation(
                  retrieved.regionId(),
                  retrieved.id(),
                  retrieved.version() + 1,
                  location.id(),
                  lifecycleState,
                  uniqueAuditCreatedById,
                  retrieved.subInventory().toString());
            });
    var sqlEx3 = (SQLException) ex3.getCause();
    assertEquals(20001, sqlEx3.getErrorCode());
    assertTrue(
        sqlEx3
            .getMessage()
            .contains("Cannot move Asset " + retrieved.id().toString() + " to PENDING_RETURN"));

    auditEvents =
        createEntityTestHelper
            .listAuditEventsByUpdatedByAndEventName(
                uniqueAuditCreatedById, "MOVE_ASSET_TO_LOCATION")
            .stream()
            .filter(
                auditEvent -> auditEvent.eventResponse().eventStatus().equals(EventStatus.FAILED))
            .toList();
    assertEquals(3, auditEvents.size());

    // Move to PENDING_VALIDATION
    var ex4 =
        assertThrows(
            DataAccessException.class,
            () -> {
              @Nullable String lifecycleState = AssetLifecycleState.PENDING_VALIDATION.toString();
              assetRepository.moveAssetToLocation(
                  retrieved.regionId(),
                  retrieved.id(),
                  retrieved.version() + 1,
                  location.id(),
                  lifecycleState,
                  uniqueAuditCreatedById,
                  retrieved.subInventory().toString());
            });
    var sqlEx4 = (SQLException) ex4.getCause();
    assertEquals(20001, sqlEx4.getErrorCode());
    assertTrue(
        sqlEx4
            .getMessage()
            .contains("Cannot move Asset " + retrieved.id().toString() + " to PENDING_VALIDATION"));

    auditEvents =
        createEntityTestHelper
            .listAuditEventsByUpdatedByAndEventName(
                uniqueAuditCreatedById, "MOVE_ASSET_TO_LOCATION")
            .stream()
            .filter(
                auditEvent -> auditEvent.eventResponse().eventStatus().equals(EventStatus.FAILED))
            .toList();
    assertEquals(4, auditEvents.size());

    // Move to FAILED
    var ex5 =
        assertThrows(
            DataAccessException.class,
            () -> {
              @Nullable String lifecycleState = AssetLifecycleState.FAILED.toString();
              assetRepository.moveAssetToLocation(
                  retrieved.regionId(),
                  retrieved.id(),
                  retrieved.version() + 1,
                  location.id(),
                  lifecycleState,
                  uniqueAuditCreatedById,
                  retrieved.subInventory().toString());
            });
    var sqlEx5 = (SQLException) ex5.getCause();
    assertEquals(20001, sqlEx5.getErrorCode());
    assertTrue(
        sqlEx5
            .getMessage()
            .contains("Cannot move Asset " + retrieved.id().toString() + " to FAILED"));

    auditEvents =
        createEntityTestHelper
            .listAuditEventsByUpdatedByAndEventName(
                uniqueAuditCreatedById, "MOVE_ASSET_TO_LOCATION")
            .stream()
            .filter(
                auditEvent -> auditEvent.eventResponse().eventStatus().equals(EventStatus.FAILED))
            .toList();
    assertEquals(5, auditEvents.size());
  }

  @Test
  void testMoveAssetFromAcquiredToInstalled() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    var region = createEntityTestHelper.createRegionEntity();
    String airportCode = region.airportCode();
    String regionId = regionService.getRegionByAirportCodeOrFail(airportCode).id();
    final String oraclePartNumber = "7338187";
    final String manufacturerPartNumber = "35096952";
    var part = util.createCatalogPart(oraclePartNumber, manufacturerPartNumber);

    // Create Asset
    Optional<Asset> createdAsset =
        createAsset(
            regionId,
            "**********",
            oraclePartNumber,
            manufacturerPartNumber,
            null,
            null,
            "oracle-tag-xyz",
            "E2:7F:1C:A3:B8:D5",
            uniqueAuditCreatedById);
    assertTrue(createdAsset.isPresent());
    Asset retrieved = createdAsset.get();
    assertEquals(AssetLifecycleState.AVAILABLE, retrieved.lifecycleState());
    AssetReservation reservation = util.createAssetReservation(region.id(), part.id());

    // Acquire
    assetRepository.acquireAsset(
        retrieved.regionId(),
        retrieved.id(),
        retrieved.version(),
        reservation.id(),
        null,
        uniqueAuditCreatedById,
        retrieved.subInventory().toString());

    List<AuditEvent> auditEvents =
        createEntityTestHelper
            .listAuditEventsByUpdatedByAndEventName(
                uniqueAuditCreatedById, InventoryAuditEventConstants.ACQUIRE_ASSET)
            .stream()
            .filter(
                auditEvent -> auditEvent.eventResponse().eventStatus().equals(EventStatus.SUCCESS))
            .toList();
    assertEquals(1, auditEvents.size());

    AssetLocation location = util.createAssetLocation(region.id());

    // Move to INSTALLED
    var ex1 =
        assertThrows(
            DataAccessException.class,
            () -> {
              @Nullable String lifecycleState = AssetLifecycleState.INSTALLED.toString();
              assetRepository.moveAssetToLocation(
                  retrieved.regionId(),
                  retrieved.id(),
                  retrieved.version() + 1,
                  location.id(),
                  lifecycleState,
                  uniqueAuditCreatedById,
                  retrieved.subInventory().toString());
            });
    var sqlEx1 = (SQLException) ex1.getCause();
    assertEquals(20001, sqlEx1.getErrorCode());
    assertTrue(
        sqlEx1.getMessage().contains("Invalid lifecycle state specified for move to location"));

    @Nullable RmcId rackInstanceId = RmcId.generate(RmcIdType.DCMS.DEVICE_INSTANCE);
    @Nullable String lifecycleState = AssetLifecycleState.INSTALLED.toString();
    assetRepository.moveAssetToRack(
        retrieved.regionId(),
        retrieved.id(),
        retrieved.version() + 1,
        rackInstanceId,
        "elevation",
        lifecycleState,
        uniqueAuditCreatedById);

    auditEvents =
        createEntityTestHelper
            .listAuditEventsByUpdatedByAndEventName(uniqueAuditCreatedById, "MOVE_ASSET_TO_RACK")
            .stream()
            .filter(
                auditEvent -> auditEvent.eventResponse().eventStatus().equals(EventStatus.SUCCESS))
            .toList();
    assertEquals(1, auditEvents.size());

    // Assert
    Asset updated = repository.findById(retrieved.id()).get();
    assertEquals(AssetLifecycleState.INSTALLED, updated.lifecycleState());
    assertEquals(retrieved.version() + 2, updated.version());
    assertNotNull(updated.rackInstanceId());
    assertNull(updated.locationId());
  }

  @Test
  void testMoveInstalledAssetToAnotherRack() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    var region = createEntityTestHelper.createRegionEntity();
    String airportCode = region.airportCode();
    String regionId = regionService.getRegionByAirportCodeOrFail(airportCode).id();
    final String oraclePartNumber = "7338187";
    final String manufacturerPartNumber = "35096952";
    var part = util.createCatalogPart(oraclePartNumber, manufacturerPartNumber);

    // Create Asset
    Optional<Asset> createdAsset =
        createAsset(
            regionId,
            "**********",
            oraclePartNumber,
            manufacturerPartNumber,
            null,
            null,
            "oracle-tag-xyz",
            "E2:7F:1C:A3:B8:D5",
            uniqueAuditCreatedById);
    assertTrue(createdAsset.isPresent());
    Asset retrieved = createdAsset.get();
    assertEquals(AssetLifecycleState.AVAILABLE, retrieved.lifecycleState());
    AssetReservation reservation = util.createAssetReservation(region.id(), part.id());

    // Acquire
    assetRepository.acquireAsset(
        retrieved.regionId(),
        retrieved.id(),
        retrieved.version(),
        reservation.id(),
        null,
        uniqueAuditCreatedById,
        retrieved.subInventory().toString());

    List<AuditEvent> auditEvents =
        createEntityTestHelper
            .listAuditEventsByUpdatedByAndEventName(uniqueAuditCreatedById, "ACQUIRE_ASSET")
            .stream()
            .filter(
                auditEvent -> auditEvent.eventResponse().eventStatus().equals(EventStatus.SUCCESS))
            .toList();
    assertEquals(1, auditEvents.size());

    @Nullable RmcId rackInstanceId1 = RmcId.generate(RmcIdType.DCMS.DEVICE_INSTANCE);
    @Nullable String lifecycleState1 = AssetLifecycleState.INSTALLED.toString();
    assetRepository.moveAssetToRack(
        retrieved.regionId(),
        retrieved.id(),
        retrieved.version() + 1,
        rackInstanceId1,
        "elevation",
        lifecycleState1,
        uniqueAuditCreatedById);

    auditEvents =
        createEntityTestHelper
            .listAuditEventsByUpdatedByAndEventName(uniqueAuditCreatedById, "MOVE_ASSET_TO_RACK")
            .stream()
            .filter(
                auditEvent -> auditEvent.eventResponse().eventStatus().equals(EventStatus.SUCCESS))
            .toList();
    assertEquals(1, auditEvents.size());

    // Act

    @Nullable RmcId rackInstanceId = RmcId.generate(RmcIdType.DCMS.DEVICE_INSTANCE);
    @Nullable String lifecycleState = AssetLifecycleState.INSTALLED.toString();
    assetRepository.moveAssetToRack(
        retrieved.regionId(),
        retrieved.id(),
        retrieved.version() + 2,
        rackInstanceId,
        "elevation",
        lifecycleState,
        uniqueAuditCreatedById);

    auditEvents =
        createEntityTestHelper
            .listAuditEventsByUpdatedByAndEventName(uniqueAuditCreatedById, "MOVE_ASSET_TO_RACK")
            .stream()
            .filter(
                auditEvent -> auditEvent.eventResponse().eventStatus().equals(EventStatus.SUCCESS))
            .toList();
    assertEquals(2, auditEvents.size());

    // Assert
    Asset updated = repository.findById(retrieved.id()).get();
    assertEquals(AssetLifecycleState.INSTALLED, updated.lifecycleState());
    assertEquals(retrieved.version() + 3, updated.version());
    assertNotNull(updated.rackInstanceId());
    assertNull(updated.locationId());
  }

  // @Test
  void testMoveInstalledAssetToFailed() {
    // Arrange
    var region = createEntityTestHelper.createRegionEntity();
    String airportCode = region.airportCode();
    String regionId = regionService.getRegionByAirportCodeOrFail(airportCode).id();
    final String oraclePartNumber = "7338187";
    final String manufacturerPartNumber = "35096952";
    var part = util.createCatalogPart(oraclePartNumber, manufacturerPartNumber);

    // Create Asset
    Optional<Asset> createdAsset =
        createAsset(
            regionId,
            "**********",
            oraclePartNumber,
            manufacturerPartNumber,
            null,
            null,
            "oracle-tag-xyz",
            "E2:7F:1C:A3:B8:D5",
            TEST_USER);
    assertTrue(createdAsset.isPresent());
    Asset retrieved = createdAsset.get();
    assertEquals(AssetLifecycleState.AVAILABLE, retrieved.lifecycleState());
    AssetReservation reservation = util.createAssetReservation(region.id(), part.id());
    AssetLocation location = util.createAssetLocation(region.id());

    // Acquire
    assetRepository.acquireAsset(
        retrieved.regionId(),
        retrieved.id(),
        retrieved.version(),
        reservation.id(),
        null,
        TEST_USER,
        retrieved.subInventory().toString());

    @Nullable RmcId rackInstanceId = RmcId.generate(RmcIdType.DCMS.DEVICE_INSTANCE);
    @Nullable String lifecycleState = AssetLifecycleState.INSTALLED.toString();
    assetRepository.moveAssetToRack(
        retrieved.regionId(),
        retrieved.id(),
        retrieved.version() + 1,
        rackInstanceId,
        "elevation",
        lifecycleState,
        TEST_USER);

    // Act
    @Nullable String lifecycleState1 = AssetLifecycleState.FAILED.toString();
    assetRepository.moveAssetToLocation(
        retrieved.regionId(),
        retrieved.id(),
        retrieved.version() + 2,
        location.id(),
        lifecycleState1,
        TEST_USER,
        retrieved.subInventory().toString());

    Asset updated = repository.findById(retrieved.id()).get();
    assertEquals(AssetLifecycleState.FAILED, updated.lifecycleState());
    assertEquals(retrieved.version() + 3, updated.version());
    assertNotNull(updated.rackInstanceId());
    assertNull(updated.locationId());
  }

  // @Test
  void testMoveFailedAssetToPendingValidation() {
    // Arrange
    var region = createEntityTestHelper.createRegionEntity();
    String airportCode = region.airportCode();
    String regionId = regionService.getRegionByAirportCodeOrFail(airportCode).id();
    final String oraclePartNumber = "7338187";
    final String manufacturerPartNumber = "35096952";
    var part = util.createCatalogPart(oraclePartNumber, manufacturerPartNumber);

    // Create Asset
    Optional<Asset> createdAsset =
        createAsset(
            regionId,
            "**********",
            oraclePartNumber,
            manufacturerPartNumber,
            null,
            null,
            "oracle-tag-xyz",
            "E2:7F:1C:A3:B8:D5",
            TEST_USER);
    assertTrue(createdAsset.isPresent());
    Asset retrieved = createdAsset.get();
    assertEquals(AssetLifecycleState.AVAILABLE, retrieved.lifecycleState());
    AssetReservation reservation = util.createAssetReservation(region.id(), part.id());
    AssetLocation location = util.createAssetLocation(region.id());

    // Acquire
    assetRepository.acquireAsset(
        retrieved.regionId(),
        retrieved.id(),
        retrieved.version(),
        reservation.id(),
        null,
        TEST_USER,
        retrieved.subInventory().toString());

    @Nullable RmcId rackInstanceId1 = RmcId.generate(RmcIdType.DCMS.DEVICE_INSTANCE);
    @Nullable String lifecycleState1 = AssetLifecycleState.INSTALLED.toString();
    assetRepository.moveAssetToRack(
        retrieved.regionId(),
        retrieved.id(),
        retrieved.version() + 1,
        rackInstanceId1,
        "elevation",
        lifecycleState1,
        TEST_USER);

    @Nullable String lifecycleState2 = AssetLifecycleState.FAILED.toString();
    assetRepository.moveAssetToLocation(
        retrieved.regionId(),
        retrieved.id(),
        retrieved.version() + 2,
        location.id(),
        lifecycleState2,
        TEST_USER,
        retrieved.subInventory().toString());

    Asset updated = repository.findById(retrieved.id()).get();

    // todo (andlang) - update this to also check when null for other sproc
    // Assert
    var ex1 =
        assertThrows(
            DataAccessException.class,
            () -> {
              @Nullable String lifecycleState = AssetLifecycleState.INSTALLED.toString();
              assetRepository.moveAssetToLocation(
                  retrieved.regionId(),
                  retrieved.id(),
                  updated.version(),
                  null,
                  lifecycleState,
                  TEST_USER,
                  retrieved.subInventory().toString());
            });
    var sqlEx1 = (SQLException) ex1.getCause();
    assertEquals(20001, sqlEx1.getErrorCode());
    assertTrue(
        sqlEx1.getMessage().contains("At least one of locationId and rackId must be specified"));

    var ex2 =
        assertThrows(
            DataAccessException.class,
            () -> {
              @Nullable RmcId rackInstanceId = RmcId.generate(RmcIdType.DCMS.DEVICE_INSTANCE);
              @Nullable String lifecycleState = AssetLifecycleState.PENDING_VALIDATION.toString();
              assetRepository.moveAssetToRack(
                  retrieved.regionId(),
                  retrieved.id(),
                  updated.version(),
                  rackInstanceId,
                  "elevation",
                  lifecycleState,
                  TEST_USER);
            });
    var sqlEx2 = (SQLException) ex2.getCause();
    assertEquals(20001, sqlEx2.getErrorCode());
    assertTrue(
        sqlEx2
            .getMessage()
            .contains("LocationId must be specified when state = PENDING_VALIDATION"));

    @Nullable String lifecycleState = AssetLifecycleState.PENDING_VALIDATION.toString();
    assetRepository.moveAssetToLocation(
        retrieved.regionId(),
        retrieved.id(),
        updated.version(),
        location.id(),
        lifecycleState,
        TEST_USER,
        retrieved.subInventory().toString());

    // Assert
    Asset moved = repository.findById(retrieved.id()).get();
    assertEquals(AssetLifecycleState.PENDING_VALIDATION, moved.lifecycleState());
    assertEquals(retrieved.version() + 3, moved.version());
    assertNotNull(moved.rackInstanceId());
    assertNull(moved.locationId());
  }

  //  @Test
  void testMoveInTransitAssetBetweenLocations() {
    // Arrange
    RegionEntity region = util.createRegion();
    Asset asset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .build());
    AssetTransfer transfer = util.createAssetTransfer(region.id(), asset.id());
    Asset retrieved = repository.findById(asset.id()).get();
    assertEquals(AssetLifecycleState.AVAILABLE, retrieved.lifecycleState());
    assertNull(retrieved.transferId());

    assetRepository.acquireAsset(
        retrieved.regionId(),
        retrieved.id(),
        retrieved.version(),
        null,
        transfer.id(),
        TEST_USER,
        retrieved.subInventory().toString());

    // Act
    var ex1 =
        assertThrows(
            DataAccessException.class,
            () -> { // invalid location
              @Nullable RmcId locationId = RmcId.generate(RmcIdType.Inventory.ASSET_LOCATION);
              @Nullable String lifecycleState = AssetLifecycleState.AVAILABLE.toString();
              assetRepository.moveAssetToLocation(
                  retrieved.regionId(),
                  retrieved.id(),
                  retrieved.version() + 1,
                  locationId,
                  lifecycleState,
                  TEST_USER,
                  retrieved.subInventory().toString());
            });
    var sqlEx1 = (SQLException) ex1.getCause();
    assertEquals(2291, sqlEx1.getErrorCode());
    assertTrue(sqlEx1.getMessage().contains("parent key not found"));

    AssetLocation location = util.createAssetLocation(region.id());
    AssetLocation inactiveAssetLocation = util.createInactiveAssetLocation(region.id());

    var ex2 =
        assertThrows(
            DataAccessException.class,
            () -> { // inactive location
              @Nullable String lifecycleState = AssetLifecycleState.AVAILABLE.toString();
              assetRepository.moveAssetToLocation(
                  retrieved.regionId(),
                  retrieved.id(),
                  retrieved.version() + 1,
                  inactiveAssetLocation.id(),
                  lifecycleState,
                  TEST_USER,
                  retrieved.subInventory().toString());
            });
    var sqlEx2 = (SQLException) ex2.getCause();
    assertEquals(2291, sqlEx2.getErrorCode());
    assertTrue(sqlEx2.getMessage().contains("Location is inactive"));

    @Nullable String lifecycleState = AssetLifecycleState.AVAILABLE.toString();
    assetRepository.moveAssetToLocation(
        retrieved.regionId(),
        retrieved.id(),
        retrieved.version() + 1,
        location.id(),
        lifecycleState,
        TEST_USER,
        retrieved.subInventory().toString());

    // Assert
    Asset updated = repository.findById(retrieved.id()).get();
    assertEquals(AssetLifecycleState.AVAILABLE, updated.lifecycleState());
    assertEquals(retrieved.version() + 2, updated.version());
    assertEquals(transfer.id(), updated.transferId()); // should transferId be reset ?
  }

  // ACQUIRE ASSET TEST CASES

  @Test
  void testAcquireAsset_reservation() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    RegionEntity region = util.createRegion();
    Asset asset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .build());
    AssetReservation reservation = util.createAssetReservation(region.id(), asset.catalogPartId());
    Asset retrieved = repository.findById(asset.id()).get();
    assertEquals(AssetLifecycleState.AVAILABLE, retrieved.lifecycleState());
    assertNull(retrieved.reservationId());

    // Act
    assetRepository.acquireAsset(
        retrieved.regionId(),
        retrieved.id(),
        retrieved.version(),
        reservation.id(),
        null,
        uniqueAuditCreatedById,
        retrieved.subInventory().toString());

    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.ACQUIRE_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.SUCCESS, auditEvents.get(0).eventResponse().eventStatus());

    // Assert
    Asset updated = repository.findById(retrieved.id()).get();
    assertEquals(AssetLifecycleState.ACQUIRED, updated.lifecycleState());
    assertEquals(retrieved.version() + 1, updated.version());
    assertEquals(reservation.id(), updated.reservationId());
  }

  @Test
  void testAcquireAsset_subInventory() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    RegionEntity region = util.createRegion();
    Asset asset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .subInventory(AssetSubInventory.FCO_SPARES)
                .build());
    AssetReservation reservation = util.createAssetReservation(region.id(), asset.catalogPartId());
    Asset retrieved = repository.findById(asset.id()).get();
    assertEquals(AssetLifecycleState.AVAILABLE, retrieved.lifecycleState());
    assertNull(retrieved.reservationId());

    // Act
    assetRepository.acquireAsset(
        retrieved.regionId(),
        retrieved.id(),
        retrieved.version(),
        reservation.id(),
        null,
        uniqueAuditCreatedById,
        retrieved.subInventory().toString());

    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.ACQUIRE_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.SUCCESS, auditEvents.get(0).eventResponse().eventStatus());

    // Assert
    Asset updated = repository.findById(retrieved.id()).get();
    assertEquals(AssetSubInventory.FCO_SPARES, updated.subInventory());
    assertEquals(AssetLifecycleState.ACQUIRED, updated.lifecycleState());
    assertEquals(retrieved.version() + 1, updated.version());
    assertEquals(reservation.id(), updated.reservationId());
  }

  @Test
  void testAcquireAsset_transfer() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    RegionEntity region = util.createRegion();
    Asset asset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .locationId(util.createAssetLocation(region.id()).id())
                .build());
    AssetTransfer transfer = util.createAssetTransfer(region.id(), asset.id());
    Asset retrieved = repository.findById(asset.id()).get();
    assertEquals(AssetLifecycleState.AVAILABLE, retrieved.lifecycleState());
    assertNull(retrieved.transferId());

    // Act
    assetRepository.acquireAsset(
        retrieved.regionId(),
        retrieved.id(),
        retrieved.version(),
        null,
        transfer.id(),
        uniqueAuditCreatedById,
        retrieved.subInventory().toString());

    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, "ACQUIRE_ASSET");
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.SUCCESS, auditEvents.get(0).eventResponse().eventStatus());

    // Assert
    Asset updated = repository.findById(retrieved.id()).get();
    assertEquals(AssetLifecycleState.IN_TRANSIT, updated.lifecycleState());
    assertEquals(retrieved.version() + 1, updated.version());
    assertEquals(transfer.id(), updated.transferId());
  }

  @Test
  void testAcquireAsset_withChildAsset() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    RegionEntity region = util.createRegion();
    Asset asset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .createdBy(uniqueAuditCreatedById)
                .build());
    Asset retrieved = repository.findById(asset.id()).get();
    assertEquals(AssetLifecycleState.AVAILABLE, retrieved.lifecycleState());
    assertNull(retrieved.reservationId());

    // Create child asset
    util.createAsset(
        AssetCreationDetailsBuilder.builder()
            .regionId(region.id())
            .parentAssetId(asset.id())
            .createdBy(uniqueAuditCreatedById)
            .build());

    // Act
    AssetReservation reservation = util.createAssetReservation(region.id(), asset.catalogPartId());
    assetRepository.acquireAsset(
        retrieved.regionId(),
        retrieved.id(),
        retrieved.version(),
        reservation.id(),
        null,
        uniqueAuditCreatedById,
        retrieved.subInventory().toString());

    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.ACQUIRE_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.SUCCESS, auditEvents.get(0).eventResponse().eventStatus());

    // Assert
    Asset updated = repository.findById(retrieved.id()).get();
    assertEquals(AssetLifecycleState.ACQUIRED, updated.lifecycleState());
    assertEquals(retrieved.version() + 1, updated.version());
    assertEquals(reservation.id(), updated.reservationId());
  }

  @Test
  void testAcquireAsset_badParams() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    RegionEntity region = util.createRegion();
    Asset asset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .locationId(util.createAssetLocation(region.id()).id())
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .build());

    // ReservationId and TransferId empty
    var ex1 =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.acquireAsset(
                    asset.regionId(),
                    asset.id(),
                    asset.version(),
                    null,
                    null,
                    uniqueAuditCreatedById,
                    asset.subInventory().toString()));
    var sqlEx1 = (SQLException) ex1.getCause();
    assertEquals(20001, sqlEx1.getErrorCode());
    assertTrue(
        sqlEx1
            .getMessage()
            .contains("At least one of reservationId and transferId must be specified"));

    List<AuditEvent> auditEvents =
        createEntityTestHelper
            .listAuditEventsByUpdatedByAndEventName(uniqueAuditCreatedById, "ACQUIRE_ASSET")
            .stream()
            .filter(
                auditEvent -> auditEvent.eventResponse().eventStatus().equals(EventStatus.FAILED))
            .toList();
    assertEquals(1, auditEvents.size());

    // Both specified
    AssetReservation reservation = util.createAssetReservation(region.id(), asset.catalogPartId());
    AssetTransfer transfer = util.createAssetTransfer(region.id(), asset.id());
    var ex2 =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.acquireAsset(
                    asset.regionId(),
                    asset.id(),
                    asset.version(),
                    reservation.id(),
                    transfer.id(),
                    uniqueAuditCreatedById,
                    asset.subInventory().toString()));
    var sqlEx2 = (SQLException) ex2.getCause();
    assertEquals(20001, sqlEx2.getErrorCode());
    assertTrue(
        sqlEx2.getMessage().contains("Only one of reservationId and transferId must be specified"));

    auditEvents =
        createEntityTestHelper
            .listAuditEventsByUpdatedByAndEventName(uniqueAuditCreatedById, "ACQUIRE_ASSET")
            .stream()
            .filter(
                auditEvent -> auditEvent.eventResponse().eventStatus().equals(EventStatus.FAILED))
            .toList();
    assertEquals(2, auditEvents.size());

    Long assetVersion = assetRepository.findById(asset.id()).get().version();

    // Invalid reservationId
    var invalidReservationId = RmcId.generate(RmcIdType.Inventory.ASSET_RESERVATION);
    var exInvalidRes =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.acquireAsset(
                    asset.regionId(),
                    asset.id(),
                    assetVersion,
                    invalidReservationId,
                    null,
                    uniqueAuditCreatedById,
                    asset.subInventory().toString()));
    var sqlInvalidRes = (SQLException) exInvalidRes.getCause();
    assertEquals(20001, sqlInvalidRes.getErrorCode());
    assertTrue(
        sqlInvalidRes
            .getMessage()
            .contains("No reservation found with id: " + invalidReservationId));

    auditEvents =
        createEntityTestHelper
            .listAuditEventsByUpdatedByAndEventName(
                uniqueAuditCreatedById, InventoryAuditEventConstants.ACQUIRE_ASSET)
            .stream()
            .filter(
                auditEvent -> auditEvent.eventResponse().eventStatus().equals(EventStatus.FAILED))
            .toList();
    assertEquals(3, auditEvents.size());

    // Invalid transferId
    var invalidTransferId = RmcId.generate(RmcIdType.Inventory.ASSET_TRANSFER);
    var exInvalidTransfer =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.acquireAsset(
                    asset.regionId(),
                    asset.id(),
                    assetVersion,
                    null,
                    invalidTransferId,
                    uniqueAuditCreatedById,
                    asset.subInventory().toString()));
    var sqlInvalidTransfer = (SQLException) exInvalidTransfer.getCause();
    assertEquals(20001, sqlInvalidRes.getErrorCode());
    assertThat(sqlInvalidTransfer.getMessage())
        .contains("No transfer found with id: " + invalidTransferId);

    auditEvents =
        createEntityTestHelper
            .listAuditEventsByUpdatedByAndEventName(
                uniqueAuditCreatedById, InventoryAuditEventConstants.ACQUIRE_ASSET)
            .stream()
            .filter(
                auditEvent -> auditEvent.eventResponse().eventStatus().equals(EventStatus.FAILED))
            .toList();
    assertEquals(4, auditEvents.size());

    // Asset already reserved for reservation

    Asset reservedAsset =
        util.createAssetAndAcquireReservationOrTransfer(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .build(),
            reservation.id(),
            null);

    var ex3 =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.acquireAsset(
                    reservedAsset.regionId(),
                    reservedAsset.id(),
                    reservedAsset.version(),
                    reservation.id(),
                    null,
                    uniqueAuditCreatedById,
                    reservedAsset.subInventory().toString()));
    var sqlEx3 = (SQLException) ex3.getCause();
    assertEquals(20001, sqlEx3.getErrorCode());
    assertTrue(
        sqlEx3
            .getMessage()
            .contains("Asset cannot be acquired when reservationId or transferId is set"));

    auditEvents =
        createEntityTestHelper
            .listAuditEventsByUpdatedByAndEventName(
                uniqueAuditCreatedById, InventoryAuditEventConstants.ACQUIRE_ASSET)
            .stream()
            .filter(
                auditEvent -> auditEvent.eventResponse().eventStatus().equals(EventStatus.FAILED))
            .toList();
    assertEquals(5, auditEvents.size());

    // Asset already reserved for transfer

    Asset transferAsset =
        util.createAssetAndAcquireReservationOrTransfer(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .build(),
            null,
            transfer.id());
    var ex4 =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.acquireAsset(
                    transferAsset.regionId(),
                    transferAsset.id(),
                    transferAsset.version(),
                    reservation.id(),
                    null,
                    uniqueAuditCreatedById,
                    transferAsset.subInventory().toString()));
    var sqlEx4 = (SQLException) ex4.getCause();
    assertEquals(20001, sqlEx4.getErrorCode());
    assertTrue(
        sqlEx4
            .getMessage()
            .contains("Asset cannot be acquired when reservationId or transferId is set"));

    auditEvents =
        createEntityTestHelper
            .listAuditEventsByUpdatedByAndEventName(
                uniqueAuditCreatedById, InventoryAuditEventConstants.ACQUIRE_ASSET)
            .stream()
            .filter(
                auditEvent -> auditEvent.eventResponse().eventStatus().equals(EventStatus.FAILED))
            .toList();
    assertEquals(6, auditEvents.size());

    // Invalid state transition for reservation
    Asset asset2 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.INSTALLED)
                .build());
    var ex5 =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.acquireAsset(
                    asset2.regionId(),
                    asset2.id(),
                    asset2.version(),
                    reservation.id(),
                    null,
                    uniqueAuditCreatedById,
                    asset2.subInventory().toString()));
    var sqlEx5 = (SQLException) ex5.getCause();
    assertEquals(20001, sqlEx5.getErrorCode());
    assertTrue(sqlEx5.getMessage().contains("Invalid state transition"));

    auditEvents =
        createEntityTestHelper
            .listAuditEventsByUpdatedByAndEventName(
                uniqueAuditCreatedById, InventoryAuditEventConstants.ACQUIRE_ASSET)
            .stream()
            .filter(
                auditEvent -> auditEvent.eventResponse().eventStatus().equals(EventStatus.FAILED))
            .toList();
    assertEquals(7, auditEvents.size());

    // Invalid state transition for transfer
    Asset asset3 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.INSTALLED)
                .build());
    var ex6 =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.acquireAsset(
                    asset3.regionId(),
                    asset3.id(),
                    asset3.version(),
                    null,
                    transfer.id(),
                    uniqueAuditCreatedById,
                    asset3.subInventory().toString()));
    var sqlEx6 = (SQLException) ex6.getCause();
    assertEquals(20001, sqlEx6.getErrorCode());
    assertTrue(sqlEx6.getMessage().contains("Invalid state transition"));

    auditEvents =
        createEntityTestHelper
            .listAuditEventsByUpdatedByAndEventName(
                uniqueAuditCreatedById, InventoryAuditEventConstants.ACQUIRE_ASSET)
            .stream()
            .filter(
                auditEvent -> auditEvent.eventResponse().eventStatus().equals(EventStatus.FAILED))
            .toList();
    assertEquals(8, auditEvents.size());

    // Asset not found
    Asset dummyAsset = createDummyAssetObject();
    var ex7 =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.acquireAsset(
                    dummyAsset.regionId(),
                    dummyAsset.id(),
                    dummyAsset.version(),
                    null,
                    transfer.id(),
                    uniqueAuditCreatedById,
                    AssetSubInventory.GENERAL_SPARES.toString()));
    var sqlEx7 = (SQLException) ex7.getCause();
    assertEquals(20001, sqlEx7.getErrorCode());
    assertTrue(sqlEx7.getMessage().contains("No records found for given region, id and version"));

    auditEvents =
        createEntityTestHelper
            .listAuditEventsByUpdatedByAndEventName(
                uniqueAuditCreatedById, InventoryAuditEventConstants.ACQUIRE_ASSET)
            .stream()
            .filter(
                auditEvent -> auditEvent.eventResponse().eventStatus().equals(EventStatus.FAILED))
            .toList();
    assertEquals(9, auditEvents.size());
  }

  @Test
  void testUnsetRackInstance() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    var region = createEntityTestHelper.createRegionEntity();
    String airportCode = region.airportCode();
    String regionId = regionService.getRegionByAirportCodeOrFail(airportCode).id();
    String oraclePartNumber = "7338187";
    String manufacturerPartNumber = "35096952";
    // create first part
    util.createCatalogPart(oraclePartNumber, manufacturerPartNumber);

    // Creating rack asset and move to rack
    Asset parentAsset =
        createAsset(
                regionId,
                "**********",
                oraclePartNumber,
                manufacturerPartNumber,
                null,
                null,
                "oracle-tag-xyz",
                "",
                uniqueAuditCreatedById)
            .get();

    RmcId rackId = RmcId.generate(RmcIdType.DCMS.DEVICE_INSTANCE);
    assetRepository.moveAssetToRack(
        parentAsset.regionId(),
        parentAsset.id(),
        parentAsset.version(),
        rackId,
        null,
        AssetLifecycleState.IN_TRANSIT.toString(),
        uniqueAuditCreatedById);

    // Assert rackInstanceId is set
    Asset gotParentAsset = assetRepository.findById(parentAsset.id()).get();
    assertEquals(rackId, gotParentAsset.rackInstanceId());

    // create second part
    String oraclePartNumber2 = "8187337";
    String manufacturerPartNumber2 = "69350952";
    Part part2 = util.createCatalogPart(oraclePartNumber2, manufacturerPartNumber2);

    // Create child assets
    final Asset childAsset1 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(regionId)
                .serialNumber("SK00410765")
                .catalogPartId(part2.id())
                .parentAssetId(parentAsset.id())
                .createdBy(uniqueAuditCreatedById)
                .build());
    final Asset childAsset2 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(regionId)
                .serialNumber("SK00410766")
                .catalogPartId(part2.id())
                .parentAssetId(parentAsset.id())
                .createdBy(uniqueAuditCreatedById)
                .build());

    // Assert parent's rackInstanceId is set on child assets
    Asset gotChildAsset1 = assetRepository.findById(childAsset1.id()).get();
    assertEquals(rackId, gotChildAsset1.rackInstanceId());

    Asset gotChildAsset2 = assetRepository.findById(childAsset2.id()).get();
    assertEquals(rackId, gotChildAsset2.rackInstanceId());

    // Unset rack instance on parent
    assetRepository.unsetRackInstanceFromAsset(
        regionId, gotParentAsset.id(), gotParentAsset.version(), uniqueAuditCreatedById);

    // Assert rackInstanceId has been unset from parent and child assets
    gotParentAsset = assetRepository.findById(parentAsset.id()).get();
    assertNull(gotParentAsset.rackInstanceId());
    assertEquals(AssetLifecycleState.IN_TRANSIT, gotParentAsset.lifecycleState());
    gotChildAsset1 = assetRepository.findById(childAsset1.id()).get();
    assertNull(gotChildAsset1.rackInstanceId());
    assertEquals(AssetLifecycleState.INSTALLED, gotChildAsset1.lifecycleState());
    gotChildAsset2 = assetRepository.findById(childAsset2.id()).get();
    assertNull(gotChildAsset2.rackInstanceId());
    assertEquals(AssetLifecycleState.INSTALLED, gotChildAsset2.lifecycleState());

    // Check audit events were emitted
    List<AuditEvent> auditEvents =
        createEntityTestHelper
            .listAuditEventsByUpdatedByAndEventName(
                uniqueAuditCreatedById, InventoryAuditEventConstants.UNSET_RACK_INSTANCE_FROM_ASSET)
            .stream()
            .filter(
                auditEvent -> auditEvent.eventResponse().eventStatus().equals(EventStatus.SUCCESS))
            .toList();
    assertEquals(3, auditEvents.size());
  }

  @Test
  void testUnsetRackInstance_In_Returned_State() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    var region = createEntityTestHelper.createRegionEntity();
    String airportCode = region.airportCode();
    String regionId = regionService.getRegionByAirportCodeOrFail(airportCode).id();
    String oraclePartNumber = "7338187";
    String manufacturerPartNumber = "35096952";
    // create first part
    util.createCatalogPart(oraclePartNumber, manufacturerPartNumber);

    // Creating rack asset and move to rack
    Asset parentAsset =
        createAsset(
                regionId,
                "**********",
                oraclePartNumber,
                manufacturerPartNumber,
                null,
                null,
                "oracle-tag-xyz",
                "",
                uniqueAuditCreatedById)
            .get();

    RmcId rackId = RmcId.generate(RmcIdType.DCMS.DEVICE_INSTANCE);
    assetRepository.moveAssetToRack(
        parentAsset.regionId(),
        parentAsset.id(),
        parentAsset.version(),
        rackId,
        null,
        AssetLifecycleState.IN_TRANSIT.toString(),
        uniqueAuditCreatedById);

    // Assert rackInstanceId is set
    Asset gotParentAsset = assetRepository.findById(parentAsset.id()).get();
    assertEquals(rackId, gotParentAsset.rackInstanceId());

    // create second part
    String oraclePartNumber2 = "8187337";
    String manufacturerPartNumber2 = "69350952";
    Part part2 = util.createCatalogPart(oraclePartNumber2, manufacturerPartNumber2);

    // Create child assets
    final Asset childAsset1 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(regionId)
                .serialNumber("SK00410765")
                .catalogPartId(part2.id())
                .parentAssetId(parentAsset.id())
                .createdBy(uniqueAuditCreatedById)
                .build());
    final Asset childAsset2 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(regionId)
                .serialNumber("SK00410766")
                .catalogPartId(part2.id())
                .parentAssetId(parentAsset.id())
                .createdBy(uniqueAuditCreatedById)
                .build());

    // Assert parent's rackInstanceId is set on child assets
    Asset gotChildAsset1 = assetRepository.findById(childAsset1.id()).get();
    assertEquals(rackId, gotChildAsset1.rackInstanceId());

    Asset gotChildAsset2 = assetRepository.findById(childAsset2.id()).get();
    assertEquals(rackId, gotChildAsset2.rackInstanceId());

    assetRepository.returnAsset(
        regionId, gotParentAsset.id(), gotParentAsset.version(), uniqueAuditCreatedById);

    // Unset rack instance on parent
    assetRepository.unsetRackInstanceFromAsset(
        regionId, gotParentAsset.id(), gotParentAsset.version() + 1, uniqueAuditCreatedById);

    // Assert rackInstanceId has been unset from parent and child assets
    gotParentAsset = assetRepository.findById(parentAsset.id()).get();
    assertNull(gotParentAsset.rackInstanceId());
    assertEquals(AssetLifecycleState.RETURNED, gotParentAsset.lifecycleState());
    gotChildAsset1 = assetRepository.findById(childAsset1.id()).get();
    assertNull(gotChildAsset1.rackInstanceId());
    assertEquals(AssetLifecycleState.RETURNED, gotChildAsset1.lifecycleState());
    gotChildAsset2 = assetRepository.findById(childAsset2.id()).get();
    assertNull(gotChildAsset2.rackInstanceId());
    assertEquals(AssetLifecycleState.RETURNED, gotChildAsset2.lifecycleState());

    // Check audit events were emitted
    List<AuditEvent> auditEvents =
        createEntityTestHelper
            .listAuditEventsByUpdatedByAndEventName(
                uniqueAuditCreatedById, InventoryAuditEventConstants.UNSET_RACK_INSTANCE_FROM_ASSET)
            .stream()
            .filter(
                auditEvent -> auditEvent.eventResponse().eventStatus().equals(EventStatus.SUCCESS))
            .toList();
    assertEquals(3, auditEvents.size());
  }

  @Test
  void testUnsetRackInstance_invalidRackAssetState() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    var region = createEntityTestHelper.createRegionEntity();
    String airportCode = region.airportCode();
    String regionId = regionService.getRegionByAirportCodeOrFail(airportCode).id();
    String oraclePartNumber = "7338187";
    String manufacturerPartNumber = "35096952";
    // create first part
    util.createCatalogPart(oraclePartNumber, manufacturerPartNumber);

    // Creating rack asset and move to rack
    Asset parentAsset =
        createAsset(
                regionId,
                "**********",
                oraclePartNumber,
                manufacturerPartNumber,
                null,
                null,
                "oracle-tag-xyz",
                "",
                uniqueAuditCreatedById)
            .get();

    RmcId rackId = RmcId.generate(RmcIdType.DCMS.DEVICE_INSTANCE);
    assetRepository.moveAssetToRack(
        parentAsset.regionId(),
        parentAsset.id(),
        parentAsset.version(),
        rackId,
        null,
        AssetLifecycleState.INSTALLED.toString(),
        uniqueAuditCreatedById);

    // Assert rackInstanceId is set
    Asset gotParentAsset = assetRepository.findById(parentAsset.id()).get();
    assertEquals(rackId, gotParentAsset.rackInstanceId());

    // create second part
    String oraclePartNumber2 = "8187337";
    String manufacturerPartNumber2 = "69350952";
    Part part2 = util.createCatalogPart(oraclePartNumber2, manufacturerPartNumber2);

    // Create child asset
    util.createAsset(
        AssetCreationDetailsBuilder.builder()
            .regionId(regionId)
            .serialNumber("SK00410765")
            .catalogPartId(part2.id())
            .parentAssetId(parentAsset.id())
            .createdBy(uniqueAuditCreatedById)
            .lifecycleState(AssetLifecycleState.INSTALLED)
            .build());

    // Assert exception
    var ex =
        assertThrows(
            RuntimeException.class,
            () ->
                assetRepository.unsetRackInstanceFromAsset(
                    regionId,
                    gotParentAsset.id(),
                    gotParentAsset.version(),
                    uniqueAuditCreatedById));
    var sqlEx = (SQLException) ex.getCause();
    assertEquals(20001, sqlEx.getErrorCode());
    assertTrue(
        sqlEx.getMessage().contains("Cannot unset rack instance when asset is in state INSTALLED"));

    // Check audit events were emitted
    List<AuditEvent> auditEvents =
        createEntityTestHelper
            .listAuditEventsByUpdatedByAndEventName(
                uniqueAuditCreatedById, InventoryAuditEventConstants.UNSET_RACK_INSTANCE_FROM_ASSET)
            .stream()
            .filter(
                auditEvent -> auditEvent.eventResponse().eventStatus().equals(EventStatus.FAILED))
            .toList();
    assertEquals(1, auditEvents.size());
  }

  @Test
  void testSetAndUnsetRackInstanceOnAsset() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    var region = createEntityTestHelper.createRegionEntity();
    String airportCode = region.airportCode();
    String regionId = regionService.getRegionByAirportCodeOrFail(airportCode).id();
    String oraclePartNumber = "7338187";
    String manufacturerPartNumber = "35096952";

    // Create asset that is in_transit
    var asset =
        createAssetwithLifecycleState(
                regionId,
                "**********",
                oraclePartNumber,
                manufacturerPartNumber,
                null,
                null,
                AssetLifecycleState.RECEIVED,
                "oracle-tag-xyz",
                "E2:7F:1C:A3:B8:D5",
                uniqueAuditCreatedById)
            .get();

    RmcId rackInstanceId = RmcId.generate(RmcIdType.DCMS.DEVICE_INSTANCE);

    assetRepository.setRackInstanceOnAsset(
        regionId, asset.id(), rackInstanceId, asset.version() + 1, uniqueAuditCreatedById);
    var fetchedAsset = assetRepository.findById(asset.id()).get();
    assertEquals(rackInstanceId, fetchedAsset.rackInstanceId());
    assertEquals(AssetLifecycleState.RECEIVED, asset.lifecycleState());

    // unset
    assetRepository.unsetRackInstanceFromAsset(
        regionId, asset.id(), fetchedAsset.version() + 1, uniqueAuditCreatedById);

    fetchedAsset = assetRepository.findById(asset.id()).get();
    assertNull(fetchedAsset.rackInstanceId());
    assertEquals(AssetLifecycleState.RECEIVED, fetchedAsset.lifecycleState());
  }

  @Test
  void testSetRackInstanceOnAsset() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    var region = createEntityTestHelper.createRegionEntity();
    String airportCode = region.airportCode();
    String regionId = regionService.getRegionByAirportCodeOrFail(airportCode).id();
    String oraclePartNumber = "7338187";
    String manufacturerPartNumber = "35096952";

    // create first part
    util.createCatalogPart(oraclePartNumber, manufacturerPartNumber);

    // Create asset that is in_transit
    var parentAsset =
        createAssetwithLifecycleState(
                regionId,
                "**********",
                oraclePartNumber,
                manufacturerPartNumber,
                null,
                null,
                AssetLifecycleState.IN_TRANSIT,
                "oracle-tag-xyz",
                "E2:7F:1C:A3:B8:D5",
                uniqueAuditCreatedById)
            .get();
    assertEquals(AssetLifecycleState.IN_TRANSIT, parentAsset.lifecycleState());

    // create second part
    String oraclePartNumber2 = "8187337";
    String manufacturerPartNumber2 = "69350952";
    Part part2 = util.createCatalogPart(oraclePartNumber2, manufacturerPartNumber2);

    // Create child assets
    final var childAsset1 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(regionId)
                .serialNumber("SK00410765")
                .catalogPartId(part2.id())
                .parentAssetId(parentAsset.id())
                .createdBy(uniqueAuditCreatedById)
                .lifecycleState(AssetLifecycleState.INSTALLED)
                .build());
    final var childAsset2 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(regionId)
                .serialNumber("SK00410766")
                .catalogPartId(part2.id())
                .parentAssetId(parentAsset.id())
                .createdBy(uniqueAuditCreatedById)
                .lifecycleState(AssetLifecycleState.INSTALLED)
                .build());

    RmcId rackInstanceId = RmcId.generate(RmcIdType.DCMS.DEVICE_INSTANCE);

    assetRepository.setRackInstanceOnAsset(
        regionId,
        parentAsset.id(),
        rackInstanceId,
        parentAsset.version() + 1,
        uniqueAuditCreatedById);

    var fetchedParentAsset = assetRepository.findById(parentAsset.id()).get();
    assertEquals(rackInstanceId, fetchedParentAsset.rackInstanceId());
    assertEquals(AssetLifecycleState.IN_TRANSIT, parentAsset.lifecycleState());
    var fetchedChildAsset1 = assetRepository.findById(childAsset1.id()).get();
    assertEquals(rackInstanceId, fetchedChildAsset1.rackInstanceId());
    assertEquals(AssetLifecycleState.INSTALLED, fetchedChildAsset1.lifecycleState());
    var fetchedChildAsset2 = assetRepository.findById(childAsset2.id()).get();
    assertEquals(rackInstanceId, fetchedChildAsset2.rackInstanceId());
    assertEquals(AssetLifecycleState.INSTALLED, fetchedChildAsset2.lifecycleState());

    // Check audit events were emitted
    List<AuditEvent> auditEvents =
        createEntityTestHelper
            .listAuditEventsByUpdatedByAndEventName(
                uniqueAuditCreatedById, InventoryAuditEventConstants.SET_RACK_INSTANCE_ON_ASSET)
            .stream()
            .filter(
                auditEvent -> auditEvent.eventResponse().eventStatus().equals(EventStatus.SUCCESS))
            .toList();
    assertEquals(3, auditEvents.size());
  }

  @Test
  void testSetRackInstanceOnAsset_InvalidRackAssetState() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    var region = createEntityTestHelper.createRegionEntity();
    String airportCode = region.airportCode();
    String regionId = regionService.getRegionByAirportCodeOrFail(airportCode).id();
    String oraclePartNumber = "7338187";
    String manufacturerPartNumber = "35096952";
    // create first part
    var part1 = util.createCatalogPart(oraclePartNumber, manufacturerPartNumber);

    var parentAsset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(regionId)
                .serialNumber("**********")
                .catalogPartId(part1.id())
                .createdBy(uniqueAuditCreatedById)
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .build());

    // create second part
    String oraclePartNumber2 = "8187337";
    String manufacturerPartNumber2 = "69350952";
    Part part2 = util.createCatalogPart(oraclePartNumber2, manufacturerPartNumber2);

    // Create child assets
    util.createAsset(
        AssetCreationDetailsBuilder.builder()
            .regionId(regionId)
            .serialNumber("SK00410765")
            .catalogPartId(part2.id())
            .parentAssetId(parentAsset.id())
            .createdBy(uniqueAuditCreatedById)
            .lifecycleState(AssetLifecycleState.INSTALLED)
            .build());
    util.createAsset(
        AssetCreationDetailsBuilder.builder()
            .regionId(regionId)
            .serialNumber("SK00410766")
            .catalogPartId(part2.id())
            .parentAssetId(parentAsset.id())
            .createdBy(uniqueAuditCreatedById)
            .lifecycleState(AssetLifecycleState.INSTALLED)
            .build());

    @Nullable RmcId rackInstanceId = RmcId.generate(RmcIdType.DCMS.DEVICE_INSTANCE);

    var ex =
        assertThrows(
            RuntimeException.class,
            () ->
                assetRepository.setRackInstanceOnAsset(
                    regionId,
                    parentAsset.id(),
                    rackInstanceId,
                    parentAsset.version() + 1,
                    uniqueAuditCreatedById));
    var sqlEx = (SQLException) ex.getCause();
    assertEquals(20001, sqlEx.getErrorCode());
    assertTrue(
        sqlEx.getMessage().contains("Cannot set rack instance when asset is in state AVAILABLE"));

    // Check audit events were emitted
    List<AuditEvent> auditEvents =
        createEntityTestHelper
            .listAuditEventsByUpdatedByAndEventName(
                uniqueAuditCreatedById, InventoryAuditEventConstants.SET_RACK_INSTANCE_ON_ASSET)
            .stream()
            .filter(
                auditEvent -> auditEvent.eventResponse().eventStatus().equals(EventStatus.FAILED))
            .toList();
    assertEquals(1, auditEvents.size());
  }

  @Test
  void testMoveAssetToRack() {
    util.createRackParentEntities();
    var region = createEntityTestHelper.createRegionEntity();
    Asset rackAsset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .catalogPartId(util.createCatalogPart("partnumber", "partnumber").id())
                .serialNumber("SN." + UUID.randomUUID())
                .lifecycleState(AssetLifecycleState.IN_TRANSIT)
                .build());
    // Create a rack model
    RackModel rackModel = util.createAndGetComputeRackModel();
    RackInstanceDetails rackInstance = util.createRackInstance(region, rackAsset, rackModel);
    assetRepository.moveAssetToRack(
        region.id(),
        rackAsset.id(),
        rackAsset.version(),
        RmcId.fromString(rackInstance.id()),
        null,
        AssetLifecycleState.RECEIVED.toString(),
        "AssetRepository-Test");

    Asset updatedAsset = assetRepository.findById(rackAsset.id()).orElse(null);
    assertNotNull(updatedAsset);
    assertEquals(AssetLifecycleState.RECEIVED, updatedAsset.lifecycleState());
    assertEquals(rackInstance.id(), updatedAsset.rackInstanceId().toString());
  }

  @Test
  void testMoveAssetToRackParentStatePendingReturn() {
    util.createRackParentEntities();
    var region = createEntityTestHelper.createRegionEntity();
    Asset rackAsset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .catalogPartId(util.createCatalogPart("partnumber", "partnumber").id())
                .serialNumber("SN." + UUID.randomUUID())
                .lifecycleState(AssetLifecycleState.PENDING_RETURN)
                .build());
    // Create a rack model
    RackModel rackModel = util.createAndGetComputeRackModel();
    RackInstanceDetails rackInstance = util.createRackInstance(region, rackAsset, rackModel);
    assetRepository.moveAssetToRack(
        region.id(),
        rackAsset.id(),
        rackAsset.version(),
        RmcId.fromString(rackInstance.id()),
        null,
        AssetLifecycleState.AVAILABLE.toString(),
        "AssetRepository-Test");

    Asset updatedAsset = assetRepository.findById(rackAsset.id()).orElse(null);
    assertNotNull(updatedAsset);
    assertEquals(AssetLifecycleState.AVAILABLE, updatedAsset.lifecycleState());
    assertEquals(rackInstance.id(), updatedAsset.rackInstanceId().toString());
  }

  @Test
  void testMoveAssetToRackParentStatePendingReturnChildStateMissing() {
    util.createRackParentEntities();
    var region = createEntityTestHelper.createRegionEntity();
    Asset rackAsset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .catalogPartId(util.createCatalogPart("partnumber", "partnumber").id())
                .serialNumber("SN." + UUID.randomUUID())
                .lifecycleState(AssetLifecycleState.PENDING_RETURN)
                .build());
    // Create a rack model
    RackModel rackModel = util.createAndGetComputeRackModel();
    RackInstanceDetails rackInstance = util.createRackInstance(region, rackAsset, rackModel);

    final var childAsset =
        createAssetwithLifecycleState(
                region.id(),
                "SK00410765",
                "8187337",
                "69350952",
                null,
                rackAsset.id(),
                AssetLifecycleState.INSTALLED,
                "oracle-tag-xyz",
                "E2:7F:1C:A3:B8:D5",
                "AssetRepository-Test")
            .get();
    repository.updateAssetLifecycle(childAsset.id(), AssetLifecycleState.PENDING_RETURN.toString());
    Asset updatedChildAsset = assetRepository.findById(childAsset.id()).orElse(null);
    assertEquals(AssetLifecycleState.PENDING_RETURN, updatedChildAsset.lifecycleState());

    assetRepository.moveAssetToRack(
        region.id(),
        rackAsset.id(),
        rackAsset.version(),
        RmcId.fromString(rackInstance.id()),
        null,
        AssetLifecycleState.AVAILABLE.toString(),
        "AssetRepository-Test");

    Asset updatedAsset = assetRepository.findById(rackAsset.id()).orElse(null);
    assertNotNull(updatedAsset);
    assertEquals(AssetLifecycleState.AVAILABLE, updatedAsset.lifecycleState());
    assertEquals(rackInstance.id(), updatedAsset.rackInstanceId().toString());
    updatedChildAsset = assetRepository.findById(childAsset.id()).orElse(null);
    assertEquals(AssetLifecycleState.INSTALLED, updatedChildAsset.lifecycleState());
  }

  @Test
  void testInstallAsset_parentAssetInLocation() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Arrange
    util.createRackParentEntities();
    var region = util.getRegionEntity();
    var part = util.createPart("opn-1", null, RmcId.generate(RmcIdType.Catalog.PART_CATEGORY));
    var assetLocation1 =
        util.createAssetLocation(
            region.id(), util.getDataHall().id(), util.getBuildingEntity().id(), "A");
    var assetLocation2 =
        util.createAssetLocation(
            region.id(), util.getDataHall().id(), util.getBuildingEntity().id(), "B");
    Asset assetToBeInstalled =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .locationId(assetLocation1.id())
                .build());
    Asset parentAsset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .locationId(assetLocation2.id())
                .lifecycleState(AssetLifecycleState.RECEIVED)
                .build());

    // Act
    assetRepository.installAsset(
        assetToBeInstalled.regionId(),
        assetToBeInstalled.id(),
        parentAsset.id(),
        assetToBeInstalled.elevation(),
        assetToBeInstalled.version(),
        parentAsset.version(),
        uniqueAuditCreatedById);

    // Assert
    Asset updatedAsset = assetRepository.findById(assetToBeInstalled.id()).orElse(null);
    assertNotNull(updatedAsset);
    assertEquals(AssetLifecycleState.INSTALLED, updatedAsset.lifecycleState());
    assertEquals(parentAsset.id(), updatedAsset.parentAssetId());
    assertEquals(assetLocation2.id(), updatedAsset.locationId());
    assertEquals(assetToBeInstalled.version() + 2L, updatedAsset.version());

    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.INSTALL_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.SUCCESS, auditEvents.get(0).eventResponse().eventStatus());
  }

  @Test
  void testInstallAsset_WithChildAsset_ParentAssetInLocation() {
    // Arrange
    util.createRackParentEntities();
    var region = util.getRegionEntity();
    var part = util.createPart("opn-1", null, RmcId.generate(RmcIdType.Catalog.PART_CATEGORY));
    var assetLocation1 =
        util.createAssetLocation(
            region.id(), util.getDataHall().id(), util.getBuildingEntity().id(), "A");
    var assetLocation2 =
        util.createAssetLocation(
            region.id(), util.getDataHall().id(), util.getBuildingEntity().id(), "B");

    Asset assetToBeInstalled =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .locationId(assetLocation1.id())
                .build());

    Asset parentAsset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .locationId(assetLocation2.id())
                .lifecycleState(AssetLifecycleState.RECEIVED)
                .build());

    Asset childAsset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.INSTALLED)
                .locationId(assetLocation1.id())
                .parentAssetId(assetToBeInstalled.id())
                .build());

    // Act
    assetRepository.installAsset(
        assetToBeInstalled.regionId(),
        assetToBeInstalled.id(),
        parentAsset.id(),
        assetToBeInstalled.elevation(),
        assetToBeInstalled.version(),
        parentAsset.version(),
        "AssetRepository-Test");

    // Assert
    Asset updatedChildAsset = assetRepository.findById(childAsset.id()).orElse(null);
    assertNotNull(updatedChildAsset);
    assertEquals(AssetLifecycleState.INSTALLED, updatedChildAsset.lifecycleState());
    assertEquals(assetToBeInstalled.id(), updatedChildAsset.parentAssetId());
    assertEquals(assetLocation2.id(), updatedChildAsset.locationId());
    assertEquals(childAsset.version() + 1L, updatedChildAsset.version());

    Asset updatedAsset = assetRepository.findById(assetToBeInstalled.id()).orElse(null);
    assertNotNull(updatedAsset);
    assertEquals(AssetLifecycleState.INSTALLED, updatedAsset.lifecycleState());
    assertEquals(parentAsset.id(), updatedAsset.parentAssetId());
    assertEquals(assetLocation2.id(), updatedAsset.locationId());
    assertEquals(assetToBeInstalled.version() + 2L, updatedAsset.version());
  }

  @Test
  void testInstallAsset_ParentAssetInRack() {
    util.createRackParentEntities();
    var region = util.getRegionEntity();

    var assetLocation1 =
        util.createAssetLocation(
            region.id(), util.getDataHall().id(), util.getBuildingEntity().id(), "A");

    Asset rackAsset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .catalogPartId(util.createCatalogPart("partnumber", "partnumber").id())
                .serialNumber("SN." + UUID.randomUUID())
                .lifecycleState(AssetLifecycleState.IN_TRANSIT)
                .build());
    // Create a rack instance
    RackModel rackModel = util.createAndGetComputeRackModel();
    RackInstanceDetails rackInstance = util.createRackInstance(region, rackAsset, rackModel);

    Asset parentAsset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .build());

    assetRepository.moveAssetToRack(
        region.id(),
        parentAsset.id(),
        parentAsset.version(),
        RmcId.fromString(rackInstance.id()),
        null,
        AssetLifecycleState.INSTALLED.toString(),
        "AssetRepository-Test");

    Asset assetToBeInstalled =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .locationId(assetLocation1.id())
                .build());
    Asset childAsset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.INSTALLED)
                .locationId(assetLocation1.id())
                .parentAssetId(assetToBeInstalled.id())
                .build());
    assetRepository.installAsset(
        assetToBeInstalled.regionId(),
        assetToBeInstalled.id(),
        parentAsset.id(),
        assetToBeInstalled.elevation(),
        assetToBeInstalled.version(),
        parentAsset.version() + 1,
        "AssetRepository-Test");
    Asset updatedChildAsset = assetRepository.findById(childAsset.id()).orElse(null);
    assertNotNull(updatedChildAsset);
    assertEquals(AssetLifecycleState.INSTALLED, updatedChildAsset.lifecycleState());
    assertEquals(assetToBeInstalled.id(), updatedChildAsset.parentAssetId());
    assertEquals(rackInstance.id(), updatedChildAsset.rackInstanceId().toString());
    assertEquals(childAsset.version() + 1L, updatedChildAsset.version());
    Asset updatedAsset = assetRepository.findById(assetToBeInstalled.id()).orElse(null);
    assertNotNull(updatedAsset);
    assertEquals(AssetLifecycleState.INSTALLED, updatedAsset.lifecycleState());
    assertEquals(parentAsset.id(), updatedAsset.parentAssetId());
    assertEquals(rackInstance.id(), updatedAsset.rackInstanceId().toString());
    assertEquals(assetToBeInstalled.version() + 2L, updatedAsset.version());
  }

  @Test
  void testInstallAsset_AssetNotAvailable() {
    // Arrange
    var region = util.createRegion();
    var part = util.createPart("opn-1", null, RmcId.generate(RmcIdType.Catalog.PART_CATEGORY));
    Asset assetToBeInstalled =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.IN_TRANSIT)
                .locationId(util.createAssetLocation(region.id()).id())
                .build());
    Asset parentAsset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.RECEIVED)
                .build());

    // Act & Assert
    var ex =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.installAsset(
                    assetToBeInstalled.regionId(),
                    assetToBeInstalled.id(),
                    parentAsset.id(),
                    assetToBeInstalled.elevation(),
                    assetToBeInstalled.version(),
                    parentAsset.version(),
                    "AssetRepository-Test"));
    var sqlEx = (SQLException) ex.getCause();
    assertEquals(20001, sqlEx.getErrorCode());
    assertTrue(
        sqlEx
            .getMessage()
            .contains(
                "Asset must be a SPARE asset to be installed (State should be available and should"
                    + " be in a valid location type)"));
  }

  @Test
  void testInstallAsset_LocationIdNull() {
    // Arrange
    var region = util.createRegion();
    var part = util.createPart("opn-1", null, RmcId.generate(RmcIdType.Catalog.PART_CATEGORY));
    Asset assetToBeInstalled =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .build());
    Asset parentAsset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.RECEIVED)
                .build());

    // Act & Assert
    var ex =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.installAsset(
                    assetToBeInstalled.regionId(),
                    assetToBeInstalled.id(),
                    parentAsset.id(),
                    assetToBeInstalled.elevation(),
                    assetToBeInstalled.version(),
                    parentAsset.version(),
                    "AssetRepository-Test"));
    var sqlEx = (SQLException) ex.getCause();
    assertEquals(20001, sqlEx.getErrorCode());
    assertTrue(sqlEx.getMessage().contains("No asset location found"));
  }

  @Test
  void testInstallAsset_ParentAssetInvalidState() {
    // Arrange
    var region = util.createRegion();
    var part = util.createPart("opn-1", null, RmcId.generate(RmcIdType.Catalog.PART_CATEGORY));
    Asset assetToBeInstalled =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .locationId(util.createAssetLocation(region.id()).id())
                .build());
    Asset parentAsset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.FAILED)
                .build());

    // Act & Assert
    var ex =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.installAsset(
                    assetToBeInstalled.regionId(),
                    assetToBeInstalled.id(),
                    parentAsset.id(),
                    assetToBeInstalled.elevation(),
                    assetToBeInstalled.version(),
                    parentAsset.version(),
                    "AssetRepository-Test"));
    var sqlEx = (SQLException) ex.getCause();
    assertEquals(20001, sqlEx.getErrorCode());
    assertTrue(
        sqlEx
            .getMessage()
            .contains(
                "Parent asset must be in IN-TRANSIT, RECEIVED, AVAILABLE, INSTALLED or PENDING"
                    + " VALIDATION state to install asset"));
  }

  @Test
  void testInstallAsset_ParentAssetIsInTransit() {
    // Arrange
    var region = util.createRegion();
    var part = util.createPart("opn-1", null, RmcId.generate(RmcIdType.Catalog.PART_CATEGORY));
    Asset assetToBeInstalled =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .locationId(util.createAssetLocation(region.id()).id())
                .build());
    Asset parentAsset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.IN_TRANSIT)
                .build());

    assetRepository.installAsset(
        assetToBeInstalled.regionId(),
        assetToBeInstalled.id(),
        parentAsset.id(),
        assetToBeInstalled.elevation(),
        assetToBeInstalled.version(),
        parentAsset.version(),
        "AssetRepository-Test");

    Optional<Asset> updatedAsset = assetRepository.findById(assetToBeInstalled.id());
    assertTrue(updatedAsset.isPresent());
    Asset updatedAssetObj = updatedAsset.get();
    assertEquals(updatedAssetObj.lifecycleState(), AssetLifecycleState.INSTALLED);
  }

  @Test
  void testInstallAsset_AssetNotFound() {
    // Arrange
    var region = util.createRegion();
    var part = util.createPart("opn-1", null, RmcId.generate(RmcIdType.Catalog.PART_CATEGORY));
    Asset assetToBeInstalled =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .locationId(util.createAssetLocation(region.id()).id())
                .build());
    Asset parentAsset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.RECEIVED)
                .build());

    // Act & Assert
    var ex =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.installAsset(
                    assetToBeInstalled.regionId(),
                    RmcId.generate(RmcIdType.Inventory.ASSET),
                    parentAsset.id(),
                    assetToBeInstalled.elevation(),
                    assetToBeInstalled.version(),
                    parentAsset.version(),
                    "AssetRepository-Test"));
    var sqlEx = (SQLException) ex.getCause();
    assertEquals(20001, sqlEx.getErrorCode());
    assertTrue(sqlEx.getMessage().contains("Asset not found"));
  }

  @Test
  void testInstallAsset_ParentAssetNotFound() {
    // Arrange
    var region = util.createRegion();
    var part = util.createPart("opn-1", null, RmcId.generate(RmcIdType.Catalog.PART_CATEGORY));
    Asset assetToBeInstalled =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .locationId(util.createAssetLocation(region.id()).id())
                .build());
    Asset parentAsset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .lifecycleState(AssetLifecycleState.RECEIVED)
                .build());

    // Act & Assert
    var ex =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.installAsset(
                    assetToBeInstalled.regionId(),
                    assetToBeInstalled.id(),
                    RmcId.generate(RmcIdType.Inventory.ASSET),
                    assetToBeInstalled.elevation(),
                    assetToBeInstalled.version(),
                    parentAsset.version(),
                    "AssetRepository-Test"));
    var sqlEx = (SQLException) ex.getCause();
    assertEquals(20001, sqlEx.getErrorCode());
    assertTrue(sqlEx.getMessage().contains("Parent asset not found"));
  }

  @Test
  void testRemoveAssetHappyPath() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    RegionEntity region = util.createRegion();
    AssetLocation location = util.createAssetLocation(region.id());
    AssetLocation newLocation = util.createAssetLocation(region.id());

    final Asset parentAsset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .locationId(location.id())
                .serialNumber("123")
                .build());
    final Asset childAsset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .locationId(location.id())
                .serialNumber("123")
                .parentAssetId(parentAsset.id())
                .build());
    final Asset grandchildAsset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .locationId(location.id())
                .serialNumber("456")
                .parentAssetId(childAsset.id())
                .lifecycleState(AssetLifecycleState.INSTALLED)
                .build());

    assetRepository.removeAsset(
        region.id(),
        childAsset.id(),
        parentAsset.id(),
        newLocation.id(),
        AssetLifecycleState.AVAILABLE.toString(),
        childAsset.version(),
        uniqueAuditCreatedById);

    Asset fetchedAsset = repository.findById(childAsset.id()).get();
    assertEquals(AssetLifecycleState.AVAILABLE, fetchedAsset.lifecycleState());
    assertEquals(newLocation.id(), fetchedAsset.locationId());
    assertNull(fetchedAsset.parentAssetId());

    Asset fetchedGrandchildAsset = repository.findById(grandchildAsset.id()).get();
    assertEquals(AssetLifecycleState.INSTALLED, fetchedGrandchildAsset.lifecycleState());
    assertEquals(newLocation.id(), fetchedGrandchildAsset.locationId());
    assertEquals(fetchedAsset.id(), fetchedGrandchildAsset.parentAssetId());

    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.REMOVE_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.SUCCESS, auditEvents.get(0).eventResponse().eventStatus());
  }

  @Test
  void testRemoveAssetInvalidLifecycleState() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    RegionEntity region = util.createRegion();
    AssetLocation location = util.createAssetLocation(region.id());
    AssetLocation newLocation = util.createAssetLocation(region.id());

    final Asset parentAsset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .locationId(location.id())
                .serialNumber("123")
                .build());
    final Asset childAsset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .locationId(location.id())
                .serialNumber("123")
                .parentAssetId(parentAsset.id())
                .build());

    // try removing an asset and putting in INSTALLED state
    var createException =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.removeAsset(
                    region.id(),
                    childAsset.id(),
                    parentAsset.id(),
                    newLocation.id(),
                    AssetLifecycleState.INSTALLED.toString(),
                    childAsset.version(),
                    uniqueAuditCreatedById));
    var sqlEx = (SQLException) createException.getCause();
    assertEquals(20001, sqlEx.getErrorCode());
    assertTrue(
        sqlEx
            .getMessage()
            .contains("Lifecycle state cannot transitioned to INSTALLED when removing an asset."));

    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.REMOVE_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.FAILED, auditEvents.get(0).eventResponse().eventStatus());
  }

  @Test
  void testRemoveAssetNoParentAsset() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    RegionEntity region = util.createRegion();
    AssetLocation location = util.createAssetLocation(region.id());
    AssetLocation newLocation = util.createAssetLocation(region.id());

    final Asset asset =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region.id())
                .locationId(location.id())
                .serialNumber("123")
                .build());

    // remove an asset without a valid parent asset
    var createException =
        assertThrows(
            DataAccessException.class,
            () ->
                assetRepository.removeAsset(
                    region.id(),
                    asset.id(),
                    RmcId.generate(RmcIdType.Inventory.ASSET),
                    newLocation.id(),
                    AssetLifecycleState.IN_TRANSIT.toString(),
                    asset.version(),
                    uniqueAuditCreatedById));
    var sqlEx = (SQLException) createException.getCause();
    assertEquals(20001, sqlEx.getErrorCode());
    assertTrue(
        sqlEx.getMessage().contains("Asset not found for given id/regionId/parentAssetId/version"));

    List<AuditEvent> auditEvents =
        createEntityTestHelper.listAuditEventsByUpdatedByAndEventName(
            uniqueAuditCreatedById, InventoryAuditEventConstants.REMOVE_ASSET);
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.FAILED, auditEvents.get(0).eventResponse().eventStatus());
  }

  @Test
  void testMoveInstalledAssetToAvailable() {
    // Arrange
    var region = createEntityTestHelper.createRegionEntity();
    String airportCode = region.airportCode();
    String regionId = regionService.getRegionByAirportCodeOrFail(airportCode).id();
    final String oraclePartNumber = "7338187";
    final String manufacturerPartNumber = "35096952";
    var part = util.createCatalogPart(oraclePartNumber, manufacturerPartNumber);

    // Create Asset
    Optional<Asset> createdAsset =
        createAsset(
            regionId,
            "**********",
            oraclePartNumber,
            manufacturerPartNumber,
            null,
            null,
            "oracle-tag-xyz",
            "E2:7F:1C:A3:B8:D5",
            TEST_USER);
    assertTrue(createdAsset.isPresent());
    Asset retrieved = createdAsset.get();
    assertEquals(AssetLifecycleState.AVAILABLE, retrieved.lifecycleState());
    AssetReservation reservation = util.createAssetReservation(region.id(), part.id());
    AssetLocation holdAssetLocation = util.createAssetLocation(region.id(), AssetLocationType.HOLD);

    // Acquire
    assetRepository.acquireAsset(
        retrieved.regionId(),
        retrieved.id(),
        retrieved.version(),
        reservation.id(),
        null,
        TEST_USER,
        retrieved.subInventory().toString());

    @Nullable RmcId rackInstanceId = RmcId.generate(RmcIdType.DCMS.DEVICE_INSTANCE);
    @Nullable String lifecycleState = AssetLifecycleState.INSTALLED.toString();
    assetRepository.moveAssetToRack(
        retrieved.regionId(),
        retrieved.id(),
        retrieved.version() + 1,
        rackInstanceId,
        "elevation",
        lifecycleState,
        TEST_USER);

    // Act
    assetRepository.moveAssetToLocation(
        retrieved.regionId(),
        retrieved.id(),
        retrieved.version() + 2,
        holdAssetLocation.id(),
        AssetLifecycleState.AVAILABLE.toString(),
        TEST_USER,
        retrieved.subInventory().toString());

    Asset updated = repository.findById(retrieved.id()).get();
    assertEquals(AssetLifecycleState.AVAILABLE, updated.lifecycleState());
    assertEquals(retrieved.version() + 3, updated.version());
    assertNull(updated.rackInstanceId());
    assertNotNull(updated.locationId());
  }

  @Test
  void testAssetWithLocationInDifferentRegion() {
    final String uniqueAuditCreatedById = UUID.randomUUID().toString();
    var region = createEntityTestHelper.createRegionEntity();
    var region2 = createEntityTestHelper.createAdditionalRegionEntity();
    final String oraclePartNumber = "7338187";
    final String manufacturerPartNumber = "35096952";
    util.createCatalogPart(oraclePartNumber, manufacturerPartNumber);
    AssetLocation location = util.createAssetLocation(region2.id());

    var createException =
        assertThrows(
            DataAccessException.class,
            () ->
                createAsset(
                    region.id(),
                    "**********",
                    oraclePartNumber,
                    manufacturerPartNumber,
                    location.id(),
                    null,
                    "oracle-tag-xyz",
                    "E2:7F:1C:A3:B8:D5",
                    uniqueAuditCreatedById));

    var sqlEx = (SQLException) createException.getCause();
    assertEquals(20001, sqlEx.getErrorCode());
    assertTrue(sqlEx.getMessage().contains("Asset location not found in the provided region"));
  }

  @Test
  void testAssetUniqueIndex() {
    RegionEntity region1 = createEntityTestHelper.createRegionEntity();
    String serial1 = "serial1";
    String opn1 = "opn1";
    String mpn1 = "mpn1";
    Part part1 = util.createCatalogPart(opn1, mpn1);
    Asset asset1 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region1.id())
                .catalogPartId(part1.id())
                .serialNumber(serial1)
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .build());
    assertNotNull(asset1);

    // Should fail when we try to create another asset in a valid state
    var ex =
        assertThrows(
            DataAccessException.class,
            () ->
                util.createAsset(
                    AssetCreationDetailsBuilder.builder()
                        .regionId(region1.id())
                        .catalogPartId(part1.id())
                        .serialNumber(serial1)
                        .lifecycleState(AssetLifecycleState.INSTALLED)
                        .build()));
    assertTrue(
        ex.getMessage()
            .contains(
                "unique constraint (ADMIN.UNIQ_ASSET_SERIAL_PART_ID) violated on table"
                    + " ADMIN.DCMS_ASSET"));

    // Should succeed for same regionId, serial, partId when lifecycle state is DELETED
    Asset asset2 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region1.id())
                .catalogPartId(part1.id())
                .serialNumber(serial1)
                .lifecycleState(AssetLifecycleState.DELETED)
                .build());
    assertNotNull(asset2);

    // Should succeed with same serial and different partId
    String opn2 = "opn2";
    String mpn2 = "mpn2";
    Part part2 = util.createCatalogPart(opn2, mpn2);
    Asset asset3 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region1.id())
                .catalogPartId(part2.id())
                .serialNumber(serial1)
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .build());
    assertNotNull(asset3);

    // Should succeed with same partId but different serial
    String serial2 = "serial2";
    Asset asset4 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region1.id())
                .catalogPartId(part1.id())
                .serialNumber(serial2)
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .build());
    assertNotNull(asset4);

    // Should succeed with same partId, serial but different region
    RegionEntity region2 = createEntityTestHelper.createAdditionalRegionEntity();
    Asset asset5 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region2.id())
                .catalogPartId(part1.id())
                .serialNumber(serial1)
                .lifecycleState(AssetLifecycleState.AVAILABLE)
                .build());
    assertNotNull(asset5);

    // Should succeed for duplicate NOT_SERIALIZED static assets
    String nonSerial = "NOT_SERIALIZED";
    Asset nonSrl1 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region1.id())
                .catalogPartId(part1.id())
                .serialNumber(nonSerial)
                .lifecycleState(AssetLifecycleState.INSTALLED)
                .build());
    assertNotNull(nonSrl1);

    Asset nonSrl2 =
        util.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(region1.id())
                .catalogPartId(part1.id())
                .serialNumber(nonSerial)
                .lifecycleState(AssetLifecycleState.INSTALLED)
                .build());
    assertNotNull(nonSrl2);
  }

  /**
   * Test repository for Asset that only contains helper methods. Any sprocs that act on Asset
   * entity should be a part of AssetRepository/AssetRepositoryOracle
   */
  interface TestAssetRepository extends CrudRepository<Asset, RmcId> {

    @H2Repository
    interface TestAssetRepositoryH2 extends TestAssetRepository {}

    @OracleRepository(dataSource = DataSourceConstants.DEFAULT)
    interface TestAssetRepositoryOracle extends TestAssetRepository {}

    @Query("SELECT dcms_v24_1.is_asset_state_transition_valid(:curState, :toState) FROM DUAL")
    int isAssetStateTransitionValid(String curState, String toState);

    @Transactional
    @Query("UPDATE dcms_asset SET lifecycle_state = :lifecycle WHERE id = :assetId")
    void updateAssetLifecycle(RmcId assetId, String lifecycle);
  }

  private Asset createDummyAssetObject() {
    return new Asset(
        RmcId.generate(RmcIdType.Inventory.ASSET),
        "xyz",
        "XYZ",
        RmcId.generate(RmcIdType.Catalog.PART),
        null,
        null,
        null,
        null,
        null,
        null,
        "TEST-COMPARTMENT-OCID",
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        AssetLifecycleState.AVAILABLE,
        AssetSubInventory.GENERAL_SPARES,
        null);
  }

  private void assertLatestAuditEvent(EventStatus status, String eventName) {
    assertEquals(
        status,
        auditEventRepository
            .findTop1ByEventNameOrderByEventTimeDesc(eventName)
            .get()
            .eventResponse()
            .eventStatus());
  }
}
