package com.oracle.oci.rmc.dcms.impl.dal.inventory;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.oracle.oci.rmc.auditevent.dal.AuditEvent;
import com.oracle.oci.rmc.auditevent.model.AuditEventType;
import com.oracle.oci.rmc.auditevent.model.EventStatus;
import com.oracle.oci.rmc.auditevent.service.AuditEventService;
import com.oracle.oci.rmc.auditevent.util.InventoryAuditEventConstants;
import com.oracle.oci.rmc.catalog.api.model.entity.Part;
import com.oracle.oci.rmc.dcms.api.model.entity.AssetLocation;
import com.oracle.oci.rmc.dcms.api.model.entity.AssetLocationType;
import com.oracle.oci.rmc.dcms.api.model.entity.RegionEntity;
import com.oracle.oci.rmc.dcms.api.model.entity.StorageRoom;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetCreationDetailsBuilder;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetLifecycleState;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetLocationCreationDetailsBuilder;
import com.oracle.oci.rmc.dcms.impl.dal.inventory.assetlocation.AssetLocationRepository;
import com.oracle.oci.rmc.dcms.impl.dal.testutil.DcmsRepositoryTestUtil;
import com.oracle.oci.rmc.model.RmcId;
import com.oracle.oci.sfw.micronaut.http.pagination.PageQuery;
import io.micronaut.context.annotation.Requires;
import io.micronaut.data.exceptions.DataAccessException;
import io.micronaut.data.jdbc.runtime.JdbcOperations;
import io.micronaut.test.annotation.TransactionMode;
import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

@Requires(property = "database.type", value = "ORACLE")
@MicronautTest(transactionMode = TransactionMode.SINGLE_TRANSACTION)
public class AssetLocationRepositoryTest {

  private final DcmsRepositoryTestUtil repoUtil;
  private final AssetLocationRepository assetLocationRepository;
  private final JdbcOperations jdbcOperations;
  private final AuditEventService auditEventService;

  public AssetLocationRepositoryTest(
      DcmsRepositoryTestUtil repoUtil,
      AssetLocationRepository assetLocationRepository,
      JdbcOperations jdbcOperations,
      AuditEventService auditEventService) {
    this.repoUtil = repoUtil;
    this.assetLocationRepository = assetLocationRepository;
    this.jdbcOperations = jdbcOperations;
    this.auditEventService = auditEventService;
  }

  @BeforeEach
  void setUp() {
    repoUtil.createRackParentEntities();
  }

  @Test
  void testCreateAndList() {

    // Asset is large so that it simply exist in a data hall.
    // P1.PALLET
    Optional<AssetLocation> assetLocation1 =
        createAssetLocation(
            repoUtil.getRegionEntity().id(),
            "Asset Location 1",
            AssetLocationType.PALLET.toString(),
            "P1",
            null,
            null,
            null,
            null,
            repoUtil.getBuildingEntity().id().toString(),
            repoUtil.getDataHall().id().toString(),
            null);
    assertTrue(assetLocation1.isPresent());
    AssetLocation al1 = assetLocation1.get();
    assertEquals("P1.PALLET", al1.canonicalName());
    assertEquals(repoUtil.getRegionEntity().id(), al1.region());
    assertEquals(AssetLocationType.PALLET, al1.type());
    assertEquals("P1", al1.aisle());
    assertNull(al1.storageColumn());
    assertNull(al1.shelf());
    assertNull(al1.bin());
    assertNull(al1.storageRoomId());
    assertEquals(repoUtil.getDataHall().id(), al1.dataHallId());
    assertEquals(repoUtil.getBuildingEntity().id(), al1.buildingId());
    assertEquals(1, al1.active());
    assertNotNull(al1.timeCreated());
    assertNotNull(al1.timeUpdated());

    Part part = repoUtil.createCatalogPart("mock Oracle Part", "mock manufacture part");

    // Asset resides in aile,column,shelf and bin inside a storage room
    StorageRoom storageRoom =
        repoUtil.createStorageRoom(
            repoUtil.getRegionEntity(), repoUtil.getBuildingEntity(), repoUtil.getDataHall(), "3");

    // SITE_NAME.ROOM_NAME.STORAGE_TYPE.{AISLE}{COLUMN}.{SHELF}{BIN}
    Optional<AssetLocation> assetLocation2 =
        createAssetLocation(
            repoUtil.getRegionEntity().id(),
            "Asset Location 2",
            AssetLocationType.EXCS.toString(),
            "A",
            "01",
            "B",
            "02",
            storageRoom.id().toString(),
            repoUtil.getBuildingEntity().id().toString(),
            repoUtil.getDataHall().id().toString(),
            part.id().toString());
    assertTrue(assetLocation2.isPresent());
    AssetLocation al2 = assetLocation2.get();
    assertEquals(
        repoUtil.getBuildingEntity().name()
            + "."
            + repoUtil.getDataHall().name()
            + "."
            + storageRoom.canonicalName()
            + "."
            + al2.type().toString()
            + "."
            + "A01.B02",
        al2.canonicalName());

    // Asset can be large enough so that it simply resides in a shelf, bin can be left blank
    // SITE_NAME.ROOM_NAME.STORAGE_TYPE.{AISLE}{COLUMN}.{SHELF}
    Optional<AssetLocation> assetLocation3 =
        createAssetLocation(
            repoUtil.getRegionEntity().id(),
            "Asset Location 3",
            AssetLocationType.SPARE.toString(),
            "A",
            "01",
            "B",
            null,
            storageRoom.id().toString(),
            repoUtil.getBuildingEntity().id().toString(),
            repoUtil.getDataHall().id().toString(),
            null);

    assertTrue(assetLocation3.isPresent());
    AssetLocation al3 = assetLocation3.get();
    assertEquals(
        repoUtil.getBuildingEntity().name()
            + "."
            + repoUtil.getDataHall().name()
            + "."
            + storageRoom.canonicalName()
            + "."
            + al3.type().toString()
            + "."
            + "A01.B",
        al3.canonicalName());

    // Asset resides inside a storage room in a pallet
    // SITE_NAME.ROOM_NAME.AISLE.PALLET
    Optional<AssetLocation> assetLocation4 =
        createAssetLocation(
            repoUtil.getRegionEntity().id(),
            "Asset Location 4",
            AssetLocationType.PALLET.toString(),
            "P9",
            null,
            null,
            null,
            storageRoom.id().toString(),
            repoUtil.getBuildingEntity().id().toString(),
            repoUtil.getDataHall().id().toString(),
            null);

    assertTrue(assetLocation4.isPresent());
    AssetLocation al4 = assetLocation4.get();
    assertEquals(
        repoUtil.getBuildingEntity().name()
            + "."
            + storageRoom.canonicalName()
            + "."
            + al4.aisle()
            + "."
            + "PALLET",
        al4.canonicalName());

    // Test list

    // Filter by ID
    var assetLocations =
        assetLocationRepository.listV3(
            al2.region(),
            al4.id().toString(),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            PageQuery.fromEmptyToken(10));

    assertThat(assetLocations.getResults()).containsExactly(al4);

    // Fetch all (No filters)

    var allAssetLocations =
        assetLocationRepository.listV3(
            al2.region(),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            PageQuery.fromEmptyToken(10));

    var expected = Arrays.asList(al1, al2, al3, al4);
    assertEquals(expected, allAssetLocations.getResults());

    // Filter by building
    var assetLocationByBuilding =
        assetLocationRepository.listV3(
            al2.region(),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            repoUtil.getBuildingEntity().id(),
            null,
            PageQuery.fromEmptyToken(10));

    assertEquals(expected, assetLocationByBuilding.getResults());
  }

  @Test
  void testCreate_LoadingDock() {
    // Asset is large so that it simply exist in a data hall.
    // L1.LOADING_DOCK
    Optional<AssetLocation> assetLocation1 =
        createAssetLocation(
            repoUtil.getRegionEntity().id(),
            "Asset Location 1",
            AssetLocationType.LOADING_DOCK.toString(),
            "L1",
            null,
            null,
            null,
            null,
            repoUtil.getBuildingEntity().id().toString(),
            repoUtil.getDataHall().id().toString(),
            null);
    assertTrue(assetLocation1.isPresent());
    AssetLocation al1 = assetLocation1.get();
    assertAssetLocationProperties(
        al1, repoUtil.getDataHall().name() + ".LOADING_DOCK", "L1", AssetLocationType.LOADING_DOCK);
  }

  @Test
  void testCreate_Donor() {
    // Asset is large so that it simply exist in a data hall.
    // L1.DONOR
    String serialNumber = "AK006MANGN";
    Optional<AssetLocation> assetLocation1 =
        createAssetLocation(
            repoUtil.getRegionEntity().id(),
            "Rack at position 0152 in Data Hall A - ABL15.1",
            AssetLocationType.DONOR.toString(),
            serialNumber,
            null,
            null,
            null,
            null,
            repoUtil.getBuildingEntity().id().toString(),
            repoUtil.getDataHall().id().toString(),
            null);
    assertTrue(assetLocation1.isPresent());
    AssetLocation al1 = assetLocation1.get();
    assertAssetLocationProperties(
        al1,
        repoUtil.getDataHall().name() + ".DONOR." + serialNumber,
        serialNumber,
        AssetLocationType.DONOR);
  }

  @Test
  void testCreate_Pallet_withRoomIsNull() {
    // A1.PALLET
    String aisle = "A1";
    Optional<AssetLocation> assetLocation1 =
        createAssetLocation(
            repoUtil.getRegionEntity().id(),
            "pallet location 1",
            AssetLocationType.PALLET.toString(),
            aisle,
            null,
            null,
            null,
            null,
            repoUtil.getBuildingEntity().id().toString(),
            repoUtil.getDataHall().id().toString(),
            null);
    assertTrue(assetLocation1.isPresent());
    AssetLocation al1 = assetLocation1.get();
    assertAssetLocationProperties(
        al1, aisle + "." + AssetLocationType.PALLET, aisle, AssetLocationType.PALLET);
  }

  @Test
  void testCreate_Pallet_withRoomAndColumn() {
    // Ex: Building 8tZMm4f.SR1.PALLET.A01.F02
    var storageRoom =
        repoUtil.createStorageRoom(
            repoUtil.getRegionEntity(),
            repoUtil.getBuildingEntity(),
            repoUtil.getDataHall(),
            "SR1");
    String aisle = "A";
    Optional<AssetLocation> assetLocation1 =
        createAssetLocation(
            repoUtil.getRegionEntity().id(),
            "Pallet location 2",
            AssetLocationType.PALLET.toString(),
            aisle,
            "01",
            "F",
            "2",
            storageRoom.id().toString(),
            repoUtil.getBuildingEntity().id().toString(),
            repoUtil.getDataHall().id().toString(),
            null);
    assertTrue(assetLocation1.isPresent());
    AssetLocation al1 = assetLocation1.get();
    assertAssetLocationProperties(
        al1,
        repoUtil.getBuildingEntity().name()
            + "."
            + repoUtil.getDataHall().name()
            + "."
            + storageRoom.canonicalName()
            + "."
            + AssetLocationType.PALLET
            + "."
            + aisle
            + "01.F02",
        aisle,
        AssetLocationType.PALLET);
  }

  @Test
  void testCreate_Pallet_withRoomAndColumnIsNull() {
    // Ex: Building 8tZMm4f.SR1.PALLET.A01.F02
    var storageRoom =
        repoUtil.createStorageRoom(
            repoUtil.getRegionEntity(),
            repoUtil.getBuildingEntity(),
            repoUtil.getDataHall(),
            "SR1");
    String aisle = "A";
    Optional<AssetLocation> assetLocation1 =
        createAssetLocation(
            repoUtil.getRegionEntity().id(),
            "Pallet location 2",
            AssetLocationType.PALLET.toString(),
            aisle,
            null,
            null,
            null,
            storageRoom.id().toString(),
            repoUtil.getBuildingEntity().id().toString(),
            repoUtil.getDataHall().id().toString(),
            null);
    assertTrue(assetLocation1.isPresent());
    AssetLocation al1 = assetLocation1.get();
    assertAssetLocationProperties(
        al1,
        repoUtil.getBuildingEntity().name()
            + "."
            + storageRoom.canonicalName()
            + "."
            + aisle
            + "."
            + AssetLocationType.PALLET,
        aisle,
        AssetLocationType.PALLET);
  }

  @Test
  void testUpdateAssetLocation() {
    StorageRoom storageRoom =
        repoUtil.createStorageRoom(
            repoUtil.getRegionEntity(), repoUtil.getBuildingEntity(), repoUtil.getDataHall(), "3");
    Optional<AssetLocation> assetLocation =
        createAssetLocation(
            repoUtil.getRegionEntity().id(),
            "Asset Location 2",
            AssetLocationType.EXCS.toString(),
            "A",
            "01",
            "B",
            "02",
            storageRoom.id().toString(),
            repoUtil.getBuildingEntity().id().toString(),
            repoUtil.getDataHall().id().toString(),
            null);
    assertTrue(assetLocation.isPresent());
    AssetLocation al = assetLocation.get();

    // Updated active state for id from 1 -> 0
    RmcId id = al.id();
    jdbcOperations.prepareStatement(
        "BEGIN DCMS_V41.dcms_update_asset_location_active_state(?, ?, ?, ?); END;",
        statement -> {
          statement.setString(1, id.toString());
          statement.setInt(2, 0);
          statement.setString(3, repoUtil.getRegionEntity().id());
          statement.setInt(4, 0);
          statement.executeUpdate();
          return null;
        });

    assertTrue(assetLocationRepository.findById(id).isPresent());
    assertEquals(0, assetLocationRepository.findById(id).get().active());

    // Update with invalid version
    var updateException1 =
        assertThrows(
            DataAccessException.class,
            () ->
                jdbcOperations.prepareStatement(
                    "BEGIN DCMS_V18.dcms_update_asset_location_active_state(?, ?, ?, ?); END;",
                    statement -> {
                      statement.setString(1, id.toString());
                      statement.setInt(2, 1);
                      statement.setString(3, repoUtil.getRegionEntity().id());
                      statement.setInt(4, 0);
                      statement.executeUpdate();
                      return null;
                    }));
    var sqlEx1 = (SQLException) updateException1.getCause();
    assertEquals(20001, sqlEx1.getErrorCode());
    assertTrue(sqlEx1.getMessage().contains("Record not found for update."));

    // Update with invalid region

    var updateException2 =
        assertThrows(
            DataAccessException.class,
            () ->
                jdbcOperations.prepareStatement(
                    "BEGIN DCMS_V18.dcms_update_asset_location_active_state(?, ?, ?, ?); END;",
                    statement -> {
                      statement.setString(1, id.toString());
                      statement.setInt(2, 0);
                      statement.setString(3, "invalid-region");
                      statement.setInt(4, 0);
                      statement.executeUpdate();
                      return null;
                    }));
    var sqlEx2 = (SQLException) updateException2.getCause();
    assertEquals(20001, sqlEx2.getErrorCode());
    assertTrue(sqlEx2.getMessage().contains("Record not found for update."));
  }

  @Test
  void deleteAssetLocation() {

    // Positive tests
    StorageRoom storageRoom =
        repoUtil.createStorageRoom(
            repoUtil.getRegionEntity(), repoUtil.getBuildingEntity(), repoUtil.getDataHall(), "3");
    Optional<AssetLocation> assetLocation =
        createAssetLocation(
            repoUtil.getRegionEntity().id(),
            "Asset Location",
            AssetLocationType.EXCS.toString(),
            "A",
            "01",
            "B",
            "02",
            storageRoom.id().toString(),
            repoUtil.getBuildingEntity().id().toString(),
            repoUtil.getDataHall().id().toString(),
            null);
    assertTrue(assetLocation.isPresent());
    AssetLocation al = assetLocation.get();
    RmcId id = al.id();
    jdbcOperations.prepareStatement(
        "BEGIN DCMS_V41.dcms_delete_asset_location(?,?,?); END;",
        statement -> {
          statement.setString(1, id.toString());
          statement.setInt(2, 0);
          statement.setString(3, repoUtil.getRegionEntity().id());
          statement.executeUpdate();
          return null;
        });
    assertFalse(assetLocationRepository.findById(id).isPresent());

    // Location does not exist/ Invalid location

    AssetLocation location = repoUtil.createAssetLocation(repoUtil.getRegionEntity().id());
    var deleteException1 =
        assertThrows(
            RuntimeException.class,
            () ->
                jdbcOperations.prepareStatement(
                    "BEGIN DCMS_V41.dcms_delete_asset_location(?,?,?); END;",
                    statement -> {
                      statement.setString(1, "Invalid-location-id");
                      statement.setInt(2, 0);
                      statement.setString(3, repoUtil.getRegionEntity().id());
                      statement.executeUpdate();
                      return null;
                    }));

    var sqlEx = (SQLException) deleteException1.getCause();
    assertTrue(sqlEx.getMessage().contains("Record not found for deletion."));

    // Region does not exists
    var deleteException2 =
        assertThrows(
            RuntimeException.class,
            () ->
                jdbcOperations.prepareStatement(
                    "BEGIN DCMS_V41.dcms_delete_asset_location(?,?,?); END;",
                    statement -> {
                      statement.setString(1, location.id().toString());
                      statement.setInt(2, 0);
                      statement.setString(3, "Invalid region");
                      statement.executeUpdate();
                      return null;
                    }));

    var sqlEx2 = (SQLException) deleteException2.getCause();
    assertTrue(sqlEx2.getMessage().contains("Record not found for deletion."));

    // Incorrect version
    var deleteException3 =
        assertThrows(
            RuntimeException.class,
            () ->
                jdbcOperations.prepareStatement(
                    "BEGIN DCMS_V41.dcms_delete_asset_location(?,?,?); END;",
                    statement -> {
                      statement.setString(1, location.id().toString());
                      statement.setInt(2, 1);
                      statement.setString(3, repoUtil.getRegionEntity().id());
                      statement.executeUpdate();
                      return null;
                    }));

    var sqlEx3 = (SQLException) deleteException3.getCause();
    assertTrue(sqlEx3.getMessage().contains("Record not found for deletion."));
  }

  @Test
  void testCrudlProcsBadParams() {
    StorageRoom storageRoom =
        repoUtil.createStorageRoom(
            repoUtil.getRegionEntity(), repoUtil.getBuildingEntity(), repoUtil.getDataHall(), "3");
    assertThrows(
        IllegalArgumentException.class,
        () ->
            createAssetLocation(
                null,
                "Asset Location",
                AssetLocationType.EXCS.toString(),
                "A",
                "01",
                "B",
                "02",
                storageRoom.id().toString(),
                repoUtil.getBuildingEntity().id().toString(),
                repoUtil.getDataHall().id().toString(),
                null));

    var updateException =
        assertThrows(
            DataAccessException.class,
            () ->
                jdbcOperations.prepareStatement(
                    "BEGIN DCMS_V18.dcms_update_asset_location_active_state(?, ?, ?, ?); END;",
                    statement -> {
                      statement.setString(1, "id2");
                      statement.setInt(2, 0);
                      statement.setString(3, repoUtil.getRegionEntity().id());
                      statement.setInt(4, 0);
                      statement.executeUpdate();
                      return null;
                    }));
    var sqlEx = (SQLException) updateException.getCause();
    assertEquals(20001, sqlEx.getErrorCode());
    assertTrue(sqlEx.getMessage().contains("Record not found for update."));

    var deleteException =
        assertThrows(
            DataAccessException.class,
            () ->
                jdbcOperations.prepareStatement(
                    "BEGIN DCMS_V18.dcms_delete_asset_location(?,?,?); END;",
                    statement -> {
                      statement.setString(1, "id2");
                      statement.setInt(2, 0);
                      statement.setString(3, repoUtil.getRegionEntity().id());
                      statement.executeUpdate();
                      return null;
                    }));
    sqlEx = (SQLException) deleteException.getCause();
    assertEquals(20001, sqlEx.getErrorCode());
    assertTrue(sqlEx.getMessage().contains("Record not found for deletion."));
  }

  Optional<AssetLocation> createAssetLocation(
      String regionId,
      String name,
      String type,
      String aisle,
      String storageColumn,
      String shelf,
      String bin,
      String storageRoomId,
      String buildingId,
      String dataHallId,
      String partId) {

    RmcId rmcId =
        assetLocationRepository
            .saveAssetLocation(
                AssetLocationCreationDetailsBuilder.builder()
                    .createdBy(null)
                    .buildingId(RmcId.fromString(buildingId))
                    .storageRoomId(storageRoomId == null ? null : RmcId.fromString(storageRoomId))
                    .bin(bin)
                    .aisle(aisle)
                    .column(storageColumn)
                    .shelf(shelf)
                    .dataHallId(dataHallId == null ? null : RmcId.fromString(dataHallId))
                    .isActive(true)
                    .name(name)
                    .partId(partId == null ? null : RmcId.fromString(partId))
                    .regionId(regionId)
                    .type(AssetLocationType.valueOf(type))
                    .createdBy("test-user")
                    .build())
            .id();
    return assetLocationRepository.findById(rmcId);
  }

  @Test
  void createAssetLocation_invalidType() {
    assertThrows(
        IllegalArgumentException.class,
        () ->
            createAssetLocation(
                repoUtil.getRegionEntity().id(),
                "AL: Invalid asset location type",
                "Invalid-Type",
                "P1",
                null,
                null,
                "02",
                null,
                repoUtil.getBuildingEntity().id().toString(),
                repoUtil.getDataHall().id().toString(),
                null));
  }

  @Test
  void createAssetLocation_duplicateAssetLocation() {

    StorageRoom storageRoom =
        repoUtil.createStorageRoom(
            repoUtil.getRegionEntity(), repoUtil.getBuildingEntity(), repoUtil.getDataHall(), "3");

    Optional<AssetLocation> assetLocation =
        createAssetLocation(
            repoUtil.getRegionEntity().id(),
            "Asset Location 1",
            AssetLocationType.SPARE.toString(),
            "A",
            "01",
            "B",
            "02",
            storageRoom.id().toString(),
            repoUtil.getBuildingEntity().id().toString(),
            repoUtil.getDataHall().id().toString(),
            null);
    assertTrue(assetLocation.isPresent());

    var createException =
        assertThrows(
            RuntimeException.class,
            () ->
                createAssetLocation(
                    repoUtil.getRegionEntity().id(),
                    "Asset Location 2",
                    AssetLocationType.SPARE.toString(),
                    "A",
                    "01",
                    "B",
                    "02",
                    storageRoom.id().toString(),
                    repoUtil.getBuildingEntity().id().toString(),
                    repoUtil.getDataHall().id().toString(),
                    null));
    var sqlEx = (SQLException) createException.getCause();
    // Unique constraint on canonical name violated
    assertEquals(1, sqlEx.getErrorCode());
  }

  @Test
  void deleteAssetLocation_havingExistingChildAssets() {

    RegionEntity region = repoUtil.getRegionEntity();
    AssetLocation location = repoUtil.createAssetLocation(region.id());
    repoUtil.createAsset(
        AssetCreationDetailsBuilder.builder()
            .regionId(region.id())
            .lifecycleState(AssetLifecycleState.AVAILABLE)
            .locationId(location.id())
            .build());

    var createException =
        assertThrows(
            RuntimeException.class,
            () ->
                jdbcOperations.prepareStatement(
                    "BEGIN DCMS_V41.dcms_delete_asset_location(?,?,?); END;",
                    statement -> {
                      statement.setString(1, location.id().toString());
                      statement.setInt(2, 0);
                      statement.setString(3, region.id());
                      statement.executeUpdate();
                      return null;
                    }));

    var sqlEx = (SQLException) createException.getCause();
    assertTrue(
        sqlEx
            .getMessage()
            .contains(
                "ORA-02292: integrity constraint (ADMIN.DCMS_FK_ASSET_ASSET_LOCATION) violated -"
                    + " child record found"));
  }

  @Test
  void updateStateToInactive_locationUsedByOtherAssets() {

    RegionEntity region = repoUtil.getRegionEntity();
    AssetLocation location = repoUtil.createAssetLocation(region.id());
    repoUtil.createAsset(
        AssetCreationDetailsBuilder.builder()
            .regionId(region.id())
            .lifecycleState(AssetLifecycleState.AVAILABLE)
            .locationId(location.id())
            .build());

    var createException =
        assertThrows(
            RuntimeException.class,
            () ->
                jdbcOperations.prepareStatement(
                    "BEGIN DCMS_V18.dcms_update_asset_location_active_state(?, ?, ?, ?); END;",
                    statement -> {
                      statement.setString(1, location.id().toString());
                      statement.setInt(2, 0);
                      statement.setString(3, repoUtil.getRegionEntity().id());
                      statement.setInt(4, 0);
                      statement.executeUpdate();
                      return null;
                    }));

    var sqlEx = (SQLException) createException.getCause();
    assertEquals(20001, sqlEx.getErrorCode());
    assertTrue(
        sqlEx
            .getMessage()
            .contains("Cannot set location inactive with child assets associated with location"));
  }

  @Test
  void testAssetLocationStoredProcedureAuditing() {
    String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // Asset is large so that it simply exist in a data hall.
    // P1.PALLET
    AssetLocation assetLocation1 =
        assetLocationRepository.saveAssetLocation(
            AssetLocationCreationDetailsBuilder.builder()
                .createdBy(null)
                .buildingId(repoUtil.getBuildingEntity().id())
                .aisle("P1")
                .dataHallId(repoUtil.getDataHall().id())
                .isActive(true)
                .name("Asset Location 1")
                .regionId(repoUtil.getRegionEntity().id())
                .type(AssetLocationType.PALLET)
                .createdBy(uniqueAuditCreatedById)
                .build());

    // Toggle active state to disabled
    assetLocationRepository.toggleAssetLocationActiveState(
        assetLocation1.id(),
        0,
        assetLocation1.region(),
        assetLocation1.version(),
        uniqueAuditCreatedById);

    var foundAssetLocation = assetLocationRepository.findById(assetLocation1.id());

    // Delete the asset location
    assetLocationRepository.deleteAssetLocation(
        assetLocation1.id(),
        foundAssetLocation.get().version(),
        assetLocation1.region(),
        uniqueAuditCreatedById);

    List<AuditEvent> auditEvents =
        auditEventService
            .listAuditEvents(null, null, null, uniqueAuditCreatedById, PageQuery.fromEmptyToken(10))
            .getResults();
    assertEquals(3, auditEvents.size(), "Expected 3 audit events from this test");

    var createEvent =
        auditEvents.stream()
            .filter(
                event ->
                    event.eventName().equals(InventoryAuditEventConstants.CREATE_ASSET_LOCATION)
                        && event.eventType().equals(AuditEventType.POST.name()))
            .findFirst();
    assertTrue(createEvent.isPresent());
    assertEquals(EventStatus.SUCCESS, createEvent.get().eventResponse().eventStatus());

    var updateEvent =
        auditEvents.stream()
            .filter(
                event ->
                    event.eventName().equals(InventoryAuditEventConstants.UPDATE_ASSET_LOCATION)
                        && event.eventType().equals(AuditEventType.PATCH.name()))
            .findFirst();
    assertTrue(updateEvent.isPresent());
    assertEquals(EventStatus.SUCCESS, updateEvent.get().eventResponse().eventStatus());

    var deleteEvent =
        auditEvents.stream()
            .filter(
                event ->
                    event.eventName().equals(InventoryAuditEventConstants.DELETE_ASSET_LOCATION)
                        && event.eventType().equals(AuditEventType.DELETE.name()))
            .findFirst();
    assertTrue(deleteEvent.isPresent());
    assertEquals(EventStatus.SUCCESS, deleteEvent.get().eventResponse().eventStatus());
  }

  @Test
  void testAssetLocationStoredProcedureAuditingFailures() {
    String uniqueAuditCreatedById = UUID.randomUUID().toString();
    // We should fail to create assetLocation if the aisle is non-alpha
    assertThrows(
        DataAccessException.class,
        () ->
            assetLocationRepository.saveAssetLocation(
                AssetLocationCreationDetailsBuilder.builder()
                    .createdBy(null)
                    .buildingId(repoUtil.getBuildingEntity().id())
                    .aisle("P1")
                    .dataHallId(repoUtil.getDataHall().id())
                    .isActive(true)
                    .aisle("@@##@#")
                    .name("Asset Location 1")
                    .regionId(repoUtil.getRegionEntity().id())
                    .type(AssetLocationType.SPARE)
                    .createdBy(uniqueAuditCreatedById)
                    .build()));

    // Actually create it
    var assetLocation1 =
        assetLocationRepository.saveAssetLocation(
            AssetLocationCreationDetailsBuilder.builder()
                .createdBy(null)
                .buildingId(repoUtil.getBuildingEntity().id())
                .aisle("P1")
                .dataHallId(repoUtil.getDataHall().id())
                .isActive(true)
                .aisle("A")
                .name("Asset Location 1")
                .regionId(repoUtil.getRegionEntity().id())
                .type(AssetLocationType.SPARE)
                .createdBy(uniqueAuditCreatedById)
                .build());

    // Toggle active state to disabled will fail if there is an asset associated with the location
    repoUtil.createAsset(
        AssetCreationDetailsBuilder.builder().locationId(assetLocation1.id()).build());
    assertThrows(
        DataAccessException.class,
        () ->
            assetLocationRepository.toggleAssetLocationActiveState(
                assetLocation1.id(),
                0,
                assetLocation1.region(),
                assetLocation1.version(),
                uniqueAuditCreatedById));

    // Delete should fail if the wrong version is supplied
    assertThrows(
        DataAccessException.class,
        () ->
            assetLocationRepository.deleteAssetLocation(
                assetLocation1.id(), -1L, assetLocation1.region(), uniqueAuditCreatedById));

    List<AuditEvent> auditEvents =
        auditEventService
            .listAuditEvents(null, null, null, uniqueAuditCreatedById, PageQuery.fromEmptyToken(10))
            .getResults();
    assertEquals(
        4, auditEvents.size(), "Expected 4 audit events from this test (1 success, 3 failures)");

    assertEquals(
        1,
        auditEvents.stream()
            .filter(event -> EventStatus.SUCCESS == event.eventResponse().eventStatus())
            .count(),
        "Expected only 1 successful event");
    assertEquals(
        3,
        auditEvents.stream()
            .filter(event -> EventStatus.FAILED == event.eventResponse().eventStatus())
            .count(),
        "Expected 3 failed events");

    var createEvent =
        auditEvents.stream()
            .filter(
                event ->
                    event.eventName().equals(InventoryAuditEventConstants.CREATE_ASSET_LOCATION)
                        && event.eventType().equals(AuditEventType.POST.name())
                        && EventStatus.FAILED == event.eventResponse().eventStatus())
            .findFirst();
    assertTrue(createEvent.isPresent());

    var updateEvent =
        auditEvents.stream()
            .filter(
                event ->
                    event.eventName().equals(InventoryAuditEventConstants.UPDATE_ASSET_LOCATION)
                        && event.eventType().equals(AuditEventType.PATCH.name()))
            .findFirst();
    assertTrue(updateEvent.isPresent());
    assertEquals(EventStatus.FAILED, updateEvent.get().eventResponse().eventStatus());

    var deleteEvent =
        auditEvents.stream()
            .filter(
                event ->
                    event.eventName().equals(InventoryAuditEventConstants.DELETE_ASSET_LOCATION)
                        && event.eventType().equals(AuditEventType.DELETE.name()))
            .findFirst();
    assertTrue(deleteEvent.isPresent());
    assertEquals(EventStatus.FAILED, deleteEvent.get().eventResponse().eventStatus());
  }

  @Test
  void createAssetLocation_duplicateAssetLocationInDiffDataHall() {

    StorageRoom storageRoom =
        repoUtil.createStorageRoom(
            repoUtil.getRegionEntity(), repoUtil.getBuildingEntity(), repoUtil.getDataHall(), "3");

    Optional<AssetLocation> assetLocation =
        createAssetLocation(
            repoUtil.getRegionEntity().id(),
            "Asset Location 1",
            AssetLocationType.SPARE.toString(),
            "A",
            "01",
            "B",
            "02",
            storageRoom.id().toString(),
            repoUtil.getBuildingEntity().id().toString(),
            repoUtil.getDataHall().id().toString(),
            null);
    assertTrue(assetLocation.isPresent());

    Optional<AssetLocation> assetLocation2 =
        createAssetLocation(
            repoUtil.getRegionEntity().id(),
            "Asset Location 2",
            AssetLocationType.SPARE.toString(),
            "A",
            "01",
            "B",
            "02",
            storageRoom.id().toString(),
            repoUtil.getBuildingEntity().id().toString(),
            repoUtil.getDataHall2().id().toString(),
            null);

    assertTrue(assetLocation2.isPresent());
  }

  private void assertAssetLocationProperties(
      AssetLocation al, String expectedCanonicalName, String aisle, AssetLocationType type) {
    assertEquals(expectedCanonicalName, al.canonicalName());
    assertEquals(repoUtil.getRegionEntity().id(), al.region());
    assertEquals(type, al.type());
    assertEquals(aisle, al.aisle());
    assertEquals(repoUtil.getDataHall().id(), al.dataHallId());
    assertEquals(repoUtil.getBuildingEntity().id(), al.buildingId());
    assertEquals(1, al.active());
    assertNotNull(al.timeCreated());
    assertNotNull(al.timeUpdated());
  }
}
