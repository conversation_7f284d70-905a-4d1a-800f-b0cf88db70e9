package com.oracle.oci.rmc.dcms.api.model.common;

import io.micronaut.core.annotation.NonNull;

@SuppressWarnings("PMD.ExcessivePublicCount")
public enum NodeGroupName {
  FLEAF_TS_GROUP("fleaf_ts"),
  BLEAF_TS_GROUP("bleaf_ts"),
  COMPUTE_TOR("compute_tor"),
  COMPUTE_UTILITY("compute_utility"),
  CX7060_MTOR("cx7060_mtor"),

  // fgpu logical node groups
  FGPU_POD_1("fgpu_pod_1"),
  FGPU_POD_2("fgpu_pod_2"),
  FGPU_POD_3("fgpu_pod_3"),
  FGPU_POD_4("fgpu_pod_4"),
  FLEAF("fleaf"),
  FLEAF_NAMING("fleaf_naming"),
  FLEAF_FLEAF_CLOCK("fleaf_fleaf_clock"),
  FLEAF_MTOR("fleaf_mtor"),
  FSPINE("fspine"),
  INTER_BUILDING_FSPINE_ABL15("inter_building_fspine_abl15"),
  INTER_BUILDING_FSPINE_ABL16("inter_building_fspine_abl16"),
  FTOR("ftor"),
  FSPINE_INTRARACK_PP("fspine-intrarack-pp"),

  INTRA_RACK_FLEAF("intra_rack_fleaf"),
  INTRA_RACK_MTOR("intra_rack_mtor"),
  INTRA_RACK_MCORE_CLOCK("intra_rack_mcore_clock"),
  INTRA_RACK_MCORE_MTOR("intra_rack_mcore_mtor"),
  CLIENT_FLEAF("client_fleaf"),
  CLIENT_FSPINE("client_fspine"),
  MCORE_MTOR("mcore_mtor"),
  MCORE_MTOR_1("mcore_mtor_1"),
  MCORE_MTOR_2("mcore_mtor_2"),
  MLEAF("mleaf"),
  MSPINE("mspine"),
  MTOR("mtor"),
  TS("ts"),
  MTOR_MTS("mtor_mts"),
  MTOR_TS("mtor_ts"),
  MTS("mts"),
  OOB("oob"),
  OCS_MTOR("ocs_mtor"),
  OCS_OCS("ocs_ocs"),
  TX7050_MTOR("tx7050_mtor"),
  FSPINE_TS_GROUP("fspine_ts"),
  BSPINE_TS_GROUP("bspine_ts"),
  MTOR_MTOR("mtor_mtor"),
  MCORE_MTS("mcore_mts"),
  FSPINE_MTOR_GROUP("fspine_mtor"),
  BSPINE_MTOR_GROUP("bspine_mtor"),
  MCORE_CLOCK("mcore_clock"),
  ALL_MTOR("all_mtor"),
  ALL_MLEAF("all_mleaf"),
  ALL_MSPINE("all_mspine"),

  // frontend
  FLEAF_GPU_OHR("fleaf_gpu_ohr"),
  FGPU_POD("fgpu_pod"),
  FLEAF_POD("fleaf_pod"),

  // TS5
  COMPUTE_FTOR_GROUP("compute_ftor"),
  COMPUTE_E5_BASE("compute_e5"),
  COMPUTE_E5_GROUP_1("compute_e5_1"),
  COMPUTE_E5_GROUP_2("compute_e5_2"),
  COMPUTE_MUTILITY_GROUP("compute_mutility"),
  FABRIC_MTOR_GROUP("fabric_mtor"),
  FABRIC_TS_GROUP("fabric_ts"),
  FGPU_TP5_GROUP("fgpu-tp5"),
  FLEAF_TP5_GROUP("fleaf-tp5"),
  FSPINE_TP5_GROUP("fspine-tp5"),
  FTOR_TP5_GROUP("ftor-tp5"),
  GPU_MUTILITY_GROUP("gpu_mutility"),
  MGMT_CLOCK_GROUP("mgmt_clock"),
  MGMT_MLEAF_GROUP("mgmt_mleaf"),
  MGMT_MSPINE_GROUP("mgmt_mspine"),
  MGMT_RU9_MTOR_GROUP("mgmt_ru9_mtor"),
  MGMT_RU41_MTOR_GROUP("mgmt_ru41_mtor"),
  MGMT_TS_GROUP("mgmt_ts"),
  TP5_GPU_MUTILITY_GROUP("tp5_gpu_mutility"),
  TP5_INTRA_RACK_BLEAF_GROUP("tp5_intra_rack_bleaf"),
  TP5_INTRA_RACK_BSPINE_GROUP("tp5_intra_rack_bspine"),
  TP5_INTRA_RACK_FLEAF_GROUP(
      "tp5_intra_rack_fleaf"), // Create new group since linkrule (port) is different from prod.
  TP5_INTRA_RACK_FSPINE_GROUP("tp5_intra_rack_fspine"),
  TP5_MTOR_GROUP("tp5_mtor"),
  TP5_OOB_GROUP("tp5_oob"),
  TP5_TS_GROUP("tp5_ts"),

  // backend
  BSPINE("bspine"),
  BLEAF("bleaf"),
  BGPU("bgpu"),
  BGPU_ROW_RED_BLEAF_PP("bgpu_row_red_bleaf_pp"), // 1 node group per GPU ROW
  BGPU_ROW_GREEN_BLEAF_PP("bgpu_row_green_bleaf_pp"), // 1 node group per GPU ROW
  BLEAF_MTOR("bleaf_mtor"),
  BLEAF_ROOM_1_SPINE_ROOM_1_SPINE_RACK_PPS("bleaf-room1-spine-room1-pps"),
  BLEAF_ROOM_2_SPINE_ROOM_1_SPINE_RACK_PPS("bleaf-room2-spine-room1-pps"),
  BLEAF_ROOM_3_SPINE_ROOM_1_SPINE_RACK_PPS("bleaf-room3-spine-room1-pps"),
  BLEAF_ROOM_4_SPINE_ROOM_1_SPINE_RACK_PPS("bleaf-room4-spine-room1-pps"),
  BLEAF_ROOM_1_SPINE_ROOM_2_SPINE_RACK_PPS("bleaf-room1-spine-room2-pps"),
  BLEAF_ROOM_2_SPINE_ROOM_2_SPINE_RACK_PPS("bleaf-room2-spine-room2-pps"),
  BLEAF_ROOM_3_SPINE_ROOM_2_SPINE_RACK_PPS("bleaf-room3-spine-room2-pps"),
  BLEAF_ROOM_4_SPINE_ROOM_2_SPINE_RACK_PPS("bleaf-room4-spine-room2-pps"),
  BSPINE_PP("bspine_pp"), // 1 nodegroup per BSPINE rack
  BLEAF_RACK("bleaf_rack"), // 1 nodegroup per BLEAF rack
  BLEAF_PP("bleaf_pp"), // 1 nodegroup per BLEAF rack
  // Passive
  COMPUTE_OHR1_FIBER_WITH_CASSETTES_PATCH_PANEL_GROUP(
      "compute_ohr1_fiber_with_cassette_panel"), // OHR0914A
  COMPUTE_OHR2_FIBER_WITH_CASSETTES_PATCH_PANEL_GROUP(
      "compute_ohr2_fiber_with_cassette_panel"), // OHR1014A
  COMPUTE_OHR3_FIBER_WITH_CASSETTES_PATCH_PANEL_GROUP(
      "compute_ohr3_fiber_with_cassette_panel"), // OHR0910A
  COMPUTE_OHR4_FIBER_WITH_CASSETTES_PATCH_PANEL_GROUP(
      "compute_ohr4_fiber_with_cassette_panel"), // OHR1010A
  CR_1023A("cr_row8_ohr3_1023a_patch_panel"),
  CR_914A("cr_row9_ohr1_914a_patch_panel"),
  CR_923A("cr_row9_ohr3_923a_patch_panel"),
  CR_1014A("cr_row8_ohr1_1014a_patch_panel"),
  CR_1003A("cr_row9_ohr4_1003a_patch_panel"),
  FIBER_RACKS1_PP_COMPUTE_OHR1_GROUP("fiber_racks1_pp_compute_ohr1"),
  FIBER_RACKS1_PP_COMPUTE_OHR2_GROUP("fiber_racks1_pp_compute_ohr2"),
  FIBER_RACKS2_PP_COMPUTE_OHR1_GROUP("fiber_racks2_pp_compute_ohr1"),
  FIBER_RACKS2_PP_COMPUTE_OHR2_GROUP("fiber_racks2_pp_compute_ohr2"),
  INTER_BUILDING_BSPINE_ABL15("inter_building_bspine_abl15"),
  INTER_BUILDING_BSPINE_ABL16("inter_building_bspine_abl16"),

  // Hub
  HUB_FLEAF("hub_fleaf"),
  HUB_FSPINE("hub_fspine"),

  /**
   * This node group contains cassettes of the <b>814</b> rack at elevation <b>9</b> which supposed
   * to be connected to <b>OHR923A</b> rack.
   */
  FIBER_RACKS1_PP_LEAF_OHR1_GROUP("fiber_racks1_pp_leaf_ohr1"),

  /**
   * This node group contains cassettes of the <b>814</b> rack at elevation <b>3</b> which supposed
   * to be connected to <b>OHR1020A, OHR1023A</b> rack
   */
  FIBER_RACKS1_PP_LEAF_OHR2_GROUP("fiber_racks1_pp_leaf_ohr2"),

  /**
   * This node group contains cassettes of the <b>818</b> rack at elevation <b>9</b> which supposed
   * to be connected to <b>OHR920A,OHR923A</b> rack.
   */
  FIBER_RACKS2_PP_LEAF_OHR1_GROUP("fiber_racks2_pp_leaf_ohr1"),

  /**
   * This node group contains cassettes of the <b>818</b> rack at elevation <b>3</b> which supposed
   * to be connected to <b>OHR1023A</b> rack.
   */
  FIBER_RACKS2_PP_LEAF_OHR2_GROUP("fiber_racks2_pp_leaf_ohr2"),
  FLEAFCOMPUTE("fleafcompute"),
  FLEAFCOMPUTEFTOR("fleafcomputeftor"), // fleaf in DHE connecting to ftors
  FLEAFCOMPUTE_OHR1_GROUP_NAME("fleafcompute_ohr1"), // OHR0910A
  FLEAFCOMPUTE_OHR2_GROUP_NAME("fleafcompute_ohr2"), // OHR0914A
  FLEAFCOMPUTE_OHR3_GROUP_NAME("fleafcompute_ohr3"), // OHR1010A
  FLEAFCOMPUTE_OHR4_GROUP_NAME("fleafcompute_ohr4"), // OHR1014A
  FLEAFCOMPUTE_PATCH_PANEL_GROUP("fleafcompute_patch_panel"),
  FLEAF1_COMPUTE_PATCH_PANEL1_GROUP_NAME("fleaf1_compute_patch_panel1"),
  FLEAF1_COMPUTE_PATCH_PANEL2_GROUP_NAME("fleaf1_compute_patch_panel2"),
  FLEAF1_COMPUTE_PATCH_PANEL3_GROUP_NAME("fleaf1_compute_patch_panel3"),
  FLEAF1_COMPUTE_PATCH_PANEL4_GROUP_NAME("fleaf1_compute_patch_panel4"),
  FLEAF2_COMPUTE_PATCH_PANEL1_GROUP_NAME("fleaf2_compute_patch_panel1"),
  FLEAF2_COMPUTE_PATCH_PANEL2_GROUP_NAME("fleaf2_compute_patch_panel2"),
  FLEAF2_COMPUTE_PATCH_PANEL3_GROUP_NAME("fleaf2_compute_patch_panel3"),
  FLEAF2_COMPUTE_PATCH_PANEL4_GROUP_NAME("fleaf2_compute_patch_panel4"),
  LCMPOOHR1_PATCH_PANEL_GROUP("lcmpoohr1_fiber_patch_panel"),
  ROW10OHR1AND2_PATCH_PANEL_GROUP_RACK_814("row10ohr1and2_fiber_patch_panel_814"),
  ROW10OHR1AND2_PATCH_PANEL_GROUP_RACK_818("row10ohr1and2_fiber_patch_panel_818"),
  // Patch-panel which connects between two fiber racks. 1410 <-> 1457 (DHA), 3810 <-> 3857 (DHB)
  MGMT_PF_1TO2_TIE_PP_GROUP("mgmt_pf_1to2_tie_pp"),
  // patch-panel used for connections to dha from fiber racks - 814, 818 in DHE
  MGMT_PF_DHA_PP_GROUP("mgmt_pf_dha_pp_group"),
  // patch-panel used for connections to dhb from fiber racks - 814, 818 in DHE
  MGMT_PF_DHB_PP_GROUP("mgmt_pf_dhb_pp_group"),
  // patch-panel used for connections to dhc from fiber racks - 814, 818 in DHE
  MGMT_PF_DHC_PP_GROUP("mgmt_pf_dhc_pp_group"),
  // patch-panel used for connections to dhd from fiber racks - 814, 818 in DHE
  MGMT_PF_DHD_PP_GROUP("mgmt_pf_dhd_pp_group"),
  // patch-panel used for connections to dhe from fiber racks - 1410, 1457, 3810, 3857.... will keep
  // adding other racks as we work on them
  MGMT_PF_DHE_PP_GROUP("mgmt_pf_dhe_pp_group"),
  // patch-panel used for connections to dmrk A  from fiber racks - 814, 818 in DHE
  MGMT_PF_DMRKA_PP_GROUP("mgmt_pf_dmrka_pp_group"),
  // patch-panel used for connections to dmrk A  from fiber racks - 814, 818 in DHE
  MGMT_PF_DMRKB_PP_GROUP("mgmt_pf_dmrkb_pp_group"),
  /**
   * OHR923A
   *
   * <p>[fiberPp, lcMpoOhr1, lcMpoOhr1, lcMpoOhr1, lcMpoOhr1, lcMpoOhr2, lcMpoOhr2, lcMpoOhr2,
   * lcMpoOhr2, lcMpoOhr3, lcMpoOhr3, lcMpoOhr3, lcMpoOhr3, lcMpoOhr4, lcMpoOhr4, lcMpoOhr4,
   * lcMpoOhr4, lcMpoOhr5, lcMpoOhr5, lcMpoOhr5, lcMpoOhr5, lcMpoOhr6, lcMpoOhr6, lcMpoOhr6,
   * lcMpoOhr6]
   *
   * <ul>
   *   <li>fiberPp - parent device
   *   <li>lcMpoOhr1 - component at elevation 1
   *   <li>lcMpoOhr2 - component at elevation 2
   *   <li>lcMpoOhr3 - component at elevation 3
   *   <li>lcMpoOhr4 - component at elevation 4
   *   <li>lcMpoOhr5 - component at elevation 5
   *   <li>lcMpoOhr6 - component at elevation 6
   * </ul>
   */
  LEAF_OHR1_FIBER_WITH_CASSETTES_PATCH_PANEL_GROUP("leaf_ohr1_fiber_with_cassette_panel"),

  /**
   * OHR1023A
   *
   * <p>[fiberPp, lcMpoOhr1, lcMpoOhr1, lcMpoOhr1, lcMpoOhr1, lcMpoOhr2, lcMpoOhr2, lcMpoOhr2,
   * lcMpoOhr2, lcMpoOhr3, lcMpoOhr3, lcMpoOhr3, lcMpoOhr3, lcMpoOhr4, lcMpoOhr4, lcMpoOhr4,
   * lcMpoOhr4, lcMpoOhr5, lcMpoOhr5, lcMpoOhr5, lcMpoOhr5, lcMpoOhr6, lcMpoOhr6, lcMpoOhr6,
   * lcMpoOhr6]
   *
   * <ul>
   *   <li>fiberPp - parent device
   *   <li>lcMpoOhr1 - component at elevation 1
   *   <li>lcMpoOhr2 - component at elevation 2
   *   <li>lcMpoOhr3 - component at elevation 3
   *   <li>lcMpoOhr4 - component at elevation 4
   *   <li>lcMpoOhr5 - component at elevation 5
   *   <li>lcMpoOhr6 - component at elevation 6
   * </ul>
   */
  LEAF_OHR2_FIBER_WITH_CASSETTES_PATCH_PANEL_GROUP("leaf_ohr2_fiber_with_cassette_panel"),

  LEAF_OHR3_FIBER_WITH_CASSETTES_PATCH_PANEL_GROUP("leaf_ohr3_fiber_with_cassette_panel"),

  LEAF_OHR4_FIBER_WITH_CASSETTES_PATCH_PANEL_GROUP("leaf_ohr4_fiber_with_cassette_panel"),

  MTOR_PATCH_PANEL1_FIBER_RACK("mtor_patch_panel_ohr1_fiber_rack"),
  MTOR_PATCH_PANEL2_FIBER_RACK("mtor_patch_panel_ohr2_fiber_rack"),
  OHR_LEAF_COPPER_PATCH_PANEL("ohr_leaf_copper_patch_panel"),
  OHR_COMPUTE_COPPER_PATCH_PANEL("ohr_compute_copper_patch_panel"),
  OHR_COPPER_PATCH_PANEL("ohr_copper_patch_panel"),
  OHR_FIBER_PATCH_PANEL("ohr_fiber_patch_panel"),

  // Test Pods
  TP5_BLEAF_GROUP("tp5_bleaf"),
  TP5_BGPU_GROUP("tp5_bgpu"),
  TP5_EDGE_05U_PATCH_PANEL("tp5_edge_05u_patch_panel");

  private final String value;

  NodeGroupName(@NonNull String value) {
    this.value = value;
  }

  public String getValue() {
    return value;
  }

  public boolean equalsValue(@NonNull String value) {
    return this.value.equalsIgnoreCase(value);
  }
}
