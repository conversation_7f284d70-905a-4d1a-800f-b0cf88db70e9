{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 512958, "iteration": 1, "links": [], "panels": [{"cacheTimeout": null, "datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "continuous-RdYlGr"}, "mappings": [], "min": 0.85, "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 0, "links": [], "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.17", "targets": [{"ad": "_all", "aggregation": "mean", "alias": "RackModelController.createRackModel", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RackModelController.createRackModel.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "RackModelController.deleteRackModel", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RackModelController.deleteRackModel.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "B0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "RackModelController.syncDevicesWithCatalog", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RackModelController.syncDevicesWithCatalog.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "C0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "RackModelController.reCreatePluggablePorts", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RackModelController.reCreatePluggablePorts.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "D0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "DeviceModelController.createDevice", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "DeviceModelController.createDevice.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "E0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "DeviceModelController.deleteDevice", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "DeviceModelController.deleteDevice.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "F0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "DeviceModelController.updateDevice", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "DeviceModelController.updateDevice.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "G0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "RegionController.createRegion", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RegionController.createRegion.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "H0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "RegionController.deleteRegion", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RegionController.deleteRegion.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "I0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "RegionController.deleteRegionAsync", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RegionController.deleteRegionAsync.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "J0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "RegionController.resetConnections", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RegionController.resetConnections.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "K0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "ConnectionController.createConnection", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ConnectionController.createConnection.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "L0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "ConnectionController.deleteConnection", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ConnectionController.deleteConnection.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "M0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "ConnectionController.deleteConnections", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ConnectionController.deleteConnections.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "N0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "NetworkCableController.createNetworkCable", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NetworkCableController.createNetworkCable.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "O0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "NetworkCableController.deleteNetworkCable", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NetworkCableController.deleteNetworkCable.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "P0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "NetworkCableController.deleteNetworkCables", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NetworkCableController.deleteNetworkCables.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "Q0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "PhysicalLinkingController.updateCableLength", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "PhysicalLinkingController.updateCableLength.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "R0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "PortController.updatePort", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "PortController.updatePort.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "S0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "PortController.updatePortGroup", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "PortController.updatePortGroup.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "T0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "ExternalPeerController.createExternalPeer", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ExternalPeerController.createExternalPeer.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "U0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "ExternalPeerController.deleteExternalPeer", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ExternalPeerController.deleteExternalPeer.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "V0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "FabricController.createFabric", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "FabricController.createFabric.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "W0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "FabricController.deleteFabric", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "FabricController.deleteFabric.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "X0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "NodeGroupController.createNodeGroup", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NodeGroupController.createNodeGroup.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "Y0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "NodeGroupController.deleteNodeGroup", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NodeGroupController.deleteNodeGroup.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "Z0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "TrunkDefinitionController.createTrunkDefinitions", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "TrunkDefinitionController.createTrunkDefinitions.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "A1", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "TrunkDefinitionController.deleteTrunkDefinition", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "TrunkDefinitionController.deleteTrunkDefinition.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}], "timeFrom": null, "timeShift": null, "title": "Aggregated SuccessRate", "transformations": [{"id": "reduce", "options": {"reducers": ["min", "mean", "lastNotNull"]}}], "type": "stat"}, {"cacheTimeout": null, "datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 2500}, {"color": "red", "value": 3000}]}, "unit": "dtdurationms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 1, "links": [], "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.17", "targets": [{"ad": "_all", "aggregation": "mean", "alias": "RackModelController.createRackModel", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RackModelController.createRackModel.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "RackModelController.deleteRackModel", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RackModelController.deleteRackModel.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "B0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "RackModelController.syncDevicesWithCatalog", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RackModelController.syncDevicesWithCatalog.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "C0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "RackModelController.reCreatePluggablePorts", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RackModelController.reCreatePluggablePorts.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "D0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "DeviceModelController.createDevice", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "DeviceModelController.createDevice.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "E0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "DeviceModelController.deleteDevice", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "DeviceModelController.deleteDevice.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "F0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "DeviceModelController.updateDevice", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "DeviceModelController.updateDevice.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "G0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "RegionController.createRegion", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RegionController.createRegion.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "H0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "RegionController.deleteRegion", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RegionController.deleteRegion.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "I0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "RegionController.deleteRegionAsync", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RegionController.deleteRegionAsync.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "J0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "RegionController.resetConnections", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RegionController.resetConnections.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "K0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "ConnectionController.createConnection", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ConnectionController.createConnection.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "L0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "ConnectionController.deleteConnection", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ConnectionController.deleteConnection.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "M0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "ConnectionController.deleteConnections", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ConnectionController.deleteConnections.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "N0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "NetworkCableController.createNetworkCable", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NetworkCableController.createNetworkCable.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "O0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "NetworkCableController.deleteNetworkCable", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NetworkCableController.deleteNetworkCable.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "P0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "NetworkCableController.deleteNetworkCables", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NetworkCableController.deleteNetworkCables.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "Q0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "PhysicalLinkingController.updateCableLength", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "PhysicalLinkingController.updateCableLength.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "R0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "PortController.updatePort", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "PortController.updatePort.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "S0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "PortController.updatePortGroup", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "PortController.updatePortGroup.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "T0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "ExternalPeerController.createExternalPeer", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ExternalPeerController.createExternalPeer.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "U0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "ExternalPeerController.deleteExternalPeer", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ExternalPeerController.deleteExternalPeer.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "V0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "FabricController.createFabric", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "FabricController.createFabric.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "W0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "FabricController.deleteFabric", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "FabricController.deleteFabric.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "X0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "NodeGroupController.createNodeGroup", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NodeGroupController.createNodeGroup.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "Y0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "NodeGroupController.deleteNodeGroup", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NodeGroupController.deleteNodeGroup.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "Z0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "TrunkDefinitionController.createTrunkDefinitions", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "TrunkDefinitionController.createTrunkDefinitions.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "A1", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "TrunkDefinitionController.deleteTrunkDefinition", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "TrunkDefinitionController.deleteTrunkDefinition.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}], "timeFrom": null, "timeShift": null, "title": "Aggregated ResourceTime", "transformations": [{"id": "reduce", "options": {"reducers": ["min", "max", "mean", "lastNotNull"]}}], "type": "stat"}, {"aliasColors": {}, "bars": false, "cacheTimeout": null, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "hiddenSeries": false, "id": 1100, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": true, "max": false, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "RackModelController.createRackModel", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RackModelController.createRackModel.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "RackModelController.deleteRackModel", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RackModelController.deleteRackModel.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "B0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "RackModelController.syncDevicesWithCatalog", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RackModelController.syncDevicesWithCatalog.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "C0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "RackModelController.reCreatePluggablePorts", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RackModelController.reCreatePluggablePorts.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "D0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "DeviceModelController.createDevice", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "DeviceModelController.createDevice.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "E0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "DeviceModelController.deleteDevice", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "DeviceModelController.deleteDevice.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "F0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "DeviceModelController.updateDevice", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "DeviceModelController.updateDevice.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "G0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "RegionController.createRegion", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RegionController.createRegion.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "H0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "RegionController.deleteRegion", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RegionController.deleteRegion.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "I0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "RegionController.deleteRegionAsync", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RegionController.deleteRegionAsync.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "J0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "RegionController.resetConnections", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RegionController.resetConnections.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "K0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "ConnectionController.createConnection", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ConnectionController.createConnection.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "L0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "ConnectionController.deleteConnection", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ConnectionController.deleteConnection.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "M0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "ConnectionController.deleteConnections", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ConnectionController.deleteConnections.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "N0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "NetworkCableController.createNetworkCable", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NetworkCableController.createNetworkCable.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "O0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "NetworkCableController.deleteNetworkCable", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NetworkCableController.deleteNetworkCable.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "P0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "NetworkCableController.deleteNetworkCables", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NetworkCableController.deleteNetworkCables.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "Q0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "PhysicalLinkingController.updateCableLength", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "PhysicalLinkingController.updateCableLength.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "R0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "PortController.updatePort", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "PortController.updatePort.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "S0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "PortController.updatePortGroup", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "PortController.updatePortGroup.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "T0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "ExternalPeerController.createExternalPeer", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ExternalPeerController.createExternalPeer.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "U0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "ExternalPeerController.deleteExternalPeer", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ExternalPeerController.deleteExternalPeer.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "V0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "FabricController.createFabric", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "FabricController.createFabric.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "W0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "FabricController.deleteFabric", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "FabricController.deleteFabric.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "X0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "NodeGroupController.createNodeGroup", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NodeGroupController.createNodeGroup.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "Y0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "NodeGroupController.deleteNodeGroup", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NodeGroupController.deleteNodeGroup.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "Z0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "TrunkDefinitionController.createTrunkDefinitions", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "TrunkDefinitionController.createTrunkDefinitions.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "A1", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "TrunkDefinitionController.deleteTrunkDefinition", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "TrunkDefinitionController.deleteTrunkDefinition.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "API Success Rate", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:4302", "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:4303", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"cacheTimeout": null, "cards": {"cardPadding": null, "cardRound": null}, "color": {"cardColor": "#b4ff00", "colorScale": "sqrt", "colorScheme": "interpolateRdYlGn", "exponent": 0.5, "max": 0.9, "min": 1, "mode": "spectrum"}, "dataFormat": "tsbuckets", "datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 13, "y": 8}, "heatmap": {}, "hideZeroBuckets": false, "highlightCards": true, "id": 1101, "legend": {"show": false}, "links": [], "pluginVersion": "7.5.17", "reverseYBuckets": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "RackModelController.createRackModel", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RackModelController.createRackModel.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "RackModelController.deleteRackModel", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RackModelController.deleteRackModel.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "B0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "RackModelController.syncDevicesWithCatalog", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RackModelController.syncDevicesWithCatalog.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "C0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "RackModelController.reCreatePluggablePorts", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RackModelController.reCreatePluggablePorts.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "D0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "DeviceModelController.createDevice", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "DeviceModelController.createDevice.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "E0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "DeviceModelController.deleteDevice", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "DeviceModelController.deleteDevice.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "F0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "DeviceModelController.updateDevice", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "DeviceModelController.updateDevice.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "G0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "RegionController.createRegion", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RegionController.createRegion.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "H0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "RegionController.deleteRegion", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RegionController.deleteRegion.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "I0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "RegionController.deleteRegionAsync", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RegionController.deleteRegionAsync.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "J0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "RegionController.resetConnections", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RegionController.resetConnections.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "K0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "ConnectionController.createConnection", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ConnectionController.createConnection.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "L0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "ConnectionController.deleteConnection", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ConnectionController.deleteConnection.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "M0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "ConnectionController.deleteConnections", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ConnectionController.deleteConnections.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "N0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "NetworkCableController.createNetworkCable", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NetworkCableController.createNetworkCable.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "O0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "NetworkCableController.deleteNetworkCable", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NetworkCableController.deleteNetworkCable.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "P0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "NetworkCableController.deleteNetworkCables", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NetworkCableController.deleteNetworkCables.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "Q0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "PhysicalLinkingController.updateCableLength", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "PhysicalLinkingController.updateCableLength.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "R0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "PortController.updatePort", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "PortController.updatePort.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "S0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "PortController.updatePortGroup", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "PortController.updatePortGroup.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "T0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "ExternalPeerController.createExternalPeer", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ExternalPeerController.createExternalPeer.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "U0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "ExternalPeerController.deleteExternalPeer", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ExternalPeerController.deleteExternalPeer.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "V0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "FabricController.createFabric", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "FabricController.createFabric.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "W0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "FabricController.deleteFabric", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "FabricController.deleteFabric.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "X0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "NodeGroupController.createNodeGroup", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NodeGroupController.createNodeGroup.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "Y0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "NodeGroupController.deleteNodeGroup", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NodeGroupController.deleteNodeGroup.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "Z0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "TrunkDefinitionController.createTrunkDefinitions", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "TrunkDefinitionController.createTrunkDefinitions.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "A1", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "TrunkDefinitionController.deleteTrunkDefinition", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "TrunkDefinitionController.deleteTrunkDefinition.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}], "timeFrom": null, "timeShift": null, "title": "API Success Rate (Heat Map)", "tooltip": {"show": true, "showHistogram": false}, "type": "heatmap", "xAxis": {"show": true}, "xBucketNumber": null, "xBucketSize": null, "yAxis": {"decimals": null, "format": "percentunit", "logBase": 1, "max": null, "min": null, "show": true, "splitFactor": null}, "yBucketBound": "middle", "yBucketNumber": null, "yBucketSize": null}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 16}, "id": 4, "panels": [], "title": "RackModelController", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 17}, "hiddenSeries": false, "id": 5, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createRackModel", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RackModelController.createRackModel.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteRackModel", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RackModelController.deleteRackModel.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "syncDevicesWithCatalog", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RackModelController.syncDevicesWithCatalog.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "C2", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "reCreatePluggablePorts", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RackModelController.reCreatePluggablePorts.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "D3", "region": "$region", "suppressQueryFanout": false}], "thresholds": [{"$$hashKey": "object:2780", "colorMode": "critical", "fill": false, "line": true, "op": "lt", "value": 0.9, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Minimal Success Rate: <PERSON><PERSON>ModelController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1955", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1956", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": null, "description": "", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 17}, "hiddenSeries": false, "id": 6, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createRackModel", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "hide": false, "host": "_all", "includeDimensions": true, "mql": "RackModelController.createRackModel.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteRackModel", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "hide": false, "host": "_all", "includeDimensions": true, "mql": "RackModelController.deleteRackModel.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "syncDevicesWithCatalog", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "hide": false, "host": "_all", "includeDimensions": true, "mql": "RackModelController.syncDevicesWithCatalog.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "C2", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "reCreatePluggablePorts", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "hide": false, "host": "_all", "includeDimensions": true, "mql": "RackModelController.reCreatePluggablePorts.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "D3", "region": "$region", "suppressQueryFanout": false}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Resource Time (Splat measured): RackModelController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1745", "decimals": 1, "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1746", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 18}, "hiddenSeries": false, "id": 7, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createRackModel", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RackModelController.createRackModel.ResponseOut.Count[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteRackModel", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RackModelController.deleteRackModel.ResponseOut.Count[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "syncDevicesWithCatalog", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RackModelController.syncDevicesWithCatalog.ResponseOut.Count[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "C2", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "reCreatePluggablePorts", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RackModelController.reCreatePluggablePorts.ResponseOut.Count[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "D3", "region": "$region", "suppressQueryFanout": false}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Response Count: <PERSON><PERSON><PERSON><PERSON>lController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1836", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1837", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 18}, "hiddenSeries": false, "id": 8, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": true, "values": true}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createRackModel", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RackModelController.createRackModel.Fault[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteRackModel", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RackModelController.deleteRackModel.Fault[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "syncDevicesWithCatalog", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RackModelController.syncDevicesWithCatalog.Fault[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "C2", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "reCreatePluggablePorts", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RackModelController.reCreatePluggablePorts.Fault[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "D3", "region": "$region", "suppressQueryFanout": false}], "thresholds": [{"$$hashKey": "object:2886", "colorMode": "critical", "fill": false, "line": true, "op": "gt", "value": 3, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Error Count: <PERSON><PERSON><PERSON><PERSON><PERSON>ont<PERSON><PERSON>", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1955", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1956", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 33}, "id": 9, "panels": [], "title": "DeviceModelController", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 34}, "hiddenSeries": false, "id": 10, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createDevice", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "DeviceModelController.createDevice.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteDevice", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "DeviceModelController.deleteDevice.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "updateDevice", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "DeviceModelController.updateDevice.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "C2", "region": "$region", "suppressQueryFanout": false}], "thresholds": [{"$$hashKey": "object:2780", "colorMode": "critical", "fill": false, "line": true, "op": "lt", "value": 0.9, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Minimal Success Rate: DeviceModelController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1955", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1956", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": null, "description": "", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 34}, "hiddenSeries": false, "id": 11, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createDevice", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "hide": false, "host": "_all", "includeDimensions": true, "mql": "DeviceModelController.createDevice.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteDevice", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "hide": false, "host": "_all", "includeDimensions": true, "mql": "DeviceModelController.deleteDevice.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "updateDevice", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "hide": false, "host": "_all", "includeDimensions": true, "mql": "DeviceModelController.updateDevice.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "C2", "region": "$region", "suppressQueryFanout": false}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Resource Time (Splat measured): DeviceModelController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1745", "decimals": 1, "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1746", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 35}, "hiddenSeries": false, "id": 12, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createDevice", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "DeviceModelController.createDevice.ResponseOut.Count[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteDevice", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "DeviceModelController.deleteDevice.ResponseOut.Count[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "updateDevice", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "DeviceModelController.updateDevice.ResponseOut.Count[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "C2", "region": "$region", "suppressQueryFanout": false}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Response Count: DeviceModelController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1836", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1837", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 35}, "hiddenSeries": false, "id": 13, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": true, "values": true}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createDevice", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "DeviceModelController.createDevice.Fault[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteDevice", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "DeviceModelController.deleteDevice.Fault[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "updateDevice", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "DeviceModelController.updateDevice.Fault[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "C2", "region": "$region", "suppressQueryFanout": false}], "thresholds": [{"$$hashKey": "object:2886", "colorMode": "critical", "fill": false, "line": true, "op": "gt", "value": 3, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Error Count: <PERSON><PERSON><PERSON><PERSON>l<PERSON>ontroller", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1955", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1956", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 50}, "id": 14, "panels": [], "title": "RegionController", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 51}, "hiddenSeries": false, "id": 15, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createRegion", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RegionController.createRegion.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteRegion", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RegionController.deleteRegion.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteRegionAsync", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RegionController.deleteRegionAsync.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "C2", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "resetConnections", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RegionController.resetConnections.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "D3", "region": "$region", "suppressQueryFanout": false}], "thresholds": [{"$$hashKey": "object:2780", "colorMode": "critical", "fill": false, "line": true, "op": "lt", "value": 0.9, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Minimal Success Rate: RegionController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1955", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1956", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": null, "description": "", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 51}, "hiddenSeries": false, "id": 16, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createRegion", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "hide": false, "host": "_all", "includeDimensions": true, "mql": "RegionController.createRegion.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteRegion", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "hide": false, "host": "_all", "includeDimensions": true, "mql": "RegionController.deleteRegion.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteRegionAsync", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "hide": false, "host": "_all", "includeDimensions": true, "mql": "RegionController.deleteRegionAsync.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "C2", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "resetConnections", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "hide": false, "host": "_all", "includeDimensions": true, "mql": "RegionController.resetConnections.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "D3", "region": "$region", "suppressQueryFanout": false}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Resource Time (Splat measured): RegionController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1745", "decimals": 1, "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1746", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 52}, "hiddenSeries": false, "id": 17, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createRegion", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RegionController.createRegion.ResponseOut.Count[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteRegion", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RegionController.deleteRegion.ResponseOut.Count[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteRegionAsync", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RegionController.deleteRegionAsync.ResponseOut.Count[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "C2", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "resetConnections", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RegionController.resetConnections.ResponseOut.Count[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "D3", "region": "$region", "suppressQueryFanout": false}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Response Count: RegionController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1836", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1837", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 52}, "hiddenSeries": false, "id": 18, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": true, "values": true}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createRegion", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RegionController.createRegion.Fault[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteRegion", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RegionController.deleteRegion.Fault[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteRegionAsync", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RegionController.deleteRegionAsync.Fault[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "C2", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "resetConnections", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "RegionController.resetConnections.Fault[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "D3", "region": "$region", "suppressQueryFanout": false}], "thresholds": [{"$$hashKey": "object:2886", "colorMode": "critical", "fill": false, "line": true, "op": "gt", "value": 3, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Error Count: RegionController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1955", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1956", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 67}, "id": 19, "panels": [], "title": "ConnectionController", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 68}, "hiddenSeries": false, "id": 20, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createConnection", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ConnectionController.createConnection.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteConnection", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ConnectionController.deleteConnection.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteConnections", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ConnectionController.deleteConnections.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "C2", "region": "$region", "suppressQueryFanout": false}], "thresholds": [{"$$hashKey": "object:2780", "colorMode": "critical", "fill": false, "line": true, "op": "lt", "value": 0.9, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Minimal Success Rate: ConnectionController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1955", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1956", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": null, "description": "", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 68}, "hiddenSeries": false, "id": 21, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createConnection", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "hide": false, "host": "_all", "includeDimensions": true, "mql": "ConnectionController.createConnection.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteConnection", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "hide": false, "host": "_all", "includeDimensions": true, "mql": "ConnectionController.deleteConnection.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteConnections", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "hide": false, "host": "_all", "includeDimensions": true, "mql": "ConnectionController.deleteConnections.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "C2", "region": "$region", "suppressQueryFanout": false}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Resource Time (Splat measured): ConnectionController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1745", "decimals": 1, "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1746", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 69}, "hiddenSeries": false, "id": 22, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createConnection", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ConnectionController.createConnection.ResponseOut.Count[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteConnection", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ConnectionController.deleteConnection.ResponseOut.Count[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteConnections", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ConnectionController.deleteConnections.ResponseOut.Count[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "C2", "region": "$region", "suppressQueryFanout": false}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Response Count: ConnectionController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1836", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1837", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 69}, "hiddenSeries": false, "id": 23, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": true, "values": true}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createConnection", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ConnectionController.createConnection.Fault[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteConnection", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ConnectionController.deleteConnection.Fault[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteConnections", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ConnectionController.deleteConnections.Fault[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "C2", "region": "$region", "suppressQueryFanout": false}], "thresholds": [{"$$hashKey": "object:2886", "colorMode": "critical", "fill": false, "line": true, "op": "gt", "value": 3, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Error Count: ConnectionController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1955", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1956", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 84}, "id": 24, "panels": [], "title": "NetworkCableController", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 85}, "hiddenSeries": false, "id": 25, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createNetworkCable", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NetworkCableController.createNetworkCable.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteNetworkCable", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NetworkCableController.deleteNetworkCable.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteNetworkCables", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NetworkCableController.deleteNetworkCables.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "C2", "region": "$region", "suppressQueryFanout": false}], "thresholds": [{"$$hashKey": "object:2780", "colorMode": "critical", "fill": false, "line": true, "op": "lt", "value": 0.9, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Minimal Success Rate: NetworkCableController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1955", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1956", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": null, "description": "", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 85}, "hiddenSeries": false, "id": 26, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createNetworkCable", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "hide": false, "host": "_all", "includeDimensions": true, "mql": "NetworkCableController.createNetworkCable.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteNetworkCable", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "hide": false, "host": "_all", "includeDimensions": true, "mql": "NetworkCableController.deleteNetworkCable.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteNetworkCables", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "hide": false, "host": "_all", "includeDimensions": true, "mql": "NetworkCableController.deleteNetworkCables.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "C2", "region": "$region", "suppressQueryFanout": false}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Resource Time (Splat measured): NetworkCableController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1745", "decimals": 1, "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1746", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 86}, "hiddenSeries": false, "id": 27, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createNetworkCable", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NetworkCableController.createNetworkCable.ResponseOut.Count[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteNetworkCable", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NetworkCableController.deleteNetworkCable.ResponseOut.Count[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteNetworkCables", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NetworkCableController.deleteNetworkCables.ResponseOut.Count[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "C2", "region": "$region", "suppressQueryFanout": false}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Response Count: NetworkCableController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1836", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1837", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 86}, "hiddenSeries": false, "id": 28, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": true, "values": true}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createNetworkCable", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NetworkCableController.createNetworkCable.Fault[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteNetworkCable", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NetworkCableController.deleteNetworkCable.Fault[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteNetworkCables", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NetworkCableController.deleteNetworkCables.Fault[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "C2", "region": "$region", "suppressQueryFanout": false}], "thresholds": [{"$$hashKey": "object:2886", "colorMode": "critical", "fill": false, "line": true, "op": "gt", "value": 3, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Error Count: NetworkCableController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1955", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1956", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 101}, "id": 29, "panels": [], "title": "PhysicalLinkingController", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 102}, "hiddenSeries": false, "id": 30, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "updateCableLength", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "PhysicalLinkingController.updateCableLength.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}], "thresholds": [{"$$hashKey": "object:2780", "colorMode": "critical", "fill": false, "line": true, "op": "lt", "value": 0.9, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Minimal Success Rate: PhysicalLinkingController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1955", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1956", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": null, "description": "", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 102}, "hiddenSeries": false, "id": 31, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "updateCableLength", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "hide": false, "host": "_all", "includeDimensions": true, "mql": "PhysicalLinkingController.updateCableLength.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Resource Time (Splat measured): PhysicalLinkingController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1745", "decimals": 1, "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1746", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 103}, "hiddenSeries": false, "id": 32, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "updateCableLength", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "PhysicalLinkingController.updateCableLength.ResponseOut.Count[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Response Count: PhysicalLinkingController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1836", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1837", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 103}, "hiddenSeries": false, "id": 33, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": true, "values": true}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "updateCableLength", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "PhysicalLinkingController.updateCableLength.Fault[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}], "thresholds": [{"$$hashKey": "object:2886", "colorMode": "critical", "fill": false, "line": true, "op": "gt", "value": 3, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Error Count: PhysicalLinkingController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1955", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1956", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 118}, "id": 34, "panels": [], "title": "PortController", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 119}, "hiddenSeries": false, "id": 35, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "updatePort", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "PortController.updatePort.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "updatePortGroup", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "PortController.updatePortGroup.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}], "thresholds": [{"$$hashKey": "object:2780", "colorMode": "critical", "fill": false, "line": true, "op": "lt", "value": 0.9, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Minimal Success Rate: PortController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1955", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1956", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": null, "description": "", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 119}, "hiddenSeries": false, "id": 36, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "updatePort", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "hide": false, "host": "_all", "includeDimensions": true, "mql": "PortController.updatePort.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "updatePortGroup", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "hide": false, "host": "_all", "includeDimensions": true, "mql": "PortController.updatePortGroup.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Resource Time (Splat measured): PortController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1745", "decimals": 1, "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1746", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 120}, "hiddenSeries": false, "id": 37, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "updatePort", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "PortController.updatePort.ResponseOut.Count[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "updatePortGroup", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "PortController.updatePortGroup.ResponseOut.Count[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Response Count: PortController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1836", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1837", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 120}, "hiddenSeries": false, "id": 38, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": true, "values": true}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "updatePort", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "PortController.updatePort.Fault[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "updatePortGroup", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "PortController.updatePortGroup.Fault[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}], "thresholds": [{"$$hashKey": "object:2886", "colorMode": "critical", "fill": false, "line": true, "op": "gt", "value": 3, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Error Count: Port<PERSON><PERSON>roll<PERSON>", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1955", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1956", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 135}, "id": 39, "panels": [], "title": "ExternalPeerController", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 136}, "hiddenSeries": false, "id": 40, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createExternalPeer", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ExternalPeerController.createExternalPeer.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteExternalPeer", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ExternalPeerController.deleteExternalPeer.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}], "thresholds": [{"$$hashKey": "object:2780", "colorMode": "critical", "fill": false, "line": true, "op": "lt", "value": 0.9, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Minimal Success Rate: ExternalPeerController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1955", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1956", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": null, "description": "", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 136}, "hiddenSeries": false, "id": 41, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createExternalPeer", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "hide": false, "host": "_all", "includeDimensions": true, "mql": "ExternalPeerController.createExternalPeer.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteExternalPeer", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "hide": false, "host": "_all", "includeDimensions": true, "mql": "ExternalPeerController.deleteExternalPeer.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Resource Time (Splat measured): ExternalPeerController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1745", "decimals": 1, "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1746", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 137}, "hiddenSeries": false, "id": 42, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createExternalPeer", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ExternalPeerController.createExternalPeer.ResponseOut.Count[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteExternalPeer", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ExternalPeerController.deleteExternalPeer.ResponseOut.Count[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Response Count: ExternalPeerController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1836", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1837", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 137}, "hiddenSeries": false, "id": 43, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": true, "values": true}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createExternalPeer", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ExternalPeerController.createExternalPeer.Fault[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteExternalPeer", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "ExternalPeerController.deleteExternalPeer.Fault[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}], "thresholds": [{"$$hashKey": "object:2886", "colorMode": "critical", "fill": false, "line": true, "op": "gt", "value": 3, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Error Count: ExternalPeerController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1955", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1956", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 152}, "id": 44, "panels": [], "title": "FabricController", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 153}, "hiddenSeries": false, "id": 45, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createFabric", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "FabricController.createFabric.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteFabric", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "FabricController.deleteFabric.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}], "thresholds": [{"$$hashKey": "object:2780", "colorMode": "critical", "fill": false, "line": true, "op": "lt", "value": 0.9, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Minimal Success Rate: FabricController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1955", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1956", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": null, "description": "", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 153}, "hiddenSeries": false, "id": 46, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createFabric", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "hide": false, "host": "_all", "includeDimensions": true, "mql": "FabricController.createFabric.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteFabric", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "hide": false, "host": "_all", "includeDimensions": true, "mql": "FabricController.deleteFabric.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Resource Time (Splat measured): FabricController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1745", "decimals": 1, "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1746", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 154}, "hiddenSeries": false, "id": 47, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createFabric", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "FabricController.createFabric.ResponseOut.Count[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteFabric", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "FabricController.deleteFabric.ResponseOut.Count[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Response Count: <PERSON><PERSON><PERSON>Controller", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1836", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1837", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 154}, "hiddenSeries": false, "id": 48, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": true, "values": true}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createFabric", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "FabricController.createFabric.Fault[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteFabric", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "FabricController.deleteFabric.Fault[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}], "thresholds": [{"$$hashKey": "object:2886", "colorMode": "critical", "fill": false, "line": true, "op": "gt", "value": 3, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Error Count: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1955", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1956", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 169}, "id": 49, "panels": [], "title": "NodeGroupController", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 170}, "hiddenSeries": false, "id": 50, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createNodeGroup", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NodeGroupController.createNodeGroup.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteNodeGroup", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NodeGroupController.deleteNodeGroup.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}], "thresholds": [{"$$hashKey": "object:2780", "colorMode": "critical", "fill": false, "line": true, "op": "lt", "value": 0.9, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Minimal Success Rate: NodeGroupController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1955", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1956", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": null, "description": "", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 170}, "hiddenSeries": false, "id": 51, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createNodeGroup", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "hide": false, "host": "_all", "includeDimensions": true, "mql": "NodeGroupController.createNodeGroup.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteNodeGroup", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "hide": false, "host": "_all", "includeDimensions": true, "mql": "NodeGroupController.deleteNodeGroup.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Resource Time (Splat measured): NodeGroupController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1745", "decimals": 1, "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1746", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 171}, "hiddenSeries": false, "id": 52, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createNodeGroup", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NodeGroupController.createNodeGroup.ResponseOut.Count[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteNodeGroup", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NodeGroupController.deleteNodeGroup.ResponseOut.Count[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Response Count: NodeGroupController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1836", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1837", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 171}, "hiddenSeries": false, "id": 53, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": true, "values": true}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createNodeGroup", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NodeGroupController.createNodeGroup.Fault[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteNodeGroup", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "NodeGroupController.deleteNodeGroup.Fault[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}], "thresholds": [{"$$hashKey": "object:2886", "colorMode": "critical", "fill": false, "line": true, "op": "gt", "value": 3, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Error Count: NodeGroupController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1955", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1956", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 186}, "id": 54, "panels": [], "title": "TrunkDefinitionController", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 187}, "hiddenSeries": false, "id": 55, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createTrunkDefinitions", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "TrunkDefinitionController.createTrunkDefinitions.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteTrunkDefinition", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "TrunkDefinitionController.deleteTrunkDefinition.SuccessRate[$interval].grouping().min()", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}], "thresholds": [{"$$hashKey": "object:2780", "colorMode": "critical", "fill": false, "line": true, "op": "lt", "value": 0.9, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Minimal Success Rate: TrunkDefinitionController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1955", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1956", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": null, "description": "", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 187}, "hiddenSeries": false, "id": 56, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createTrunkDefinitions", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "hide": false, "host": "_all", "includeDimensions": true, "mql": "TrunkDefinitionController.createTrunkDefinitions.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteTrunkDefinition", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "hide": false, "host": "_all", "includeDimensions": true, "mql": "TrunkDefinitionController.deleteTrunkDefinition.ResourceTime[$interval].grouping().percentile($percentile)", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Resource Time (Splat measured): TrunkDefinitionController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1745", "decimals": 1, "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1746", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 188}, "hiddenSeries": false, "id": 57, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createTrunkDefinitions", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "TrunkDefinitionController.createTrunkDefinitions.ResponseOut.Count[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteTrunkDefinition", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "TrunkDefinitionController.deleteTrunkDefinition.ResponseOut.Count[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Response Count: TrunkDefinitionController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1836", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1837", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 188}, "hiddenSeries": false, "id": 58, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": true, "values": true}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"ad": "_all", "aggregation": "mean", "alias": "createTrunkDefinitions", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "TrunkDefinitionController.createTrunkDefinitions.Fault[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "A0", "region": "$region", "suppressQueryFanout": false}, {"ad": "_all", "aggregation": "mean", "alias": "deleteTrunkDefinition", "crossRealmAggregation": "", "experimentalPerformanceImprovement": false, "fillMissing": false, "fleet": "$fleet", "granularity": "60", "host": "_all", "includeDimensions": true, "mql": "TrunkDefinitionController.deleteTrunkDefinition.Fault[$interval].grouping().sum()", "project": "$project", "realm": "$realm", "refId": "B1", "region": "$region", "suppressQueryFanout": false}], "thresholds": [{"$$hashKey": "object:2886", "colorMode": "critical", "fill": false, "line": true, "op": "gt", "value": 3, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Error Count: TrunkDefinitionController", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1955", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1956", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": false}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": false, "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "current": {"selected": false, "text": "oc1", "value": "oc1"}, "datasource": null, "definition": "realms()", "description": null, "error": null, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "realm", "options": [], "query": "realms()", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "us-ashburn-1", "value": "us-ashburn-1"}, "datasource": null, "definition": "regions($realm)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "region", "options": [], "query": "regions($realm)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "rmcunstable", "value": "rmcunstable"}, "description": null, "error": null, "hide": 0, "includeAll": false, "label": "", "multi": false, "name": "project", "options": [{"selected": true, "text": "rmcunstable", "value": "rmcunstable"}, {"selected": false, "text": "rmc-dev", "value": "rmc-dev"}, {"selected": false, "text": "rmcromaabl", "value": "rmcromaabl"}], "query": "rmcu<PERSON><PERSON>,rmc-dev,rmcromaabl", "queryValue": "", "skipUrlSync": false, "type": "custom"}, {"allValue": null, "current": {"selected": false, "text": "rmc-api", "value": "rmc-api"}, "description": null, "error": null, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "fleet", "options": [{"selected": true, "text": "rmc-api", "value": "rmc-api"}], "query": "rmc-api", "queryValue": "", "skipUrlSync": false, "type": "custom"}, {"allValue": null, "current": {"selected": true, "text": "0.9", "value": "0.9"}, "description": null, "error": null, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "percentile", "options": [{"selected": false, "text": ".5", "value": ".5"}, {"selected": true, "text": "0.9", "value": "0.9"}, {"selected": false, "text": "0.99", "value": "0.99"}], "query": ".5,0.9,0.99", "queryValue": "", "skipUrlSync": false, "type": "custom"}, {"auto": false, "auto_count": 30, "auto_min": "10s", "current": {"selected": false, "text": "1h", "value": "1h"}, "description": null, "error": null, "hide": 0, "label": null, "name": "interval", "options": [{"selected": false, "text": "1m", "value": "1m"}, {"selected": false, "text": "10m", "value": "10m"}, {"selected": false, "text": "30m", "value": "30m"}, {"selected": true, "text": "1h", "value": "1h"}, {"selected": false, "text": "6h", "value": "6h"}, {"selected": false, "text": "12h", "value": "12h"}, {"selected": false, "text": "1d", "value": "1d"}], "query": "1m,10m,30m,1h,6h,12h,1d", "queryValue": "", "refresh": 2, "skipUrlSync": false, "type": "interval"}]}, "time": {"from": "now-7d", "to": "now"}, "timepicker": {"refresh_intervals": ["1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "DCMS L1 - Patch", "uid": "LaPfho8Hz", "version": 1}