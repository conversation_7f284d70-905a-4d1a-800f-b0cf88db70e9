package com.oracle.oci.rmc.network.processor;

import static com.oracle.oci.rmc.network.api.deployment.DeploymentTask.DeploymentTaskState.COMPLETED;
import static com.oracle.oci.rmc.network.api.deployment.DeploymentTask.DeploymentTaskState.PENDING;
import static com.oracle.oci.rmc.workrequest.api.WorkRequest.Status.CANCELED;
import static com.oracle.oci.rmc.workrequest.api.WorkRequest.Status.FAILED;
import static com.oracle.oci.rmc.workrequest.api.WorkRequest.Status.IN_PROGRESS;
import static com.oracle.oci.rmc.workrequest.api.WorkRequest.Status.SUCCEEDED;
import static io.micronaut.core.util.StringUtils.TRUE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.oracle.bmc.model.BmcException;
import com.oracle.oci.network.deviceaccess.service.DeviceAccess;
import com.oracle.oci.rmc.changemanagement.api.service.ChangeManagementService;
import com.oracle.oci.rmc.model.RmcId;
import com.oracle.oci.rmc.model.RmcIdType;
import com.oracle.oci.rmc.model.RmcIdType.Network;
import com.oracle.oci.rmc.network.api.config.ConfigsMetadata;
import com.oracle.oci.rmc.network.api.config.ConfigsMetadataService;
import com.oracle.oci.rmc.network.api.config.ConfigsMetadataService$CreateConfigsMetadataBuilder;
import com.oracle.oci.rmc.network.api.deployment.Deployment;
import com.oracle.oci.rmc.network.api.deployment.Deployment$DeploymentParametersBuilder;
import com.oracle.oci.rmc.network.api.deployment.Deployment.DeploymentType;
import com.oracle.oci.rmc.network.api.deployment.DeploymentService;
import com.oracle.oci.rmc.network.api.deployment.DeploymentTask;
import com.oracle.oci.rmc.network.api.deployment.DeploymentTask.DeploymentTaskState;
import com.oracle.oci.rmc.network.api.deployment.DeploymentTaskService;
import com.oracle.oci.rmc.network.api.deployment.ExecutableTask;
import com.oracle.oci.rmc.network.api.deployment.ExecuteCommandParameters;
import com.oracle.oci.rmc.network.api.deployment.tasks.RunOperationalTask;
import com.oracle.oci.rmc.network.api.device.Device;
import com.oracle.oci.rmc.network.api.device.DeviceService;
import com.oracle.oci.rmc.network.api.fabric.FabricService;
import com.oracle.oci.rmc.network.api.firmware.FirmwareImage;
import com.oracle.oci.rmc.network.api.region.Region;
import com.oracle.oci.rmc.network.api.region.RegionService;
import com.oracle.oci.rmc.network.api.state.DeviceState;
import com.oracle.oci.rmc.network.api.state.DeviceStateService;
import com.oracle.oci.rmc.network.impl.device.DeviceRepository;
import com.oracle.oci.rmc.network.impl.firmware.FirmwareImageRepository;
import com.oracle.oci.rmc.network.impl.state.DefaultDeviceStateService;
import com.oracle.oci.rmc.workrequest.api.WorkRequest;
import com.oracle.oci.rmc.workrequest.api.WorkRequest.Command;
import com.oracle.oci.rmc.workrequest.api.WorkRequestService;
import com.oracle.oci.sfw.micronaut.http.pagination.PaginatedList;
import io.micronaut.context.annotation.Property;
import io.micronaut.test.annotation.MockBean;
import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import jakarta.inject.Inject;
import java.math.BigInteger;
import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.MethodOrderer.OrderAnnotation;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.Timeout;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

@Timeout(value = 180)
@TestMethodOrder(OrderAnnotation.class)
@MicronautTest(transactional = false)
@Property(
    name = "datasources.default.auto-commit",
    value = TRUE) // testCompleteFabricManifest works only in autocommit mode
public class DeploymentTaskProcessorTest {
  @Inject DeploymentProcessor deploymentProcessor;
  @Inject DeploymentService deploymentService;
  @Inject DeploymentTaskService deploymentTaskService;
  @Inject WorkRequestService workRequestService;
  @Inject FabricService fabricService;
  @Inject DeviceStateService deviceStateService;
  @Inject ConfigsMetadataService configsMetadataService;
  @Inject DeviceService deviceService;
  @Inject DeviceRepository deviceRepository;
  @Inject FirmwareImageRepository firmwareImageRepository;
  @Inject ChangeManagementService changeManagementService;
  @Inject RegionService regionService;
  @Inject DeviceAccess deviceAccess;

  @MockBean(ChangeManagementService.class)
  ChangeManagementService changeManagementServiceMock() {
    return Mockito.mock(ChangeManagementService.class);
  }

  @MockBean(DeviceAccess.class)
  DeviceAccess deviceAccessMock() {
    return Mockito.mock(DeviceAccess.class);
  }

  private static Device TEST_DEVICE;
  private static int DEVICE_NUMBER = 0;

  // This is a overly generous value.. the logic in the tests here breaks as soon as tasks are
  // processed
  private static final int WAIT_TIMEOUT = 120;

  private RmcId testFabricId;

  @Mock private WorkRequest workRequest;
  RmcId validDeploymentTaskId;
  public static final List<String> COMMANDS = List.of("configure terminal", "end");

  private Region region;

  @BeforeEach
  void setup() {
    String airportCode = RandomStringUtils.insecure().nextAlphanumeric(3);
    region =
        regionService.create(
            new RegionService.CreateNetworkRegion(airportCode, "OC1", "Commercial", "ADMIN"));

    System.setProperty("test.mode", "true");

    // create fabric before device - required
    testFabricId = RmcId.generate(RmcIdType.DCMS.FABRIC);
    createTestFabric();

    TEST_DEVICE = createDeviceFactory(++DEVICE_NUMBER);
    ConfigsMetadata configsMetadata = createConfigMetadataForDevice(TEST_DEVICE);
    FirmwareImage firmwareImage = createFirmwareImage();

    createDeviceStateForDevice(TEST_DEVICE, configsMetadata, firmwareImage);
    MockitoAnnotations.openMocks(this);
    when(changeManagementService.isChangePermitted(any())).thenReturn(true);
    when(deviceAccess.checkConnection(any(), any())).thenReturn(true);
  }

  /*
  Creates mock devices to be used for the test suite
   */
  Device createDeviceFactory(int deviceIndex) {
    Device device =
        deviceService.create(
            new DeviceService.CreateNetworkDevice(
                testFabricId,
                RmcId.generate(RmcIdType.DCMS.DEVICE_MODEL),
                null,
                region.airportCode(),
                "abl-c1-t1-r" + deviceIndex,
                Device.Platform.EOS.toString(),
                "4.1.1",
                Device.Role.MLEAF.role(),
                deviceIndex,
                "dev"),
            region.id(),
            null);
    return deviceRepository.update(device.withIpAddress("127.0.0.1"));
  }

  ConfigsMetadata createConfigMetadataForDevice(Device device) {
    return configsMetadataService.create(
        ConfigsMetadataService$CreateConfigsMetadataBuilder.builder()
            .createdBy("createdBy")
            .networkModelId(device.networkModelId())
            .entityId(device.id())
            .configType(ConfigsMetadata.ConfigType.DEVICE)
            .manifestId(RmcId.generate(RmcIdType.Network.MANIFEST))
            .sha256("sha256")
            .objectUrl("objectUrl")
            .fileName("fileName")
            .build(),
        null);
  }

  void createDeviceStateForDevice(
      Device device, ConfigsMetadata configsMetadata, FirmwareImage firmwareImage) {
    DeviceStateService.CreateDeviceState deviceState =
        new DefaultDeviceStateService.CreateDeviceState(
            device.id(),
            device.networkModelId(),
            DeviceState.LifeCycleState.MAINTENANCE,
            DeviceState.LifeCycleState.IN_SERVICE,
            DeviceState.ProcessState.VALIDATED,
            configsMetadata.id(),
            RmcId.generate(Network.CONFIGS_METADATA),
            firmwareImage.id(),
            RmcId.generate(RmcIdType.Network.FIRMWARE_IMAGE),
            "dev");
    deviceStateService.create(deviceState, null);
  }

  private FirmwareImage createFirmwareImage() {
    return firmwareImageRepository.save(
        new FirmwareImage(
            RmcId.generate(RmcIdType.Network.FIRMWARE_IMAGE),
            region.id(),
            "fileName",
            "firmwareVersion",
            BigInteger.ZERO,
            "md5Checksum",
            "firmwareUrl",
            "createdBy",
            "modifiedBy",
            0L,
            Instant.now(),
            Instant.now()));
  }

  /*
  Creates a default Fabric for mock devices created in the test suite
   */
  void createTestFabric() {
    fabricService.create(
        new FabricService.CreateNetworkFabric(
            "backend", "backend v12 2", region.airportCode(), "1", 1, testFabricId, "dev12"),
        null);
  }

  private <T extends ExecutableTask> void setupCommand(T command) throws Exception {
    ExecutableTask spyCommand = spy(command);
    ExecuteCommandParameters executeCommandParameters =
        ExecuteCommandParameters.builder()
            .ipAddress("test")
            .platform("eos")
            .deviceAccess(deviceAccess)
            .build();
    doNothing().when(spyCommand).execute(eq(executeCommandParameters));
    when(workRequest.command()).thenReturn((Command) spyCommand);
  }

  private <T extends ExecutableTask> void setupCommand(T command, Exception exceptionToThrow)
      throws Exception {
    ExecutableTask spyCommand = spy(command);
    ExecuteCommandParameters executeCommandParameters =
        ExecuteCommandParameters.builder()
            .ipAddress("test")
            .platform("eos")
            .deviceAccess(deviceAccess)
            .build();
    doThrow(exceptionToThrow).when(spyCommand).execute(eq(executeCommandParameters));
    when(workRequest.command()).thenReturn((Command) spyCommand);
  }

  private Deployment createDeployment(
      DeploymentType type, Boolean ignoreChangeManagement, Deployment.DeploymentParameters params) {
    return deploymentService.create(
        new DeploymentService.CreateDeployment(
            type,
            region.airportCode(),
            Device.Platform.EOS,
            "theRole1",
            "backend",
            25,
            1,
            List.of(TEST_DEVICE.id().toString()),
            params,
            1,
            null,
            ignoreChangeManagement,
            "deploymentTaskProcessorTest",
            null));
  }

  private void runEvaluationThread(CountDownLatch latch, Deployment deployment) {
    new Thread(
            () -> {
              try {
                final Instant waitStart = Instant.now();
                int waitInterval = 500;
                do {
                  Deployment currentDeployment = deploymentService.get(deployment.id()).get();

                  if (currentDeployment.isEvaluated() == 1) {
                    latch.countDown();
                    return;
                  }

                  Thread.sleep(waitInterval);
                  waitInterval = Math.min(waitInterval * 2, 5000);
                } while (Duration.between(waitStart, Instant.now()).getSeconds() < 100);

                Assertions.fail("Deployment task evaluation timed out.");
              } catch (Exception e) {
                e.printStackTrace();
              }
            })
        .start();
  }

  private void waitForDeploymentEvaluation(Deployment deployment) {
    Instant start = Instant.now();
    do {
      Deployment pollDeployment = deploymentService.get(deployment.id()).get();
      if (pollDeployment.isEvaluated() == 1) {
        return;
      }

      try {
        Thread.sleep(500);
      } catch (InterruptedException e) {
        throw new RuntimeException(e);
      }
    } while (Duration.between(start, Instant.now()).getSeconds() < WAIT_TIMEOUT);
  }

  private void waitForTaskProcessing(DeploymentTask task) {
    Instant start = Instant.now();
    List<DeploymentTaskState> terminalStates =
        List.of(
            DeploymentTaskState.FAILED,
            DeploymentTaskState.COMPLETED,
            DeploymentTaskState.CANCELED);
    do {
      List<DeploymentTask> pollTasks =
          deploymentTaskService.getByDeploymentId(task.deploymentId()).getResults();
      boolean tasksRunning =
          pollTasks.stream().anyMatch(pollTask -> !terminalStates.contains(pollTask.state()));
      if (!tasksRunning) {
        return;
      }
      try {
        Thread.sleep(500);
      } catch (InterruptedException e) {
        throw new RuntimeException(e);
      }
    } while (Duration.between(start, Instant.now()).getSeconds() < WAIT_TIMEOUT);
  }

  private void waitForWorkRequestProcessing(RmcId workRequestId) {
    Instant start = Instant.now();
    do {
      Optional<WorkRequest> workRequestOptional = workRequestService.findById(workRequestId);

      if (workRequestOptional.get().status().isTerminal()) {
        return;
      }
      try {
        Thread.sleep(500);
      } catch (InterruptedException e) {
        throw new RuntimeException(e);
      }
    } while (Duration.between(start, Instant.now()).getSeconds() < WAIT_TIMEOUT);
  }

  private void waitForDeploymentProcessing(Deployment deployment) {
    waitForDeploymentEvaluation(deployment);
    DeploymentTask deploymentTask =
        deploymentTaskService.getByDeploymentId(deployment.id()).getResults().get(0);
    waitForTaskProcessing(deploymentTask);
  }

  private void runSecondaryTaskThread(CountDownLatch latch) {
    new Thread(
            () -> {
              try {
                Thread.sleep(5000);
                System.out.println("Task 2 processing completed.");
                latch.countDown();
              } catch (Exception e) {
                e.printStackTrace();
              }
            })
        .start();
  }

  private void verifyWorkRequestStatus(
      Deployment deployment,
      WorkRequest.Status expectedStatus,
      DeploymentTaskState expectedTaskState) {
    PaginatedList<DeploymentTask> deploymentTasks =
        deploymentTaskService.getByDeploymentId(deployment.id());
    Optional<WorkRequest> maybeWorkRequest =
        workRequestService.findById(deploymentTasks.getResults().get(0).workRequestId());

    assertThat(maybeWorkRequest.isPresent()).isTrue();
    WorkRequest updatedWorkRequest =
        workRequestService
            .findById(maybeWorkRequest.get().id())
            .orElseThrow(() -> new AssertionError("WorkRequest not found after processing"));
    assertEquals(expectedStatus, updatedWorkRequest.status());

    assertEquals(expectedTaskState, deploymentTasks.getResults().get(0).state());
  }

  @Test
  @Order(1)
  void testProcessWorkRequestOutsideMaintenanceWindow() throws Exception {
    when(changeManagementService.isChangePermitted(any())).thenReturn(false);
    Deployment deployment = createDeployment(DeploymentType.FIRMWARE, false, null);
    waitForDeploymentProcessing(deployment);
    verifyWorkRequestStatus(deployment, IN_PROGRESS, PENDING);
  }

  @Test
  @Disabled
  void testProcessWorkRequestWithDeployConfigCommand() throws Exception {
    Deployment deployment = createDeployment(DeploymentType.CONFIG, true, null);
    waitForDeploymentProcessing(deployment);
    verifyWorkRequestStatus(deployment, SUCCEEDED, DeploymentTaskState.COMPLETED);
  }

  @Test
  void testCompletedDeploymentButInProgressTask() {
    Deployment deployment = createDeployment(DeploymentType.CONFIG, true, null);
    waitForDeploymentEvaluation(deployment);
    deployment = deploymentService.get(deployment.id()).get();
    deploymentService.completeDeployment(deployment);
    waitForDeploymentProcessing(deployment);
    verifyWorkRequestStatus(deployment, CANCELED, DeploymentTaskState.CANCELED);
  }

  @Test
  void testFailedTaskButInProgressWorkRequest() {
    Deployment deployment = createDeployment(DeploymentType.CONFIG, true, null);
    waitForDeploymentEvaluation(deployment);
    DeploymentTask task =
        deploymentTaskService.getByDeploymentId(deployment.id()).getResults().get(0);
    deployment = deploymentService.get(deployment.id()).get();
    deploymentTaskService.updateState(task, DeploymentTaskState.FAILED);
    waitForWorkRequestProcessing(task.workRequestId());
    verifyWorkRequestStatus(deployment, FAILED, DeploymentTaskState.FAILED);
  }

  @Test
  @Disabled
  void testBmcExceptionDoesNotCauseRollback() throws Exception {
    // Setup
    System.setProperty("test.mode", "false");
    when(deviceAccess.pushReplaceConfig(any(), any(), anyString(), anyBoolean()))
        .thenCallRealMethod();
    CountDownLatch latch = new CountDownLatch(2);
    Deployment deployment = createDeployment(DeploymentType.CONFIG, false, null);
    runEvaluationThread(latch, deployment);
    runSecondaryTaskThread(latch);

    Assertions.assertTrue(
        latch.await(200, TimeUnit.SECONDS), "Deployment task did not complete in time");

    // Validation
    DeploymentTask task =
        deploymentTaskService.getByDeploymentId(deployment.id()).getResults().get(0);
    assertEquals(deployment.maxTaskRetries(), task.attempts());
    verifyWorkRequestStatus(deployment, WorkRequest.Status.FAILED, DeploymentTaskState.FAILED);
  }

  @Test
  void testChangeManagementWindowClosedLeavesTasksInPending() {
    when(changeManagementService.isChangePermitted(any())).thenReturn(false);
    Deployment deployment = createDeployment(DeploymentType.CONFIG, false, null);
    waitForDeploymentProcessing(deployment);
    DeploymentTask task =
        deploymentTaskService.getByDeploymentId(deployment.id()).getResults().get(0);
    assertEquals(PENDING, task.state());
  }

  @Test
  void testChangeManagementWindowClosedButIgnoreFieldSetProcessesTask() {
    when(changeManagementService.isChangePermitted(any())).thenReturn(false);
    Deployment deployment = createDeployment(DeploymentType.CONFIG, true, null);
    assertEquals(Deployment.DeploymentState.CREATED, deployment.state());
    waitForDeploymentProcessing(deployment);
    DeploymentTask task =
        deploymentTaskService.getByDeploymentId(deployment.id()).getResults().get(0);
    assertEquals(COMPLETED, task.state());
  }

  @Test
  @Disabled
  void testFailuresAutomaticallyPauseDeployments() {
    // Super ghetto hack. I need the command to actually execute so I can test auto pause..
    System.clearProperty("test.mode");
    when(deviceAccess.pushReplaceConfig(any(), any(), anyString(), anyBoolean()))
        .thenCallRealMethod();
    Deployment deployment = createDeployment(DeploymentType.CONFIG, true, null);
    waitForDeploymentProcessing(deployment);
    deployment = deploymentService.get(deployment.id()).get();
    assertEquals(Deployment.DeploymentState.PAUSED, deployment.state());
  }

  @Test
  @Disabled
  void testProcessWorkRequestWithRunOperationalCommand() throws Exception {
    validDeploymentTaskId = RmcId.generate(Network.DEPLOYMENT_TASK);
    setupCommand(
        new RunOperationalTask(validDeploymentTaskId, COMMANDS, RmcId.generate(Network.DEVICE)));
    Deployment deployment =
        createDeployment(
            DeploymentType.COMMAND,
            true,
            Deployment$DeploymentParametersBuilder.builder().executionCommand(COMMANDS).build());
    waitForDeploymentProcessing(deployment);
    verifyWorkRequestStatus(deployment, SUCCEEDED, DeploymentTaskState.COMPLETED);
  }

  @Test
  @Disabled
  void testProcessWorkRequestWithUnsupportedCertificatePlatformCommand()
      throws InterruptedException {
    // Create name for device
    String name = UUID.randomUUID().toString();

    // Create test junosDevice
    Device junosDevice =
        deviceService.create(
            new DeviceService.CreateNetworkDevice(
                testFabricId,
                RmcId.generate(RmcIdType.DCMS.DEVICE_MODEL),
                null,
                region.airportCode(),
                name,
                Device.Platform.JUNOS.toString(),
                "4.1.1",
                Device.Role.MLEAF.role(),
                1,
                "dev"),
            region.id(),
            null);
    deviceRepository.update(junosDevice.withIpAddress("127.0.0.1"));

    // Create state for device
    ConfigsMetadata configsMetadata = createConfigMetadataForDevice(junosDevice);
    FirmwareImage firmwareImage = createFirmwareImage();
    createDeviceStateForDevice(junosDevice, configsMetadata, firmwareImage);

    Deployment deployment =
        deploymentService.create(
            new DeploymentService.CreateDeployment(
                DeploymentType.CERTIFICATE,
                region.airportCode(),
                Device.Platform.JUNOS,
                "theRole1",
                "backend",
                25,
                1,
                List.of(junosDevice.id().toString()),
                1,
                null,
                true,
                "deploymentTaskProcessorTest"));

    waitForDeploymentProcessing(deployment);
    verify(deviceAccess, times(0))
        .installCertificateE2e(any(), any(), any(), any(), any(), any(), any());
    verifyWorkRequestStatus(deployment, SUCCEEDED, DeploymentTaskState.COMPLETED);
  }

  @Test
  void testProcessWorkRequestWithInstallExistingCertificate() {
    String name = UUID.randomUUID().toString();
    Device device =
        deviceService.create(
            new DeviceService.CreateNetworkDevice(
                testFabricId,
                RmcId.generate(RmcIdType.DCMS.DEVICE_MODEL),
                null,
                region.airportCode(),
                name,
                Device.Platform.EOS.toString(),
                "Arista-7060X6",
                Device.Role.FSPINE.role(),
                1,
                "dev"),
            region.id(),
            null);
    deviceRepository.update(device.withIpAddress("127.0.0.1"));
    DeviceStateService.CreateDeviceState deviceState =
        new DefaultDeviceStateService.CreateDeviceState(
            device.id(),
            device.networkModelId(),
            DeviceState.LifeCycleState.NEW,
            DeviceState.LifeCycleState.READY,
            DeviceState.ProcessState.BOOTSTRAPPED,
            null,
            RmcId.generate(Network.CONFIGS_METADATA),
            null,
            RmcId.generate(RmcIdType.Network.FIRMWARE_IMAGE),
            "dev");
    deviceStateService.create(deviceState, null);

    Deployment deployment =
        deploymentService.create(
            new DeploymentService.CreateDeployment(
                DeploymentType.BYO_CERTIFICATE,
                region.airportCode(),
                Device.Platform.EOS,
                "fspine",
                "Arista-7060X6",
                25,
                1,
                List.of(device.id().toString()),
                Deployment$DeploymentParametersBuilder.builder()
                    .certificateChainPem("chainPem")
                    .certificatePem("certPem")
                    .rsaKeyName("rsaKey")
                    .build(),
                1,
                null,
                true,
                "deploymentTaskProcessorTest",
                null));

    waitForDeploymentProcessing(deployment);
    verify(deviceAccess, times(1))
        .installCertificate(
            "127.0.0.1",
            "eos",
            "certPem",
            "chainPem",
            name + "-cert",
            name + "-intermediate",
            "rsaKey");
    verifyWorkRequestStatus(deployment, SUCCEEDED, DeploymentTaskState.COMPLETED);
  }

  @Test
  @Disabled("Flaky")
  void testProcessWorkRequestWithByoCertificateException() {
    String name = UUID.randomUUID().toString();
    Device device =
        deviceService.create(
            new DeviceService.CreateNetworkDevice(
                testFabricId,
                RmcId.generate(RmcIdType.DCMS.DEVICE_MODEL),
                null,
                region.airportCode(),
                name,
                Device.Platform.EOS.toString(),
                "Arista-7060X6",
                Device.Role.FSPINE.role(),
                1,
                "dev"),
            region.id(),
            null);
    deviceRepository.update(device.withIpAddress("127.0.0.1"));
    DeviceStateService.CreateDeviceState deviceState =
        new DefaultDeviceStateService.CreateDeviceState(
            device.id(),
            device.networkModelId(),
            DeviceState.LifeCycleState.NEW,
            DeviceState.LifeCycleState.READY,
            DeviceState.ProcessState.BOOTSTRAPPED,
            null,
            RmcId.generate(Network.CONFIGS_METADATA),
            null,
            RmcId.generate(RmcIdType.Network.FIRMWARE_IMAGE),
            "dev");
    deviceStateService.create(deviceState, null);

    Deployment deployment =
        deploymentService.create(
            new DeploymentService.CreateDeployment(
                DeploymentType.BYO_CERTIFICATE,
                region.airportCode(),
                Device.Platform.EOS,
                "fspine",
                "Arista-7060X6",
                25,
                1,
                List.of(device.id().toString()),
                Deployment$DeploymentParametersBuilder.builder()
                    .certificateChainPem("chainPem")
                    .certificatePem("certPem")
                    .rsaKeyName("rsaKey")
                    .build(),
                1,
                null,
                true,
                "deploymentTaskProcessorTest",
                null));

    doThrow(BmcException.class)
        .when(deviceAccess)
        .installCertificate(
            "127.0.0.1",
            "eos",
            "certPem",
            "chainPem",
            name + "-cert",
            name + "-intermediate",
            "rsaKey");
    waitForDeploymentProcessing(deployment);
    verify(deviceAccess, times(1))
        .installCertificate(any(), any(), any(), any(), any(), any(), any());
    verifyWorkRequestStatus(deployment, FAILED, DeploymentTaskState.FAILED);
  }

  @Test
  @Disabled
  void testProcessWorkRequestWithRsaKey() {
    String name = UUID.randomUUID().toString();
    Device device =
        deviceService.create(
            new DeviceService.CreateNetworkDevice(
                testFabricId,
                RmcId.generate(RmcIdType.DCMS.DEVICE_MODEL),
                null,
                region.airportCode(),
                name,
                Device.Platform.EOS.toString(),
                "Arista-7060X6",
                Device.Role.FSPINE.role(),
                1,
                "dev"),
            region.id(),
            null);
    DeviceStateService.CreateDeviceState deviceState =
        new DefaultDeviceStateService.CreateDeviceState(
            device.id(),
            device.networkModelId(),
            DeviceState.LifeCycleState.NEW,
            DeviceState.LifeCycleState.READY,
            DeviceState.ProcessState.BOOTSTRAPPED,
            null,
            RmcId.generate(Network.CONFIGS_METADATA),
            null,
            RmcId.generate(RmcIdType.Network.FIRMWARE_IMAGE),
            "dev");
    deviceStateService.create(deviceState, null);
    deviceRepository.update(device.withIpAddress("127.0.0.1"));
    Deployment deployment =
        deploymentService.create(
            new DeploymentService.CreateDeployment(
                DeploymentType.RSA_KEY,
                region.airportCode(),
                Device.Platform.EOS,
                "fspine",
                "Arista-7060X6",
                25,
                1,
                List.of(device.id().toString()),
                Deployment$DeploymentParametersBuilder.builder().rsaKeyName("rsaKey").build(),
                1,
                null,
                true,
                "deploymentTaskProcessorTest",
                null));

    waitForDeploymentProcessing(deployment);
    verify(deviceAccess, times(1)).createRsaKey("127.0.0.1", "eos", "rsaKey");
    verifyWorkRequestStatus(deployment, SUCCEEDED, DeploymentTaskState.COMPLETED);
  }

  @Test
  void testProcessWorkRequestWithRsaKeyException() {
    String name = UUID.randomUUID().toString();
    Device device =
        deviceService.create(
            new DeviceService.CreateNetworkDevice(
                testFabricId,
                RmcId.generate(RmcIdType.DCMS.DEVICE_MODEL),
                null,
                region.airportCode(),
                name,
                Device.Platform.EOS.toString(),
                "Arista-7060X6",
                Device.Role.FSPINE.role(),
                1,
                "dev"),
            region.id(),
            null);
    DeviceStateService.CreateDeviceState deviceState =
        new DefaultDeviceStateService.CreateDeviceState(
            device.id(),
            device.networkModelId(),
            DeviceState.LifeCycleState.NEW,
            DeviceState.LifeCycleState.READY,
            DeviceState.ProcessState.BOOTSTRAPPED,
            null,
            RmcId.generate(Network.CONFIGS_METADATA),
            null,
            RmcId.generate(RmcIdType.Network.FIRMWARE_IMAGE),
            "dev");
    deviceStateService.create(deviceState, null);
    deviceRepository.update(device.withIpAddress("127.0.0.1"));
    Deployment deployment =
        deploymentService.create(
            new DeploymentService.CreateDeployment(
                DeploymentType.RSA_KEY,
                region.airportCode(),
                Device.Platform.EOS,
                "fspine",
                "Arista-7060X6",
                25,
                1,
                List.of(device.id().toString()),
                Deployment$DeploymentParametersBuilder.builder().rsaKeyName("rsaKey").build(),
                1,
                null,
                true,
                "deploymentTaskProcessorTest",
                null));

    doThrow(BmcException.class).when(deviceAccess).createRsaKey("127.0.0.1", "eos", "rsaKey");
    waitForDeploymentProcessing(deployment);
    verify(deviceAccess, times(1)).createRsaKey("127.0.0.1", "eos", "rsaKey");
    verifyWorkRequestStatus(deployment, FAILED, DeploymentTaskState.FAILED);
  }

  @Test
  void testProcessWorkRequestWithFileCopy() throws MalformedURLException, URISyntaxException {
    String name = UUID.randomUUID().toString();
    Device device =
        deviceService.create(
            new DeviceService.CreateNetworkDevice(
                testFabricId,
                RmcId.generate(RmcIdType.DCMS.DEVICE_MODEL),
                null,
                region.airportCode(),
                name,
                Device.Platform.EOS.toString(),
                "Arista-7060X6",
                Device.Role.FSPINE.role(),
                1,
                "dev"),
            region.id(),
            null);
    DeviceStateService.CreateDeviceState deviceState =
        new DefaultDeviceStateService.CreateDeviceState(
            device.id(),
            device.networkModelId(),
            DeviceState.LifeCycleState.NEW,
            DeviceState.LifeCycleState.READY,
            DeviceState.ProcessState.BOOTSTRAPPED,
            null,
            RmcId.generate(Network.CONFIGS_METADATA),
            null,
            RmcId.generate(RmcIdType.Network.FIRMWARE_IMAGE),
            "dev");
    deviceStateService.create(deviceState, null);
    deviceRepository.update(device.withIpAddress("127.0.0.1"));
    Deployment deployment =
        deploymentService.create(
            new DeploymentService.CreateDeployment(
                DeploymentType.FILE_COPY,
                region.airportCode(),
                Device.Platform.EOS,
                "fspine",
                "Arista-7060X6",
                25,
                1,
                List.of(device.id().toString()),
                Deployment$DeploymentParametersBuilder.builder().firmwareUrl("objectUrl").build(),
                1,
                null,
                true,
                "deploymentTaskProcessorTest",
                null));

    waitForDeploymentProcessing(deployment);
    verify(deviceAccess, times(1)).fileCopy("127.0.0.1", "objectUrl", "eos", "MGMT", false);
    verifyWorkRequestStatus(deployment, SUCCEEDED, DeploymentTaskState.COMPLETED);
  }

  @Test
  void testProcessWorkRequestWithFileCopyAristaMgmtVrf()
      throws MalformedURLException, URISyntaxException {
    String name = UUID.randomUUID().toString();
    Device device =
        deviceService.create(
            new DeviceService.CreateNetworkDevice(
                testFabricId,
                RmcId.generate(RmcIdType.DCMS.DEVICE_MODEL),
                null,
                region.airportCode(),
                name,
                Device.Platform.EOS.toString(),
                "Arista-7060X6",
                Device.Role.FSPINE.role(),
                1,
                "dev"),
            region.id(),
            null);
    DeviceStateService.CreateDeviceState deviceState =
        new DefaultDeviceStateService.CreateDeviceState(
            device.id(),
            device.networkModelId(),
            DeviceState.LifeCycleState.NEW,
            DeviceState.LifeCycleState.READY,
            DeviceState.ProcessState.BOOTSTRAPPED,
            null,
            RmcId.generate(Network.CONFIGS_METADATA),
            null,
            RmcId.generate(RmcIdType.Network.FIRMWARE_IMAGE),
            "dev");
    deviceStateService.create(deviceState, null);
    deviceRepository.update(device.withIpAddress("127.0.0.1"));
    Deployment deployment =
        deploymentService.create(
            new DeploymentService.CreateDeployment(
                DeploymentType.FILE_COPY,
                region.airportCode(),
                Device.Platform.EOS,
                "fspine",
                "Arista-7060X6",
                25,
                1,
                List.of(device.id().toString()),
                Deployment$DeploymentParametersBuilder.builder().firmwareUrl("objectUrl").build(),
                1,
                null,
                true,
                "deploymentTaskProcessorTest",
                null));

    waitForDeploymentProcessing(deployment);
    verify(deviceAccess, times(1)).fileCopy("127.0.0.1", "objectUrl", "eos", "MGMT", false);
    verifyWorkRequestStatus(deployment, SUCCEEDED, DeploymentTaskState.COMPLETED);
  }

  @Test
  void testProcessWorkRequestWithFileCopyNonAristaMgmtVrf()
      throws MalformedURLException, URISyntaxException {
    String name = UUID.randomUUID().toString();
    Device device =
        deviceService.create(
            new DeviceService.CreateNetworkDevice(
                testFabricId,
                RmcId.generate(RmcIdType.DCMS.DEVICE_MODEL),
                null,
                region.airportCode(),
                name,
                Device.Platform.EOS.toString(),
                "Arista-7060X6",
                Device.Role.OOB.role(),
                1,
                "dev"),
            region.id(),
            null);
    DeviceStateService.CreateDeviceState deviceState =
        new DefaultDeviceStateService.CreateDeviceState(
            device.id(),
            device.networkModelId(),
            DeviceState.LifeCycleState.NEW,
            DeviceState.LifeCycleState.READY,
            DeviceState.ProcessState.BOOTSTRAPPED,
            null,
            RmcId.generate(Network.CONFIGS_METADATA),
            null,
            RmcId.generate(RmcIdType.Network.FIRMWARE_IMAGE),
            "dev");
    deviceStateService.create(deviceState, null);
    deviceRepository.update(device.withIpAddress("127.0.0.1"));
    Deployment deployment =
        deploymentService.create(
            new DeploymentService.CreateDeployment(
                DeploymentType.FILE_COPY,
                region.airportCode(),
                Device.Platform.EOS,
                "oob",
                "Arista-7060X6",
                25,
                1,
                List.of(device.id().toString()),
                Deployment$DeploymentParametersBuilder.builder().firmwareUrl("objectUrl").build(),
                1,
                null,
                true,
                "deploymentTaskProcessorTest",
                null));

    waitForDeploymentProcessing(deployment);
    verify(deviceAccess, times(1)).fileCopy("127.0.0.1", "objectUrl", "eos", null, false);
    verifyWorkRequestStatus(deployment, SUCCEEDED, DeploymentTaskState.COMPLETED);
  }

  @Test
  void testProcessWorkRequestWithFileCopyNonAristaVrf()
      throws MalformedURLException, URISyntaxException {
    String name = UUID.randomUUID().toString();
    Device device =
        deviceService.create(
            new DeviceService.CreateNetworkDevice(
                testFabricId,
                RmcId.generate(RmcIdType.DCMS.DEVICE_MODEL),
                null,
                region.airportCode(),
                name,
                Device.Platform.CUMULUS.toString(),
                "deviceModel",
                Device.Role.FSPINE.role(),
                1,
                "dev"),
            region.id(),
            null);
    DeviceStateService.CreateDeviceState deviceState =
        new DefaultDeviceStateService.CreateDeviceState(
            device.id(),
            device.networkModelId(),
            DeviceState.LifeCycleState.NEW,
            DeviceState.LifeCycleState.READY,
            DeviceState.ProcessState.BOOTSTRAPPED,
            null,
            RmcId.generate(Network.CONFIGS_METADATA),
            null,
            RmcId.generate(RmcIdType.Network.FIRMWARE_IMAGE),
            "dev");
    deviceStateService.create(deviceState, null);
    deviceRepository.update(device.withIpAddress("127.0.0.1"));
    Deployment deployment =
        deploymentService.create(
            new DeploymentService.CreateDeployment(
                DeploymentType.FILE_COPY,
                region.airportCode(),
                Device.Platform.CUMULUS,
                "fspine",
                "deviceModel",
                25,
                1,
                List.of(device.id().toString()),
                Deployment$DeploymentParametersBuilder.builder().firmwareUrl("objectUrl").build(),
                1,
                null,
                true,
                "deploymentTaskProcessorTest",
                null));

    waitForDeploymentProcessing(deployment);
    verify(deviceAccess, times(1)).fileCopy("127.0.0.1", "objectUrl", "cumulus", null, false);
    verifyWorkRequestStatus(deployment, SUCCEEDED, DeploymentTaskState.COMPLETED);
  }

  @Test
  void testProcessWorkRequestWithFileCopyException()
      throws MalformedURLException, URISyntaxException {
    String name = UUID.randomUUID().toString();
    Device device =
        deviceService.create(
            new DeviceService.CreateNetworkDevice(
                testFabricId,
                RmcId.generate(RmcIdType.DCMS.DEVICE_MODEL),
                null,
                region.airportCode(),
                name,
                Device.Platform.EOS.toString(),
                "Arista-7060X6",
                Device.Role.FSPINE.role(),
                1,
                "dev"),
            region.id(),
            null);
    DeviceStateService.CreateDeviceState deviceState =
        new DefaultDeviceStateService.CreateDeviceState(
            device.id(),
            device.networkModelId(),
            DeviceState.LifeCycleState.NEW,
            DeviceState.LifeCycleState.READY,
            DeviceState.ProcessState.BOOTSTRAPPED,
            null,
            RmcId.generate(Network.CONFIGS_METADATA),
            null,
            RmcId.generate(RmcIdType.Network.FIRMWARE_IMAGE),
            "dev");
    deviceStateService.create(deviceState, null);
    deviceRepository.update(device.withIpAddress("127.0.0.1"));
    Deployment deployment =
        deploymentService.create(
            new DeploymentService.CreateDeployment(
                DeploymentType.FILE_COPY,
                region.airportCode(),
                Device.Platform.EOS,
                "fspine",
                "Arista-7060X6",
                25,
                1,
                List.of(device.id().toString()),
                Deployment$DeploymentParametersBuilder.builder().firmwareUrl("objectUrl").build(),
                1,
                null,
                true,
                "deploymentTaskProcessorTest",
                null));

    doThrow(BmcException.class)
        .when(deviceAccess)
        .fileCopy("127.0.0.1", "objectUrl", "eos", "MGMT", false);
    waitForDeploymentProcessing(deployment);
    verify(deviceAccess, times(1)).fileCopy("127.0.0.1", "objectUrl", "eos", "MGMT", false);
    verifyWorkRequestStatus(deployment, FAILED, DeploymentTaskState.FAILED);
  }

  @Test
  @Disabled
  void testProcessWorkRequestWithSupportedCertificatePlatformCommand() {
    validDeploymentTaskId = RmcId.generate(Network.DEPLOYMENT_TASK);
    doNothing()
        .when(deviceAccess)
        .installCertificateE2e(any(), any(), any(), any(), any(), any(), any());
    Deployment deployment =
        deploymentService.create(
            new DeploymentService.CreateDeployment(
                DeploymentType.CERTIFICATE,
                region.airportCode(),
                Device.Platform.EOS,
                "theRole1",
                "backend",
                25,
                1,
                List.of(TEST_DEVICE.id().toString()),
                1,
                null,
                true,
                "deploymentTaskProcessorTest"));
    assertEquals(Deployment.DeploymentState.CREATED, deployment.state());
    waitForDeploymentProcessing(deployment);
    Mockito.verify(deviceAccess, times(1))
        .installCertificateE2e(
            anyString(),
            anyString(),
            anyString(),
            anyString(),
            anyString(),
            anyString(),
            anyString());
    verifyWorkRequestStatus(deployment, SUCCEEDED, DeploymentTaskState.COMPLETED);
  }

  @Test
  void testDeviceLifecycleStateUpdates() {
    DeviceState initialDeviceState = deviceStateService.getByDeviceId(TEST_DEVICE.id()).get();

    Assertions.assertNotEquals(
        initialDeviceState.configDeployed(), initialDeviceState.configLatest());

    Deployment deployment = createDeployment(DeploymentType.CONFIG, true, null);
    waitForDeploymentProcessing(deployment);

    verifyWorkRequestStatus(deployment, SUCCEEDED, DeploymentTaskState.COMPLETED);

    initialDeviceState = deviceStateService.getByDeviceId(TEST_DEVICE.id()).get();
    assertEquals(initialDeviceState.configDeployed(), initialDeviceState.configLatest());
  }

  @Test
  void testProcessWorkRequestWithDeployFirmwareCommand() {
    Deployment deployment = createDeployment(DeploymentType.FIRMWARE, true, null);
    waitForDeploymentProcessing(deployment);
    verifyWorkRequestStatus(deployment, SUCCEEDED, DeploymentTaskState.COMPLETED);
  }

  @Test
  @Disabled("flaky")
  void testProcessWorkRequestWithDeployConfigCommandAndNoIp() {
    deviceService
        .listAllDevices()
        .forEach(
            device -> {
              deviceRepository.update(device.withIpAddress(null));
            });

    Deployment deployment = createDeployment(DeploymentType.CONFIG, true, null);
    waitForDeploymentProcessing(deployment);
    verifyWorkRequestStatus(deployment, FAILED, DeploymentTaskState.FAILED);
  }

  @AfterEach
  void cleanup() {
    System.clearProperty("test.mode");
  }
}
