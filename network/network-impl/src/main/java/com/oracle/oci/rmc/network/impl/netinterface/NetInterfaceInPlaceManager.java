package com.oracle.oci.rmc.network.impl.netinterface;

import static com.oracle.oci.rmc.network.api.netinterface.NetInterface.Type.CONSOLE;
import static com.oracle.oci.rmc.network.api.netinterface.NetInterface.Type.L3PORT;

import com.oracle.oci.rmc.model.RmcId;
import com.oracle.oci.rmc.network.api.device.Device;
import com.oracle.oci.rmc.network.api.device.DeviceService;
import com.oracle.oci.rmc.network.api.fabric.Fabric;
import com.oracle.oci.rmc.network.api.fabric.FabricService;
import com.oracle.oci.rmc.network.api.host.HostRecordService;
import com.oracle.oci.rmc.network.api.location.DeviceLocation;
import com.oracle.oci.rmc.network.api.location.DeviceLocationService;
import com.oracle.oci.rmc.network.api.netinterface.NetInterface;
import com.oracle.oci.rmc.network.api.netinterface.NetInterfaceService;
import com.oracle.oci.rmc.network.api.netinterface.rule.NetInterfaceRule;
import com.oracle.oci.rmc.network.api.port.Port;
import com.oracle.oci.rmc.network.api.port.PortService;
import com.oracle.oci.rmc.network.impl.ipam.address.PrefixManager;
import jakarta.inject.Singleton;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public class NetInterfaceInPlaceManager {

  private final NetInterfaceService netInterfaceService;
  private final HostRecordService hostRecordService;
  private final PortService portService;
  private final DeviceService deviceService;
  private final FabricService fabricService;
  private final NetInterfaceManager netInterfaceManager;
  private final DeviceLocationService deviceLocationService;
  private static final Logger LOG = LoggerFactory.getLogger(NetInterfaceInPlaceManager.class);
  private final PrefixManager prefixManager;

  public NetInterfaceInPlaceManager(
      NetInterfaceService netInterfaceService,
      PortService portService,
      HostRecordService hostRecordService,
      DeviceService deviceService,
      FabricService fabricService,
      NetInterfaceManager netInterfaceManager,
      DeviceLocationService deviceLocationService,
      PrefixManager prefixManager) {
    this.netInterfaceService = netInterfaceService;
    this.portService = portService;
    this.hostRecordService = hostRecordService;
    this.deviceService = deviceService;
    this.fabricService = fabricService;
    this.deviceLocationService = deviceLocationService;
    this.netInterfaceManager = netInterfaceManager;
    this.prefixManager = prefixManager;
  }

  private void applyL3PortRuleOnModel(
      NetInterfaceRule netInterfaceRule,
      Fabric fabric,
      Device device,
      Device destinationDevice,
      Port sourcePort,
      Port destinationPort,
      List<NetInterface> interfaceList,
      List<HostRecordService.CreateHostRecord> hostRecordList,
      List<Port> portList,
      Map<RmcId, DeviceLocation> deviceLocationMap) {
    Map<RmcId, Map<NetInterface, HostRecordService.CreateHostRecord>> recordMap =
        netInterfaceManager.createL3PortInterfaces(
            netInterfaceRule,
            device,
            sourcePort,
            fabric,
            destinationDevice,
            destinationPort,
            deviceLocationMap,
            Optional.empty(),
            false);
    interfaceList.addAll(recordMap.get(device.id()).keySet());
    hostRecordList.addAll(recordMap.get(device.id()).values());
    interfaceList.addAll(recordMap.get(destinationDevice.id()).keySet());
    hostRecordList.addAll(recordMap.get(destinationDevice.id()).values());
    NetInterface sourceInterface = recordMap.get(device.id()).keySet().iterator().next();
    NetInterface destinationInterface =
        recordMap.get(destinationDevice.id()).keySet().iterator().next();
    portList.add(destinationPort.withParentInterfaceId(destinationInterface.id()));
    portList.add(sourcePort.withParentInterfaceId(sourceInterface.id()));
  }

  private void applyConsoleRuleOnModel(
      NetInterfaceRule netInterfaceRule,
      Device device,
      Device destinationDevice,
      Port sourcePort,
      Port destinationPort,
      List<NetInterface> interfaceList,
      List<HostRecordService.CreateHostRecord> hostRecordList,
      List<Port> portList,
      Map<RmcId, DeviceLocation> deviceLocationMap) {
    Optional<Device> mtorDeviceOptional =
        portService
            .findPortsByDeviceNameAndNetworkModelId(device.name(), device.networkModelId())
            .stream()
            .filter(port -> port.peerPortId() != null)
            .map(port -> portService.findById(port.peerPortId()).orElseThrow())
            .map(peerPort -> deviceService.findById(peerPort.deviceId()).orElseThrow())
            .filter(foundDevice -> foundDevice.role().equals(Device.Role.MTOR))
            .findFirst();
    if (mtorDeviceOptional.isEmpty()) {
      LOG.info(
          "mtor subnet could not be found for CONSOLE connection "
              + device.name()
              + ":"
              + sourcePort.name()
              + "<->"
              + destinationDevice.name()
              + ":"
              + destinationPort.name());
      return;
    }
    Map<RmcId, Map<NetInterface, HostRecordService.CreateHostRecord>> recordMapConsole =
        netInterfaceManager.createConsoleInterfaces(
            netInterfaceRule,
            mtorDeviceOptional.get(),
            device,
            sourcePort,
            destinationDevice,
            destinationPort,
            deviceLocationMap);
    interfaceList.addAll(recordMapConsole.get(device.id()).keySet());
    hostRecordList.addAll(recordMapConsole.get(device.id()).values());
    NetInterface sourceInterface = recordMapConsole.get(device.id()).keySet().iterator().next();
    portList.add(sourcePort.withParentInterfaceId(sourceInterface.id()));
  }

  public void applyRuleOnModel(NetInterfaceRule netInterfaceRule) {
    String role = netInterfaceRule.templateSelector().sourceRole();
    List<Device> devices =
        deviceService.listAllDevicesByRole(role, netInterfaceRule.networkModelId());
    Map<RmcId, DeviceLocation> deviceLocationMap =
        deviceLocationService.findDeviceLocationByIdInList(
            devices.stream().map(Device::dcmsDeviceId).toList());
    List<NetInterface> interfaceList = new ArrayList<>();
    List<HostRecordService.CreateHostRecord> hostRecordList = new ArrayList<>();
    List<Port> updatedPorts = new ArrayList<>();
    for (Device device : devices) {
      Fabric fabric = fabricService.findById(device.fabricId()).orElseThrow();
      List<Port> sourcePorts =
          device.ports().stream()
              .filter(
                  p ->
                      netInterfaceRule.templateSelector().fabricType().equals(fabric.type())
                          && p.portGroup() != null
                          && p.portGroup().equals(netInterfaceRule.templateSelector().portGroup()))
              .toList();
      for (Port sourcePort : sourcePorts) {
        if (sourcePort.peerPortId() == null || sourcePort.parentInterfaceId() != null) {
          continue;
        }
        Port destinationPort = portService.findById(sourcePort.peerPortId()).orElseThrow();
        Device destinationDevice = deviceService.findById(destinationPort.deviceId()).orElseThrow();
        if (netInterfaceRule.interfaceType() == L3PORT) {
          applyL3PortRuleOnModel(
              netInterfaceRule,
              fabric,
              device,
              destinationDevice,
              sourcePort,
              destinationPort,
              interfaceList,
              hostRecordList,
              updatedPorts,
              deviceLocationMap);
        } else if (netInterfaceRule.interfaceType() == CONSOLE) {
          applyConsoleRuleOnModel(
              netInterfaceRule,
              device,
              destinationDevice,
              sourcePort,
              destinationPort,
              interfaceList,
              hostRecordList,
              updatedPorts,
              deviceLocationMap);
        }
      }
    }
    LOG.info("Creating all interfaces. NetInterfaces.size: {}", interfaceList.size());
    netInterfaceService.saveAll(interfaceList);
    LOG.info("Creating all host records. HostRecords.size: {}", hostRecordList.size());
    hostRecordService.createAll(hostRecordList, null);
    LOG.info("Updating all ports. Ports.size: {}", updatedPorts.size());
    portService.batchUpdatePorts(updatedPorts);
  }
}
