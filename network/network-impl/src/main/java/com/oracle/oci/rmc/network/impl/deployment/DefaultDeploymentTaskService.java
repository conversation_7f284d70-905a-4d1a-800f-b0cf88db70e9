package com.oracle.oci.rmc.network.impl.deployment;

import com.oracle.oci.rmc.model.MdcCloseables;
import com.oracle.oci.rmc.model.RmcId;
import com.oracle.oci.rmc.model.RmcIdType;
import com.oracle.oci.rmc.network.api.NetworkMetrics;
import com.oracle.oci.rmc.network.api.NetworkMetrics.Dimension;
import com.oracle.oci.rmc.network.api.config.ConfigsMetadata;
import com.oracle.oci.rmc.network.api.deployment.Deployment;
import com.oracle.oci.rmc.network.api.deployment.DeploymentTask;
import com.oracle.oci.rmc.network.api.deployment.DeploymentTask.DeploymentTaskState;
import com.oracle.oci.rmc.network.api.deployment.DeploymentTaskBuilder;
import com.oracle.oci.rmc.network.api.deployment.DeploymentTaskService;
import com.oracle.oci.rmc.network.api.deployment.ExecutableTask;
import com.oracle.oci.rmc.network.api.deployment.tasks.DeployByoCertificateTask;
import com.oracle.oci.rmc.network.api.deployment.tasks.DeployCertificateTask;
import com.oracle.oci.rmc.network.api.deployment.tasks.DeployConfigTask;
import com.oracle.oci.rmc.network.api.deployment.tasks.DeployFileCopyTask;
import com.oracle.oci.rmc.network.api.deployment.tasks.DeployFirmwareTask;
import com.oracle.oci.rmc.network.api.deployment.tasks.DeployFirmwareUpgradeInitiateTask;
import com.oracle.oci.rmc.network.api.deployment.tasks.DeployRsaKeyTask;
import com.oracle.oci.rmc.network.api.deployment.tasks.RunOperationalTask;
import com.oracle.oci.rmc.network.api.device.Device;
import com.oracle.oci.rmc.network.api.device.DeviceService;
import com.oracle.oci.rmc.network.api.exceptions.MissingDeviceException;
import com.oracle.oci.rmc.network.api.exceptions.MissingDeviceStateException;
import com.oracle.oci.rmc.network.api.exceptions.MissingFirmwareImageException;
import com.oracle.oci.rmc.network.api.firmware.FirmwareImage;
import com.oracle.oci.rmc.network.api.firmware.FirmwareImageService;
import com.oracle.oci.rmc.network.api.state.DeviceState;
import com.oracle.oci.rmc.network.api.state.DeviceStateService;
import com.oracle.oci.rmc.network.impl.config.ConfigsMetadataRepository;
import com.oracle.oci.rmc.telemetry.Telemetry;
import com.oracle.oci.rmc.workrequest.api.WorkRequest;
import com.oracle.oci.rmc.workrequest.api.WorkRequestService;
import com.oracle.oci.sfw.micronaut.http.pagination.PageQuery;
import com.oracle.oci.sfw.micronaut.http.pagination.PaginatedList;
import com.oracle.oci.sfw.micronaut.http.pagination.TokenPageQuery;
import com.oracle.pic.sfw.dal.SerializedPaginationToken;
import com.oracle.pic.telemetry.commons.metrics.Metrics;
import com.oracle.pic.telemetry.commons.metrics.model.MetricName;
import io.micronaut.core.util.CollectionUtils;
import io.micronaut.transaction.annotation.Transactional;
import jakarta.inject.Singleton;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
@SuppressWarnings({
  "PMD.UnusedLocalVariable",
  "PMD.AvoidCatchingGenericException",
  "PMD.CouplingBetweenObjects",
  "PMD.TooManyMethods"
})
public class DefaultDeploymentTaskService implements DeploymentTaskService {
  private static final Logger LOG = LoggerFactory.getLogger(DefaultDeploymentTaskService.class);
  private static final MetricName SCOPE =
      MetricName.of("Network.Deployment.Service.DeploymentTask");
  private final DeploymentTaskRepository deploymentTaskRepository;
  private final WorkRequestService workRequestService;
  private final DeviceStateService deviceStateService;
  private final FirmwareImageService firmwareImageService;
  private final ConfigsMetadataRepository configsMetadataRepository;
  private final DeviceService deviceService;

  private static final String METRIC_DEVICE_MISSING = "DeviceMissing";
  private static final String METRIC_DEVICE_STATE_MISSING = "DeviceStateMissing";
  public static final String ERROR_DEVICE_STATE_MISSING = "Device state missing";
  public static final String ERROR_DEVICE_MISSING = "Device missing";
  public static final String ERROR_FIRMWARE_LATEST_MISSING =
      "Device firmwareLatest is null or absent";
  public static final String ERROR_NO_COMMANDS = "No commands supplied for deployment task";

  public DefaultDeploymentTaskService(
      DeploymentTaskRepository deploymentTaskRepository,
      WorkRequestService workRequestService,
      DeviceStateService deviceStateService,
      FirmwareImageService firmwareImageService,
      ConfigsMetadataRepository configsMetadataRepository,
      DeviceService deviceService) {
    this.deploymentTaskRepository = deploymentTaskRepository;
    this.workRequestService = workRequestService;
    this.deviceStateService = deviceStateService;
    this.firmwareImageService = firmwareImageService;
    this.configsMetadataRepository = configsMetadataRepository;
    this.deviceService = deviceService;
  }

  @Override
  public Optional<DeploymentTask> get(RmcId id) {
    try (var mdc = new MdcCloseables().put(id)) {
      LOG.info("Fetching deployment task. deploymentTaskId: {}", id);
      return deploymentTaskRepository.findById(id);
    } catch (Exception e) {
      LOG.error("Error while fetching deployment task.", e);
      throw new IllegalStateException("Failed to fetch deployment task.", e);
    }
  }

  @Override
  public Optional<DeploymentTask> getByDeploymentIdAndStateEquals(
      RmcId deploymentId, DeploymentTaskState deploymentTaskState) {
    return deploymentTaskRepository.findFirstByDeploymentIdAndStateEquals(
        deploymentId, deploymentTaskState);
  }

  @Override
  public PaginatedList<DeploymentTask> getByDeploymentId(
      TokenPageQuery pageQuery, RmcId deploymentId) {
    try (var mdc = new MdcCloseables().put(deploymentId)) {
      LOG.info("Fetching paginated deployment tasks for deploymentId: {}", deploymentId);
      return deploymentTaskRepository.findAllByDeploymentId(deploymentId, pageQuery);
    }
  }

  @Override
  public PaginatedList<DeploymentTask> getByDeploymentId(RmcId deploymentId) {
    try (var mdc = new MdcCloseables().put(deploymentId)) {
      LOG.info("Fetching deployment tasks for deploymentId: {}", deploymentId);
      var pageToken = PageQuery.fromEmptyToken(100);
      return deploymentTaskRepository.findAllByDeploymentId(deploymentId, pageToken);
    }
  }

  @Override
  public List<DeploymentTask> getAllByDeploymentId(RmcId deploymentId) {
    List<DeploymentTask> deploymentTasks = new ArrayList<>();
    PaginatedList<DeploymentTask> paginatedList;
    SerializedPaginationToken pageToken = null;

    LOG.info("Paginating deployment tasks for deploymentId: {}", deploymentId);
    do {
      TokenPageQuery pageQuery =
          pageToken == null ? PageQuery.fromEmptyToken(100) : PageQuery.fromToken(pageToken, 100);
      paginatedList = getByDeploymentId(pageQuery, deploymentId);
      if (CollectionUtils.isNotEmpty(paginatedList.getResults())) {
        deploymentTasks.addAll(paginatedList.getResults());
      }
      pageToken = paginatedList.getNextPageToken();
    } while (pageToken != null);

    return deploymentTasks;
  }

  @Override
  @Transactional
  public DeploymentTask updateState(DeploymentTask deploymentTask, DeploymentTaskState newState) {
    try (var mdc = new MdcCloseables().put(deploymentTask.id())) {
      LOG.info(
          "Updating deployment task state. newState: {}, currentState: {}",
          newState,
          deploymentTask.state());
      DeploymentTask updated = deploymentTask.withState(newState);

      return deploymentTaskRepository.update(updated);
    } catch (Exception e) {
      LOG.error("Error while updating deployment task state", e);
      throw new IllegalStateException("Failed to update deployment task state", e);
    }
  }

  @Transactional
  @Override
  public DeploymentTask incrementTaskAttempts(DeploymentTask deploymentTask) {
    deploymentTaskRepository.incrementAttempts(deploymentTask.id());
    return deploymentTaskRepository
        .findById(deploymentTask.id())
        .orElseThrow(
            () ->
                new IllegalStateException("Deployment task " + deploymentTask.id() + " not found"));
  }

  @Override
  @Transactional
  public DeploymentTask create(CreateDeploymentTask task) {
    MetricName metricName =
        SCOPE
            .child("CreateDeploymentTask")
            .withDimension(NetworkMetrics.Dimension.DEVICE_ID, task.deviceId().toString());
    try (var mdcDeployment = new MdcCloseables().put(task.deploymentId()).put(task.deviceId());
        var telemetry = new Telemetry(metricName)) {

      LOG.info("Creating deployment task. deploymentType: {}", task.deploymentType());
      DeploymentTask newDeploymentTask =
          switch (task.deploymentType()) {
            case CONFIG -> getDeployConfigTask(task);
            case FIRMWARE -> getDeployFirmwareTask(task);
            case COMMAND -> getRunOperationalTask(task);
            case CERTIFICATE -> getDeployCertificateTask(task);
            case RSA_KEY -> getRsaKeyDeploymentTask(task);
            case BYO_CERTIFICATE -> getByoCertDeploymentTask(task);
            case FIRMWARE_UPGRADE_INITIATE -> getDeployFirmwareUpgradeInitiateTask(task);
            case FILE_COPY -> getRunCopyFirmwareFileTask(task);
          };

      LOG.info(
          "Deployment task created successfully. deploymentTaskId: {}, workRequestId: {}",
          newDeploymentTask.id(),
          newDeploymentTask.workRequestId());
      var res = deploymentTaskRepository.save(newDeploymentTask);
      telemetry.recordSuccess();
      return res;
    }
  }

  @Override
  @Transactional
  public void deleteAllInRegion(RmcId networkModelId) {
    deploymentTaskRepository.deleteByNetworkModelId(networkModelId);
  }

  /**
   * Returns an instance of an ExecutableTask which can be submitted to the WorkRequest service
   *
   * @param task - The deployment task associated with the executable command being created; used
   *     for fetching device lifecycle state
   * @return RunOperationalCommand
   */
  private DeploymentTask getRunOperationalTask(CreateDeploymentTask task) {
    Metrics.emit(
        SCOPE
            .child("CreateCommandDeploymentTask")
            .withDimension(Dimension.DEPLOYMENT_ID, task.deploymentId().toString())
            .withDimension(Dimension.DEPLOYMENT_TYPE, task.deploymentType().toString()),
        1);

    // Create a deployment task id for this task
    RmcId newDeploymentTaskId = RmcId.generate(RmcIdType.Network.DEPLOYMENT_TASK);
    // Setup
    DeploymentTaskState taskState = DeploymentTaskState.PENDING;
    List<String> executionCommands = task.deploymentParameters().executionCommand();
    String comments = "";

    if (executionCommands == null || executionCommands.isEmpty()) {
      Metrics.emit(
          SCOPE
              .child("MissingCommandsException")
              .withDimension(Dimension.DEPLOYMENT_ID, task.deploymentId().toString())
              .withDimension(Dimension.DEPLOYMENT_TYPE, task.deploymentType().toString()),
          1);
      taskState = DeploymentTaskState.FAILED_CREATE;
      comments = ERROR_NO_COMMANDS;
    }

    // Create the deployment command
    RunOperationalTask command =
        new RunOperationalTask(newDeploymentTaskId, executionCommands, task.deviceId());

    return createDeploymentTaskAndWorkRequest(task, command, taskState, comments);
  }

  /**
   * Returns an instance of an ExecutableTask which can be submitted to the WorkRequest service
   *
   * @param task - The deployment task associated with the executable command being created; used
   *     for fetching device lifecycle state
   * @return DeployConfigCommand
   */
  private DeploymentTask getDeployConfigTask(CreateDeploymentTask task) {
    Metrics.emit(
        SCOPE
            .child("CreateConfigDeploymentTask")
            .withDimension(Dimension.DEPLOYMENT_ID, task.deploymentId().toString())
            .withDimension(Dimension.DEPLOYMENT_TYPE, task.deploymentType().toString()),
        1);

    // Create a deployment task id for this task
    RmcId newDeploymentTaskId = RmcId.generate(RmcIdType.Network.DEPLOYMENT_TASK);

    // Setup
    RmcId artifactId = null;
    String comments = "";
    String objectUrl = null;
    DeploymentTaskState taskState = DeploymentTaskState.PENDING;

    try {
      DeviceState deviceState = getDeviceState(task);
      ConfigsMetadata configsMetadata =
          configsMetadataRepository
              .findById(deviceState.configLatest())
              .orElseThrow(
                  () ->
                      new MissingDeviceStateException(
                          "Unable to find config metadata entry for id: "
                              + deviceState.configLatest()));
      artifactId = deviceState.configLatest();
      objectUrl = configsMetadata.objectUrl();
    } catch (MissingDeviceStateException | IllegalArgumentException e) {
      Metrics.emit(
          SCOPE
              .child(METRIC_DEVICE_STATE_MISSING)
              .withDimension(Dimension.DEPLOYMENT_ID, task.deploymentId().toString())
              .withDimension(Dimension.DEPLOYMENT_TYPE, task.deploymentType().toString()),
          1);
      LOG.error("Unable to create config task; Missing device state for {}", task.deviceId());
      comments = ERROR_DEVICE_STATE_MISSING;
      taskState = DeploymentTaskState.FAILED_CREATE;
    }

    // Create the deployment command
    DeployConfigTask command =
        new DeployConfigTask(newDeploymentTaskId, objectUrl, artifactId, task.deviceId());

    return createDeploymentTaskAndWorkRequest(task, command, taskState, comments);
  }

  private DeploymentTask createDeploymentTaskAndWorkRequest(
      CreateDeploymentTask task,
      ExecutableTask command,
      DeploymentTaskState taskState,
      String comments) {
    WorkRequest.Status workRequestStatus =
        taskState == DeploymentTaskState.FAILED_CREATE
            ? WorkRequest.Status.CANCELED
            : WorkRequest.Status.ACCEPTED;
    WorkRequest workRequest =
        workRequestService.createWorkRequestWithStatus(
            new WorkRequestService.CreateWorkRequest((WorkRequest.Command) command),
            workRequestStatus);

    return DeploymentTaskBuilder.builder()
        .id(command.getDeploymentTaskId())
        .networkModelId(task.networkModelId())
        .deploymentId(task.deploymentId())
        .deviceId(task.deviceId())
        .state(taskState)
        .workRequestId(workRequest.id())
        .attempts(0)
        .comments(comments)
        .build();
  }

  /**
   * Returns an instance of an ExecutableTask which can be submitted to the WorkRequest service
   *
   * @param task - The deployment task associated with the executable command being created; used
   *     for fetching device lifecycle state
   * @return DeployFirmwareCommand
   */
  private DeploymentTask getDeployFirmwareTask(CreateDeploymentTask task) {
    Metrics.emit(
        SCOPE
            .child("CreateFirmwareDeploymentTask")
            .withDimension(Dimension.DEPLOYMENT_ID, task.deploymentId().toString())
            .withDimension(Dimension.DEPLOYMENT_TYPE, task.deploymentType().toString()),
        1);

    // Create a deployment task id for this task
    RmcId newDeploymentTaskId = RmcId.generate(RmcIdType.Network.DEPLOYMENT_TASK);

    // Setup
    RmcId artifactId = null;
    String comments = "";
    DeploymentTaskState taskState = DeploymentTaskState.PENDING;
    String firmwareUrl = null;
    String firmwareFilename = null;
    String firmwareVersion = null;

    try {
      LOG.info("Fetching device state..");
      DeviceState deviceState = getDeviceState(task);
      if (deviceState.firmwareLatest() == null) {
        LOG.error("firmwareLatest is null for device: {}", task.deviceId());
        taskState = DeploymentTaskState.FAILED_CREATE;
        comments = ERROR_FIRMWARE_LATEST_MISSING;
      } else {
        LOG.info("Fetching current firmware image");
        FirmwareImage firmwareImage =
            firmwareImageService
                .get(deviceState.firmwareLatest())
                .orElseThrow(
                    () ->
                        new MissingFirmwareImageException(
                            "Unable to find firmware image entry for id: "
                                + deviceState.firmwareLatest()));
        artifactId = deviceState.firmwareLatest();
        firmwareUrl = firmwareImage.firmwareUrl();
        firmwareFilename = firmwareImage.fileName();
        firmwareVersion = firmwareImage.firmwareVersion();
      }
    } catch (MissingDeviceStateException | IllegalArgumentException e) {
      Metrics.emit(
          SCOPE
              .child(METRIC_DEVICE_STATE_MISSING)
              .withDimension(Dimension.DEPLOYMENT_ID, task.deploymentId().toString())
              .withDimension(Dimension.DEPLOYMENT_TYPE, task.deploymentType().toString()),
          1);
      LOG.error(
          "Unable to create firmware task; Missing device state for device: {}", task.deviceId());
      taskState = DeploymentTaskState.FAILED_CREATE;
      comments = ERROR_DEVICE_STATE_MISSING;
    } catch (MissingFirmwareImageException e) {
      Metrics.emit(
          SCOPE
              .child("FirmwareImageMissing")
              .withDimension(Dimension.DEPLOYMENT_ID, task.deploymentId().toString())
              .withDimension(Dimension.DEPLOYMENT_TYPE, task.deploymentType().toString()),
          1);
      LOG.error(
          "Failed to retrieve latest firmware image metadata for device: {}", task.deviceId());
      taskState = DeploymentTaskState.FAILED_CREATE;
      comments = ERROR_FIRMWARE_LATEST_MISSING;
    }

    Device device = deviceService.findById(task.deviceId()).orElseThrow();

    DeployFirmwareTask command =
        new DeployFirmwareTask(
            newDeploymentTaskId,
            firmwareUrl,
            firmwareFilename,
            firmwareVersion,
            artifactId,
            task.deviceId(),
            device.role());

    return createDeploymentTaskAndWorkRequest(task, command, taskState, comments);
  }

  /**
   * Returns an instance of an ExecutableTask which can be submitted to the WorkRequest service
   *
   * @param task - The deployment task associated with the executable command being created; used
   *     for fetching device lifecycle state
   * @return DeployFirmwareInititateCommand
   */
  private DeploymentTask getDeployFirmwareUpgradeInitiateTask(CreateDeploymentTask task) {
    Metrics.emit(
        SCOPE
            .child("CreateFirmwareUpgradeInitiateDeploymentTask")
            .withDimension(Dimension.DEPLOYMENT_ID, task.deploymentId().toString())
            .withDimension(Dimension.DEPLOYMENT_TYPE, task.deploymentType().toString()),
        1);

    // Create a deployment task id for this task
    RmcId newDeploymentTaskId = RmcId.generate(RmcIdType.Network.DEPLOYMENT_TASK);

    DeployFirmwareUpgradeInitiateTask command =
        new DeployFirmwareUpgradeInitiateTask(
            newDeploymentTaskId, task.deploymentParameters().firmwareUrl(), task.deviceId());

    return createDeploymentTaskAndWorkRequest(task, command, DeploymentTaskState.PENDING, null);
  }

  private DeploymentTask getRunCopyFirmwareFileTask(CreateDeploymentTask task) {
    Metrics.emit("Network.Deployment.createCopyFirmwareFileDeploymentTask", 1);

    Device device = deviceService.findById(task.deviceId()).orElseThrow();

    // Create a deployment task id for this task
    RmcId newDeploymentTaskId = RmcId.generate(RmcIdType.Network.DEPLOYMENT_TASK);

    ExecutableTask command =
        new DeployFileCopyTask(
            newDeploymentTaskId,
            task.deploymentParameters().firmwareUrl(),
            task.deviceId(),
            device.role());

    return createDeploymentTaskAndWorkRequest(task, command, DeploymentTaskState.PENDING, null);
  }

  /**
   * Returns an instance of an ExecutableTask which can be submitted to the WorkRequest service
   *
   * @param task - The deployment task associated with the executable command being created; used
   *     for fetching device lifecycle state
   * @return DeployCertificateTask
   */
  private DeploymentTask getDeployCertificateTask(CreateDeploymentTask task) {
    Metrics.emit("Network.Deployment.createCertsDeploymentTask", 1);

    // Create a deployment task id for this task
    RmcId newDeploymentTaskId = RmcId.generate(RmcIdType.Network.DEPLOYMENT_TASK);

    // Setup
    String deviceName = null;
    String comments = "";
    DeploymentTaskState taskState = DeploymentTaskState.PENDING;

    try {
      Device device =
          deviceService
              .findById(task.deviceId())
              .orElseThrow(
                  () ->
                      new MissingDeviceStateException(
                          String.format("Device: %s not found", task.deviceId())));
      deviceName = device.name();
    } catch (MissingDeviceStateException e) {
      Metrics.emit(
          SCOPE
              .child("DeviceStateMissing")
              .withDimension(Dimension.DEPLOYMENT_ID, task.deploymentId().toString())
              .withDimension(Dimension.DEPLOYMENT_TYPE, task.deploymentType().toString()),
          1);
      LOG.error("Unable to create cert task; Missing device state for: {}", task.deviceId());
      taskState = DeploymentTaskState.FAILED_CREATE;
      comments = ERROR_DEVICE_STATE_MISSING;
    }

    LOG.info(
        "Creating DeployCertificateTask for device: {}, deploymentTaskId: {}",
        task.deviceId(),
        newDeploymentTaskId);

    // Create the deploy command
    DeployCertificateTask command =
        new DeployCertificateTask(
            newDeploymentTaskId,
            deviceName,
            deviceName,
            String.format("%s-cert", deviceName),
            String.format("%s-intermediate", deviceName),
            String.format("%s-key", deviceName),
            task.deviceId());

    return createDeploymentTaskAndWorkRequest(task, command, taskState, comments);
  }

  /**
   * Returns an instance of an ExecutableTask which can be submitted to the WorkRequest service
   *
   * @param task - The deployment task associated with the executable command being created; used
   *     for fetching device lifecycle state
   * @return DeployByoCertificateCommand
   */
  private DeploymentTask getByoCertDeploymentTask(CreateDeploymentTask task) {

    // Setup
    String deviceName = null;
    String comments = "";
    DeploymentTaskState taskState = DeploymentTaskState.PENDING;

    try {
      Device device =
          deviceService
              .findById(task.deviceId())
              .orElseThrow(
                  () ->
                      new MissingDeviceException(
                          String.format("Device: %s not found", task.deviceId())));
      deviceName = device.name();
    } catch (MissingDeviceException e) {
      Metrics.emit(
          SCOPE
              .child(METRIC_DEVICE_MISSING)
              .withDimension(Dimension.DEPLOYMENT_ID, task.deploymentId().toString())
              .withDimension(Dimension.DEPLOYMENT_TYPE, task.deploymentType().toString()),
          1);
      LOG.error("Unable to create cert task; Missing device for: {}", task.deviceId());
      taskState = DeploymentTaskState.FAILED_CREATE;
      comments = ERROR_DEVICE_MISSING;
    }

    // Create a deployment task id for this task
    RmcId newDeploymentTaskId = RmcId.generate(RmcIdType.Network.DEPLOYMENT_TASK);
    LOG.info(
        "Creating deployment command for device: {}, deploymentTaskId: {}",
        task.deviceId(),
        newDeploymentTaskId);

    Metrics.emit("Network.Deployment.createExistingCertsDeploymentTask", 1);
    Deployment.DeploymentParameters params = task.deploymentParameters();
    ExecutableTask command =
        new DeployByoCertificateTask(
            newDeploymentTaskId,
            params.certificatePem(),
            params.certificateChainPem(),
            params.rsaKeyName(),
            String.format("%s-cert", deviceName),
            String.format("%s-intermediate", deviceName),
            task.deviceId());

    return createDeploymentTaskAndWorkRequest(task, command, taskState, comments);
  }

  /**
   * Returns an instance of an ExecutableTask which can be submitted to the WorkRequest service
   *
   * @param task - The deployment task associated with the executable command being created; used
   *     for fetching device lifecycle state
   * @return DeployRsaKeyCommand
   */
  private DeploymentTask getRsaKeyDeploymentTask(CreateDeploymentTask task) {
    Metrics.emit("Network.Deployment.createRsaKeyDeploymentTask", 1);

    // Create a deployment task id for this task
    RmcId newDeploymentTaskId = RmcId.generate(RmcIdType.Network.DEPLOYMENT_TASK);

    ExecutableTask command =
        new DeployRsaKeyTask(
            newDeploymentTaskId, task.deploymentParameters().rsaKeyName(), task.deviceId());

    return createDeploymentTaskAndWorkRequest(task, command, DeploymentTaskState.PENDING, null);
  }

  /**
   * Returns the device lifecycle state of the device associated with the supplied deployment task
   *
   * @param task - The deployment task associated with the the device you want lifecycle state from
   * @return DeviceState
   */
  private DeviceState getDeviceState(CreateDeploymentTask task) throws MissingDeviceStateException {
    return deviceStateService
        .getByDeviceId(task.deviceId())
        .orElseThrow(
            () ->
                new MissingDeviceStateException(
                    "Missing device state for device id: " + task.deviceId()));
  }
}
