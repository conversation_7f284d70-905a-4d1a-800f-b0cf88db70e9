package com.oracle.oci.rmc.catalog.impl.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.oracle.oci.rmc.auditevent.dal.AuditEvent;
import com.oracle.oci.rmc.auditevent.model.AuditEventContext;
import com.oracle.oci.rmc.auditevent.model.AuditEventType;
import com.oracle.oci.rmc.auditevent.model.EventStatus;
import com.oracle.oci.rmc.auditevent.service.AuditEventContextHolder;
import com.oracle.oci.rmc.auditevent.service.AuditEventService;
import com.oracle.oci.rmc.auditevent.util.InventoryAuditEventConstants;
import com.oracle.oci.rmc.catalog.api.model.entity.PartCategory;
import com.oracle.oci.rmc.model.RmcId;
import com.oracle.oci.rmc.model.RmcIdType;
import com.oracle.oci.sfw.micronaut.http.pagination.PageQuery;
import com.oracle.oci.sfw.micronaut.http.pagination.PaginatedList;
import com.oracle.oci.sfw.micronaut.http.pagination.TokenPageQuery;
import com.oracle.oci.sfw.micronaut.http.tagging.TaggingUtils;
import com.oracle.pic.commons.exceptions.server.RenderableException;
import io.micronaut.context.annotation.Requires;
import io.micronaut.data.exceptions.DataAccessException;
import io.micronaut.test.annotation.TransactionMode;
import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

@MicronautTest(transactionMode = TransactionMode.SINGLE_TRANSACTION)
@Requires(property = "database.type", value = "ORACLE")
public class PartCategoryServiceTest {
  public static final String TEST_USER = "test-user";
  private final DefaultPartCategoryService defaultPartCategoryService;
  private final AuditEventService auditEventService;

  public PartCategoryServiceTest(
      DefaultPartCategoryService defaultPartCategoryService, AuditEventService auditEventService) {
    this.defaultPartCategoryService = defaultPartCategoryService;
    this.auditEventService = auditEventService;
  }

  @BeforeEach
  void setUp() {
    AuditEventContextHolder.setContext(new AuditEventContext(TEST_USER));
  }

  @AfterEach
  void clear() {
    AuditEventContextHolder.clear();
  }

  @Test
  void testCreateGetListAndDeletePartCategoryEntry() {
    // CREATE - tests
    var testName = getTestPartCategoryName();
    var testPartCategory1 = defaultPartCategoryService.createPartCategory(testName);
    Assertions.assertNotNull(testPartCategory1);
    Assertions.assertEquals(testName, testPartCategory1.name());
    List<AuditEvent> auditEvents =
        auditEventService
            .listAuditEvents(
                InventoryAuditEventConstants.CREATE_PART_CATEGORY,
                AuditEventType.POST.name(),
                null,
                TEST_USER,
                PageQuery.fromEmptyToken(10))
            .getResults();
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.SUCCESS, auditEvents.get(0).eventResponse().eventStatus());

    // CREATE - valid id format (e.g. 1-cat-partcategory-a8f62a3d027a4c6ba6363d7ca90cf406)
    var partCategoryId = testPartCategory1.id().toString();
    Assertions.assertTrue(partCategoryId.startsWith("1-cat-partcategory-"));

    var partCategoryUuidChars = partCategoryId.substring(19);
    Assertions.assertTrue(partCategoryUuidChars.matches("[a-f0-9]+"));

    // test GET
    PartCategory fetched = defaultPartCategoryService.getPartCategory(testPartCategory1.id());
    assertEquals(testPartCategory1, fetched);

    // LIST - tests
    final var testPartCategory2 =
        defaultPartCategoryService.createPartCategory(getTestPartCategoryName());
    final var testPartCategory3 =
        defaultPartCategoryService.createPartCategory(getTestPartCategoryName());

    var pageQuery = PageQuery.fromEmptyToken(10, PageQuery.SortOrder.ASC, "timeCreated");
    final var testPartCategoriesList =
        defaultPartCategoryService.listPartCategories(null, pageQuery);

    // LIST - tests - get full list
    Assertions.assertNotNull(testPartCategory2);
    Assertions.assertNotNull(testPartCategory3);
    Assertions.assertEquals(3, testPartCategoriesList.getItems().size());

    // LIST - tests - pagination: single page at a time
    final Set<PartCategory> pagedPartCategories = new HashSet<>();
    PaginatedList<PartCategory> listPartCategories;
    TokenPageQuery pageToken = PageQuery.fromEmptyToken(1);

    var idx = 0;
    var listSize = testPartCategoriesList.getItems().size();

    while (idx < listSize) {
      listPartCategories = defaultPartCategoryService.listPartCategories(null, pageToken);
      Assertions.assertEquals(1, listPartCategories.getResults().size());
      Assertions.assertTrue(listPartCategories.hasNext());
      Assertions.assertNotNull(listPartCategories.getNextPageToken());
      pagedPartCategories.add(listPartCategories.getResults().get(0));

      pageToken = PageQuery.fromToken(listPartCategories.getNextPageToken(), 1);
      idx++;
    }

    // LIST - tests - no more pages
    listPartCategories = defaultPartCategoryService.listPartCategories(null, pageToken);
    Assertions.assertEquals(0, listPartCategories.getResults().size());
    Assertions.assertFalse(listPartCategories.hasNext());
    Assertions.assertNull(listPartCategories.getNextPageToken());

    // LIST - tests - verify all objects are paged through
    Assertions.assertEquals(3, pagedPartCategories.size());
    Assertions.assertTrue(pagedPartCategories.contains(testPartCategory1));
    Assertions.assertTrue(pagedPartCategories.contains(testPartCategory2));
    Assertions.assertTrue(pagedPartCategories.contains(testPartCategory3));

    // LIST - tests - use filter on a product category name
    var name = testPartCategory1.name();
    pageToken = PageQuery.fromEmptyToken(10);
    var nameFilterList = defaultPartCategoryService.listPartCategories(name, pageToken);
    Assertions.assertEquals(1, nameFilterList.getResults().size());

    // LIST - tests - use filter on non-existent product category name
    name = "non-existent-category";
    pageToken = PageQuery.fromEmptyToken(10);
    nameFilterList = defaultPartCategoryService.listPartCategories(name, pageToken);
    Assertions.assertEquals(0, nameFilterList.getResults().size());

    // DELETE - tests
    var currentEtag = TaggingUtils.computeTagHash(Long.toString(testPartCategory1.version()));
    defaultPartCategoryService.deletePartCategory(testPartCategory1.id(), currentEtag);
    auditEvents =
        auditEventService
            .listAuditEvents(
                InventoryAuditEventConstants.DELETE_PART_CATEGORY,
                AuditEventType.DELETE.name(),
                null,
                TEST_USER,
                PageQuery.fromEmptyToken(10))
            .getResults();
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.SUCCESS, auditEvents.get(0).eventResponse().eventStatus());

    // make sure it no longer exists
    assertThrows(
        RenderableException.class,
        () -> defaultPartCategoryService.getPartCategory(testPartCategory1.id()));
  }

  @Test
  void testNegativeIntegrationTestsCategoryEntry() {

    // CREATE - no name provided
    assertThrows(
        DataAccessException.class, () -> defaultPartCategoryService.createPartCategory(null));
    List<AuditEvent> auditEvents =
        auditEventService
            .listAuditEvents(
                InventoryAuditEventConstants.CREATE_PART_CATEGORY,
                AuditEventType.POST.name(),
                null,
                TEST_USER,
                PageQuery.fromEmptyToken(10))
            .getResults();
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.FAILED, auditEvents.get(0).eventResponse().eventStatus());

    // CREATE - duplicate name provided
    var testName = getTestPartCategoryName();
    var testPartCategory = defaultPartCategoryService.createPartCategory(testName);
    assertThrows(
        RuntimeException.class, () -> defaultPartCategoryService.createPartCategory(testName));

    // DELETE - attempt to delete with an invalid etag
    assertThrows(
        RenderableException.class,
        () -> defaultPartCategoryService.deletePartCategory(testPartCategory.id(), "invalid-etag"));
    auditEvents =
        auditEventService
            .listAuditEvents(
                InventoryAuditEventConstants.DELETE_PART_CATEGORY,
                AuditEventType.DELETE.name(),
                null,
                TEST_USER,
                PageQuery.fromEmptyToken(10))
            .getResults();
    assertEquals(1, auditEvents.size());
    assertEquals(EventStatus.FAILED, auditEvents.get(0).eventResponse().eventStatus());

    // DELETE - attempt to delete with an invalid id
    var currentEtag = TaggingUtils.computeTagHash(Long.toString(testPartCategory.version()));
    var invalidCategoryId = RmcId.generate(RmcIdType.Catalog.PART_CATEGORY);
    assertThrows(
        RenderableException.class,
        () -> defaultPartCategoryService.deletePartCategory(invalidCategoryId, currentEtag));
  }

  public String getTestPartCategoryName() {
    return "test-part-category-" + UUID.randomUUID();
  }
}
