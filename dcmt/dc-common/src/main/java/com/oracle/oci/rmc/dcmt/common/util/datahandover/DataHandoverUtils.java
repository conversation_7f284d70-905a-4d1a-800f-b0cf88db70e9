package com.oracle.oci.rmc.dcmt.common.util.datahandover;

import static com.oracle.oci.rmc.client.model.RackModelLifecycleState.Deleted;
import static com.oracle.oci.rmc.dcmt.common.task.tasks.datahandover.SignAndUploadFileTask.DEFAULT_MULTIPART_UPLOAD_SIZE_MB;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.common.base.Charsets;
import com.google.common.base.Stopwatch;
import com.google.common.util.concurrent.AtomicDouble;
import com.oracle.bmc.objectstorage.ObjectStorageClient;
import com.oracle.bmc.objectstorage.ObjectStoragePaginators;
import com.oracle.bmc.objectstorage.model.ObjectSummary;
import com.oracle.bmc.objectstorage.requests.GetObjectRequest;
import com.oracle.bmc.objectstorage.requests.HeadObjectRequest;
import com.oracle.bmc.objectstorage.requests.ListObjectsRequest;
import com.oracle.bmc.objectstorage.requests.PutObjectRequest;
import com.oracle.bmc.objectstorage.responses.GetObjectResponse;
import com.oracle.bmc.objectstorage.responses.HeadObjectResponse;
import com.oracle.bmc.objectstorage.responses.ListObjectsResponse;
import com.oracle.bmc.objectstorage.transfer.UploadConfiguration;
import com.oracle.bmc.objectstorage.transfer.UploadManager;
import com.oracle.oci.rmc.client.DcmsClient;
import com.oracle.oci.rmc.client.RepairOrderClient;
import com.oracle.oci.rmc.client.model.AssetAttestationDetails;
import com.oracle.oci.rmc.client.model.AssetAttestationResultStates;
import com.oracle.oci.rmc.client.model.AssetAttestationSummary;
import com.oracle.oci.rmc.client.model.BuildingDetails;
import com.oracle.oci.rmc.client.model.BuildingSummary;
import com.oracle.oci.rmc.client.model.ChangeOrderSummary;
import com.oracle.oci.rmc.client.model.Device;
import com.oracle.oci.rmc.client.model.DeviceInstanceSummary;
import com.oracle.oci.rmc.client.model.InternalRepairOrderSourceType;
import com.oracle.oci.rmc.client.model.RackInstanceLifecycleState;
import com.oracle.oci.rmc.client.model.RackInstanceLifecycleSubState;
import com.oracle.oci.rmc.client.model.RackInstanceSummary;
import com.oracle.oci.rmc.client.model.RackModelDetails;
import com.oracle.oci.rmc.client.model.RackModelSummary;
import com.oracle.oci.rmc.client.model.RackPositionDetails;
import com.oracle.oci.rmc.client.model.RackPositionSummary;
import com.oracle.oci.rmc.client.model.RepairOrder;
import com.oracle.oci.rmc.client.model.RepairOrderSummary;
import com.oracle.oci.rmc.client.model.Room;
import com.oracle.oci.rmc.client.model.SortOrder;
import com.oracle.oci.rmc.client.requests.GetAssetAttestationRequest;
import com.oracle.oci.rmc.client.requests.GetBuildingRequest;
import com.oracle.oci.rmc.client.requests.GetDeviceRequest;
import com.oracle.oci.rmc.client.requests.GetInternalRepairOrderRequest;
import com.oracle.oci.rmc.client.requests.GetRackModelRequest;
import com.oracle.oci.rmc.client.requests.GetRackPositionRequest;
import com.oracle.oci.rmc.client.requests.GetRoomRequest;
import com.oracle.oci.rmc.client.requests.ListAssetAttestationsRequest;
import com.oracle.oci.rmc.client.requests.ListBuildingsRequest;
import com.oracle.oci.rmc.client.requests.ListDeviceInstancesRequest;
import com.oracle.oci.rmc.client.requests.ListInternalRepairOrdersRequest;
import com.oracle.oci.rmc.client.requests.ListRackInstancesRequest;
import com.oracle.oci.rmc.client.requests.ListRackModelsRequest;
import com.oracle.oci.rmc.client.requests.ListRackPositionsRequest;
import com.oracle.oci.rmc.client.requests.ListSitesRequest;
import com.oracle.oci.rmc.client.responses.ListAssetAttestationsResponse;
import com.oracle.oci.rmc.dcms.api.model.common.DeviceRole;
import com.oracle.oci.rmc.dcmt.common.data.handover.BurninCredentials;
import com.oracle.oci.rmc.dcmt.common.data.handover.HandoverRackPlatform;
import com.oracle.oci.rmc.dcmt.common.data.handover.SoftwareBundleManifest;
import com.oracle.oci.rmc.dcmt.common.data.handover.TrancheManifest;
import com.oracle.oci.rmc.dcmt.common.data.handover.TrancheManifestRackData;
import com.oracle.oci.rmc.dcmt.common.task.manager.TaskResult;
import com.oracle.oci.rmc.dcmt.common.task.tasks.datahandover.object.TrancheOtsTicketMetadata;
import com.oracle.oci.rmc.dcmt.common.task.tasks.datahandover.object.TrancheOtsTicketMetadataBuilder;
import com.oracle.pic.telemetry.commons.metrics.Metrics;
import com.oracle.pic.telemetry.commons.metrics.model.MetricName;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.X509EncodedKeySpec;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.Queue;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.crypto.Cipher;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DataHandoverUtils {
  private static final Integer GB200_DEVICE_COUNT = 18;
  private static final String GB200_PLATFORM = "GB200";
  private static final String E5_PLATFORM = "E5";
  private static final String TRANCHE_ID_FORMAT = "tranche_%d_%s";
  public static final String TRANCHE_ID_DIM_NAME = "trancheId";
  public static final String RACK_POSITIONS_DIM_NAME = "rackPositions";
  public static final String RACK_SERIALS_DIM_NAME = "rackSerials";
  public static final String DATA_HALL_DIM_NAME = "dataHall";
  public static final String RESULT_DIM_NAME = "result";
  public static final String MINUTES_SINCE_LAST_TRANCHE_METRIC_NAME = "MinutesSinceLastTranche";
  public static final String AWAITING_REPAIR_RACKS_METRIC_NAME = "AwaitingRepairRacks";
  public static final String AWAITING_BURNIN_RACKS_METRIC_NAME = "AwaitingBurninRacks";
  private static final String BOOTSTRAP_BURNIN_DEVICE_ATTESTATION = "Bootstrap_Burnin_Device";
  private static final String CRYPTO_ATTESTATION_NAME = "cryptographic-keys-reference";
  private static final String HANDOVER_READY_ATTESTATION_NAME = "Handover_Ready";
  public static final String HANDOVER_INITIATED_ATTESTATION_NAME = "Handover_Initiated";
  public static final String REPAIR_HANDOVER_READY_ATTESTATION_NAME = "Repair_Handover_Ready";
  public static final String BURNINATOR_HANDOVER_READY_ATTESTATION_NAME =
      "Burninator_Handover_Ready";
  private static final ExecutorService DATA_HANDOVER_EXECUTOR = Executors.newFixedThreadPool(4);
  private static final ObjectMapper MAPPER = new ObjectMapper();
  private static final EnumSet<RepairOrder.LifecycleState> CLOSED_LIFECYCLE_STATES =
      EnumSet.of(RepairOrder.LifecycleState.Closed, RepairOrder.LifecycleState.Canceled);
  private static final int LIST_LIMIT = 500;
  private static final Logger LOG = LoggerFactory.getLogger(DataHandoverUtils.class);
  private static final int BUFFER_SIZE = 8192;
  private static final Map<String, Object> CACHE_MAP = new ConcurrentHashMap<>();

  static {
    MAPPER.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
    MAPPER
        .registerModule(new JavaTimeModule())
        .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
  }

  private DataHandoverUtils() {}

  @SuppressFBWarnings(
      value = "PATH_TRAVERSAL_OUT",
      justification = "This value doesn't come from an external user")
  public static byte[] downloadFromObjectStorage(
      ObjectStorageClient objectStorageClient,
      String namespace,
      String bucket,
      String path,
      boolean writeToDisk,
      String destinationFilePath) {
    LOG.debug(
        "Downloading file '{}' from bucket '{}' {}",
        path,
        bucket,
        destinationFilePath == null ? "" : "to " + destinationFilePath);
    GetObjectRequest request =
        GetObjectRequest.builder()
            .namespaceName(namespace)
            .bucketName(bucket)
            .objectName(path)
            .build();

    GetObjectResponse response = objectStorageClient.getObject(request);

    try (InputStream inputStream = response.getInputStream();
        ByteArrayOutputStream buffer = new ByteArrayOutputStream()) {

      byte[] temp = new byte[BUFFER_SIZE];
      int bytesRead;

      while ((bytesRead = inputStream.read(temp)) != -1) {
        buffer.write(temp, 0, bytesRead);
      }

      byte[] fileBytes = buffer.toByteArray();

      if (writeToDisk && destinationFilePath != null) {
        LOG.debug("Writing object {} to file {}", path, destinationFilePath);

        Path destPath = Path.of(destinationFilePath);
        Path parentDir = destPath.getParent();
        if (parentDir != null) {
          Files.createDirectories(parentDir); // Ensure parent directories exist
        }

        Files.write(destPath, fileBytes);
      }

      return fileBytes;
    } catch (IOException e) {
      throw new RuntimeException("Failed to download object", e);
    }
  }

  public static void uploadBytesToObjectStorage(
      boolean overwrite,
      int partMegabyteSize,
      ObjectStorageClient objectStorageClient,
      String namespace,
      String bucket,
      String path,
      byte[] content,
      Map<String, String> metadata) {

    try (InputStream inputStream = new BufferedInputStream(new ByteArrayInputStream(content))) {
      UploadManager uploadManager =
          new UploadManager(
              objectStorageClient,
              UploadConfiguration.builder()
                  .allowMultipartUploads(true)
                  .lengthPerUploadPart(partMegabyteSize)
                  .allowParallelUploads(true)
                  .build());

      // Create PutObjectRequest with metadata and object location
      PutObjectRequest putObjectRequest =
          PutObjectRequest.builder()
              .namespaceName(namespace)
              .bucketName(bucket)
              .objectName(path)
              .opcMeta(metadata)
              .contentLength((long) content.length)
              .build();

      AtomicDouble nextLogPercent = new AtomicDouble();
      long totalLength = (long) content.length / 1024L / 1024L;
      Stopwatch stopwatch = Stopwatch.createStarted();
      uploadManager.upload(
          UploadManager.UploadRequest.builder(inputStream, content.length)
              .allowOverwrite(overwrite)
              .progressReporter(
                  (completed, total) -> {
                    double percent = ((double) completed / total) * 100.0;
                    // Log every 10%
                    if (percent >= nextLogPercent.get()) {
                      LOG.info(
                          "Upload progress: {}KB/{}KB ({}%)",
                          completed / 1024L, totalLength, (int) percent);
                      nextLogPercent.set(percent + 10);
                    }
                  })
              .build(putObjectRequest));
      LOG.info("Finished uploading object in {}ms.", stopwatch.elapsed(TimeUnit.MILLISECONDS));

    } catch (Exception e) {
      throw new RuntimeException("Failed to upload object using UploadManager", e);
    }
  }

  public static void uploadBytesToObjectStorage(
      ObjectStorageClient objectStorageClient,
      String namespace,
      String bucket,
      String path,
      byte[] content) {
    uploadBytesToObjectStorage(
        false,
        DEFAULT_MULTIPART_UPLOAD_SIZE_MB,
        objectStorageClient,
        namespace,
        bucket,
        path,
        content,
        Map.of());
  }

  public static void uploadBytesToObjectStorage(
      ObjectStorageClient objectStorageClient,
      String namespace,
      String bucket,
      String path,
      byte[] content,
      Map<String, String> metadata) {
    uploadBytesToObjectStorage(
        false,
        DEFAULT_MULTIPART_UPLOAD_SIZE_MB,
        objectStorageClient,
        namespace,
        bucket,
        path,
        content,
        metadata);
  }

  public static Optional<String> getExistingObjectMd5Hash(
      ObjectStorageClient objectStorageClient, String namespace, String bucket, String path) {
    HeadObjectResponse headResponse =
        objectStorageClient.headObject(
            HeadObjectRequest.builder()
                .namespaceName(namespace)
                .bucketName(bucket)
                .objectName(path)
                .build());

    return Optional.of(headResponse.getContentMd5());
  }

  /**
   * Retrieves a list of open change orders by querying the ChangeOrderStubClient. This method
   * fetches all change orders in a paginated fashion, filters out those whose lifecycle state is
   * considered closed (i.e., {@code CLOSED} or {@code CANCELED}), and returns only the open change
   * orders.
   *
   * <p>The method continues retrieving subsequent pages of results until no further results are
   * available. It logs the number of open change orders found.
   *
   * @return a list of {@link ChangeOrderSummary} objects representing change orders that are not in
   *     {@code CLOSED} or {@code CANCELED} lifecycle states. May return an empty list if none are
   *     found.
   */
  public static List<RepairOrder> listOpenRepairOrders(
      RepairOrderClient repairOrderClient, String regionAirportCode) {
    String cacheKey = "listOpenRepairOrders-" + regionAirportCode;
    List<RepairOrder> cached = (List<RepairOrder>) CACHE_MAP.get(cacheKey);
    if (cached != null) {
      LOG.debug("Found {} cached repair orders", cached.size());
      return cached;
    }

    List<RepairOrder> openRepairOrders = Collections.synchronizedList(new ArrayList<>());

    for (RepairOrder.LifecycleState lifecycleState :
        EnumSet.complementOf(CLOSED_LIFECYCLE_STATES)) {
      LOG.info("Searching for repair orders with state '{}'", lifecycleState.getValue());
      String page = null;
      do {
        var listResponse =
            repairOrderClient.listInternalRepairOrders(
                ListInternalRepairOrdersRequest.builder()
                    .region(regionAirportCode)
                    .source(InternalRepairOrderSourceType.Burninator.getValue())
                    .limit(LIST_LIMIT)
                    .lifecycleState(lifecycleState.getValue())
                    .sortBy(ListInternalRepairOrdersRequest.SortBy.TimeCreated)
                    .sortOrder(SortOrder.Desc)
                    .page(page)
                    .build());

        List<RepairOrderSummary> openChangeOrders =
            listResponse.getRepairOrderCollection().getItems();
        if (openChangeOrders == null || openChangeOrders.isEmpty()) {
          break;
        }
        LOG.debug(
            "Found {} repair orders in '{}' state. Getting details for each...",
            openChangeOrders.size(),
            lifecycleState.getValue());

        CountDownLatch countDownLatch = new CountDownLatch(openChangeOrders.size());
        openChangeOrders.forEach(
            summary ->
                DATA_HANDOVER_EXECUTOR.submit(
                    () -> {
                      try {
                        LOG.debug(
                            "Retrieving '{}' repair order: {}",
                            lifecycleState.getValue(),
                            summary.getId());
                        openRepairOrders.add(
                            repairOrderClient
                                .getInternalRepairOrder(
                                    GetInternalRepairOrderRequest.builder()
                                        .repairOrderId(summary.getId())
                                        .build())
                                .getRepairOrder());
                      } finally {
                        countDownLatch.countDown();
                      }
                    }));

        // Wait for all change orders to be processed
        try {
          countDownLatch.await();
        } catch (InterruptedException e) {
          Thread.currentThread().interrupt(); // Restore interrupted status
          throw new RuntimeException("Failed to wait for change order retrieval", e);
        }

        page = listResponse.getOpcNextPage();
      } while (page != null);
    }

    LOG.info("Found {} open repair orders", openRepairOrders.size());
    CACHE_MAP.put(cacheKey, openRepairOrders);
    return openRepairOrders;
  }

  public static String prettySerializeSoftwareBundleManifest(
      SoftwareBundleManifest softwareBundleManifest) {
    try {
      return MAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(softwareBundleManifest);
    } catch (JsonProcessingException e) {
      throw new RuntimeException("Failed to pretty-serialize software bundle manifest", e);
    }
  }

  public static String prettySerializeTrancheManifest(TrancheManifest trancheManifest) {
    try {
      return MAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(trancheManifest);
    } catch (JsonProcessingException e) {
      throw new RuntimeException("Failed to pretty-serialize tranche manifest", e);
    }
  }

  public static String serializeTrancheManifest(TrancheManifest trancheManifest) {
    try {
      return MAPPER.writeValueAsString(trancheManifest);
    } catch (JsonProcessingException e) {
      throw new RuntimeException("Failed to serialize tranche manifest", e);
    }
  }

  public static TrancheManifest deSerializeTrancheManifest(String trancheManifest) {
    try {
      return MAPPER.readValue(trancheManifest, TrancheManifest.class);
    } catch (JsonProcessingException e) {
      throw new RuntimeException("Failed to deserialize tranche manifest", e);
    }
  }

  public static void serializeBurninCredentialsToFile(
      BurninCredentials[] burninCredentialsArray, File burninCredentialsFile) {
    try {
      MAPPER
          .writerWithDefaultPrettyPrinter()
          .writeValue(burninCredentialsFile, burninCredentialsArray);
    } catch (IOException e) {
      throw new RuntimeException("Failed to serialize burn-in credentials to file", e);
    }
  }

  public static BurninCredentials[] deSerializeBurninCredentialsFromFile(
      File burninCredentialsFile) {
    try {
      return MAPPER.readValue(burninCredentialsFile, BurninCredentials[].class);
    } catch (IOException e) {
      throw new RuntimeException("Failed to deserialize burn-in credentials file", e);
    }
  }

  public static List<RepairOrder> findOpenRepairOrdersForRack(
      String rackSerial, RepairOrderClient repairOrderClient, String region) {

    List<RepairOrder> openRepairOrders = Collections.synchronizedList(new ArrayList<>());

    LOG.info("Searching for repair orders with rack serial '{}'", rackSerial);
    String page = null;
    do {
      var listResponse =
          repairOrderClient.listInternalRepairOrders(
              ListInternalRepairOrdersRequest.builder()
                  .region(region)
                  .source(InternalRepairOrderSourceType.Burninator.getValue())
                  .limit(LIST_LIMIT)
                  .rackSerialNumber(rackSerial)
                  .sortBy(ListInternalRepairOrdersRequest.SortBy.TimeCreated)
                  .sortOrder(SortOrder.Desc)
                  .page(page)
                  .build());

      List<RepairOrderSummary> repairOrderSummaries =
          listResponse.getRepairOrderCollection().getItems();
      if (repairOrderSummaries == null || repairOrderSummaries.isEmpty()) {
        break;
      }

      // Filter repair order summaries by lifecycle state
      List<RepairOrderSummary> openRepairOrderSummaries =
          repairOrderSummaries.stream()
              .filter(
                  summary ->
                      EnumSet.complementOf(CLOSED_LIFECYCLE_STATES)
                          .contains(summary.getLifecycleState()))
              .toList();

      LOG.debug(
          "Found {} repair orders for rack serial: {}. Getting details for each...",
          openRepairOrderSummaries.size(),
          rackSerial);

      CountDownLatch countDownLatch = new CountDownLatch(openRepairOrderSummaries.size());
      openRepairOrderSummaries.forEach(
          summary ->
              DATA_HANDOVER_EXECUTOR.submit(
                  () -> {
                    try {
                      LOG.debug("Retrieving repair order: {}", summary.getId());
                      openRepairOrders.add(
                          repairOrderClient
                              .getInternalRepairOrder(
                                  GetInternalRepairOrderRequest.builder()
                                      .repairOrderId(summary.getId())
                                      .build())
                              .getRepairOrder());
                    } finally {
                      countDownLatch.countDown();
                    }
                  }));

      // Wait for all repair orders to be processed
      try {
        countDownLatch.await();
      } catch (InterruptedException e) {
        Thread.currentThread().interrupt(); // Restore interrupted status
        throw new RuntimeException("Failed to wait for change order retrieval", e);
      }

      page = listResponse.getOpcNextPage();
    } while (page != null);

    return openRepairOrders;
  }

  /**
   * Retrieves the serial number of a rack instance associated with a given rack number.
   *
   * <p>This method performs the following steps:
   *
   * <ol>
   *   <li>Fetches the rack position details for the given rack number using {@link
   *       #getRackPositionByRackNumberAndBuildingId(DcmsClient, String, String)}.
   *   <li>Retrieves the rack model summary at the fetched rack position using {@link
   *       #getRackModelAtPosition(DcmsClient, String, RackPositionDetails)}.
   *   <li>Obtains the rack instance summary for the retrieved rack model using {@link
   *       #getRackInstanceForRackModel(DcmsClient, String, String)}.
   *   <li>Logs and returns the serial number of the rack instance.
   * </ol>
   *
   * @param dcmsClient the DcmsClient instance used to interact with the DCMS service
   * @param region the region where the rack is located
   * @param rackNumber the number of the rack for which to retrieve the serial number
   * @param buildingId the Id of the building where the rack resides
   * @return the serial number of the rack instance associated with the given rack number
   */
  public static String getRackSerialByRackNumberAndBuildingId(
      DcmsClient dcmsClient, String region, String rackNumber, String buildingId) {
    RackPositionDetails position =
        getRackPositionByRackNumberAndBuildingId(dcmsClient, rackNumber, buildingId);
    RackModelSummary rackModel = getRackModelAtPosition(dcmsClient, region, position);
    RackInstanceSummary rackInstance =
        getRackInstanceForRackModel(dcmsClient, region, rackModel.getId());

    LOG.info("Found rack serial {} for position {}", rackInstance.getSerialNumber(), position);
    return rackInstance.getSerialNumber();
  }

  public static String getRackSerialBySiteAndRackNumber(
      DcmsClient dcmsClient, String region, String siteName, String rackNumber) {

    var siteSummary =
        dcmsClient
            .listSites(ListSitesRequest.builder().region(region).displayName(siteName).build())
            .getSiteCollection()
            .getItems()
            .stream()
            .findFirst()
            .orElseThrow(
                () ->
                    new IllegalStateException(
                        "No site found for region : " + region + " and site name : " + siteName));

    LOG.debug("Fetching rack position: {}", rackNumber);
    RackPositionSummary summary =
        dcmsClient
            .listRackPositions(
                ListRackPositionsRequest.builder()
                    .siteId(siteSummary.getId())
                    .rackNumber(rackNumber)
                    .build())
            .getRackPositionCollection()
            .getItems()
            .stream()
            .findFirst()
            .orElseThrow(
                () ->
                    new IllegalStateException(
                        "No Rack position found for rack number: " + rackNumber));

    var rackPositionDetails =
        dcmsClient
            .getRackPosition(
                GetRackPositionRequest.builder().rackPositionId(summary.getId()).build())
            .getRackPositionDetails();

    RackModelSummary rackModel = getRackModelAtPosition(dcmsClient, region, rackPositionDetails);
    RackInstanceSummary rackInstance =
        getRackInstanceForRackModel(dcmsClient, region, rackModel.getId());
    LOG.info("Found rack serial {} for position {}", rackInstance.getSerialNumber(), rackNumber);
    return rackInstance.getSerialNumber();
  }

  /**
   * Retrieves the first rack instance summary associated with a given rack model ID.
   *
   * <p>This method performs the following steps:
   *
   * <ol>
   *   <li>Sends a request to the DCMS service to list rack instances associated with the given rack
   *       model ID.
   *   <li>Retrieves the list of rack instance summaries from the response.
   *   <li>Returns the first rack instance summary found, or throws an {@link IllegalStateException}
   *       if none are found.
   * </ol>
   *
   * @param dcmsClient the DcmsClient instance used to interact with the DCMS service
   * @param region the region where the rack instance is located
   * @param rackModelId the ID of the rack model for which to retrieve the rack instance summary
   * @return the first {@link RackInstanceSummary} associated with the given rack model ID
   * @throws IllegalStateException if no rack instance is found for the given rack model ID
   */
  public static RackInstanceSummary getRackInstanceForRackModel(
      DcmsClient dcmsClient, String region, String rackModelId) {
    return dcmsClient
        .listRackInstances(
            ListRackInstancesRequest.builder().region(region).rackModelId(rackModelId).build())
        .getRackInstanceCollection()
        .getItems()
        .stream()
        .findFirst()
        .orElseThrow(
            () ->
                new IllegalStateException(
                    "No Rack instance found for rack position: " + rackModelId));
  }

  /**
   * Retrieves the first rack model summary associated with a given rack position.
   *
   * <p>This method performs the following steps:
   *
   * <ol>
   *   <li>Sends a request to the DCMS service to list rack models associated with the given rack
   *       number and site ID.
   *   <li>Retrieves the list of rack model summaries from the response.
   *   <li>Returns the first rack model summary found, or throws an {@link IllegalStateException} if
   *       none are found.
   * </ol>
   *
   * @param dcmsClient the DcmsClient instance used to interact with the DCMS service
   * @param region the region where the rack model is located
   * @param rackPosition the details of the rack position for which to retrieve the rack model
   *     summary
   * @return the first {@link RackModelSummary} associated with the given rack position
   * @throws IllegalStateException if no rack model is found for the given rack position
   */
  public static RackModelSummary getRackModelAtPosition(
      DcmsClient dcmsClient, String region, RackPositionDetails rackPosition) {
    LOG.info(
        "Fetching rack model region = {}, rack position = {}, site id = {}",
        region,
        rackPosition,
        rackPosition.getSiteId());
    return dcmsClient
        .listRackModels(
            ListRackModelsRequest.builder()
                .region(region)
                .rackNumber(rackPosition.getRackNumber())
                .siteId(rackPosition.getSiteId())
                .build())
        .getRackModelCollection()
        .getItems()
        .stream()
        .findFirst()
        .orElseThrow(
            () ->
                new IllegalStateException(
                    "No Rack model found for rack position: " + rackPosition.getRackNumber()));
  }

  /**
   * Retrieves the rack position details for a given rack number.
   *
   * <p>This method performs the following steps:
   *
   * <ol>
   *   <li>Sends a request to the DCMS service to list rack positions associated with the given rack
   *       number.
   *   <li>Retrieves the list of rack position summaries from the response.
   *   <li>Returns the details of the first rack position summary found, or throws an {@link
   *       IllegalStateException} if none are found.
   * </ol>
   *
   * @param dcmsClient the DcmsClient instance used to interact with the DCMS service
   * @param rackNumber the number of the rack for which to retrieve the rack position details
   * @param buildingId the Id of the building where the rack resides
   * @return the {@link RackPositionDetails} associated with the given rack number
   * @throws IllegalStateException if no rack position is found for the given rack number
   */
  public static RackPositionDetails getRackPositionByRackNumberAndBuildingId(
      DcmsClient dcmsClient, String rackNumber, String buildingId) {
    LOG.debug("Fetching rack position: {}", rackNumber);
    RackPositionSummary summary =
        dcmsClient
            .listRackPositions(
                ListRackPositionsRequest.builder()
                    .rackNumber(rackNumber)
                    .buildingId(buildingId)
                    .build())
            .getRackPositionCollection()
            .getItems()
            .stream()
            .findFirst()
            .orElseThrow(
                () ->
                    new IllegalStateException(
                        "No Rack position found for rack number: " + rackNumber));

    return dcmsClient
        .getRackPosition(GetRackPositionRequest.builder().rackPositionId(summary.getId()).build())
        .getRackPositionDetails();
  }

  public static BuildingSummary getBuildingByName(DcmsClient dcmsClient, String name) {
    String cacheKey = "getBuildingByName-" + name;
    BuildingSummary cachedBuilding = (BuildingSummary) CACHE_MAP.get(cacheKey);
    if (cachedBuilding != null) {
      return cachedBuilding;
    }

    LOG.debug("Fetching building: {}", name);
    BuildingSummary building =
        dcmsClient
            .listBuildings(ListBuildingsRequest.builder().name(name).build())
            .getBuildingCollection()
            .getItems()
            .stream()
            .findFirst()
            .orElseThrow(() -> new IllegalStateException("No building found for name: " + name));
    CACHE_MAP.put(cacheKey, building);
    return building;
  }

  /**
   * Reads a public key from a PEM file.
   *
   * <p>The method reads the contents of the provided PEM file, extracts the public key, decodes it
   * from Base64, and generates a PublicKey object using the RSA algorithm.
   *
   * @param pemFile the file containing the public key in PEM format
   * @return the PublicKey object representing the key read from the file
   * @throws RuntimeException if there is an issue reading the file or generating the public key
   */
  public static PublicKey readPublicKeyFromPemFile(File pemFile) {
    try {
      String pemContent = Files.readString(pemFile.toPath(), Charsets.UTF_8);
      String publicKeyPEM =
          pemContent
              .replace("-----BEGIN PUBLIC KEY-----", "")
              .replace("-----END PUBLIC KEY-----", "")
              .replaceAll("\\s", "");

      byte[] decoded = Base64.getDecoder().decode(publicKeyPEM);
      X509EncodedKeySpec spec = new X509EncodedKeySpec(decoded);
      KeyFactory kf = KeyFactory.getInstance("RSA");
      return kf.generatePublic(spec);
    } catch (NoSuchAlgorithmException | InvalidKeySpecException | IOException e) {
      throw new RuntimeException("Failed to read public key from pem file", e);
    }
  }

  /**
   * Encrypts the given data using the provided public key.
   *
   * <p>The encryption process involves using the RSA algorithm with ECB mode and OAEP padding with
   * SHA-256 and MGF1 padding. The data is first converted to bytes using UTF-8 encoding, then
   * encrypted, and finally encoded as a Base64 string.
   *
   * @param publicKey the public key to use for encryption
   * @param data the data to be encrypted
   * @return the encrypted data as a Base64-encoded string
   * @throws RuntimeException if there is an issue during the encryption process
   */
  public static String encryptDataWithPublicKey(PublicKey publicKey, String data) {
    try {
      Cipher cipher = Cipher.getInstance("RSA/ECB/OAEPWithSHA-256AndMGF1Padding");
      cipher.init(Cipher.ENCRYPT_MODE, publicKey);
      byte[] encryptedBytes = cipher.doFinal(data.getBytes(Charsets.UTF_8));
      return Base64.getEncoder().encodeToString(encryptedBytes);
    } catch (Exception e) {
      throw new RuntimeException("Failed to encrypt data using public key", e);
    }
  }

  public static HandoverRackDetails getHandoverRackDetails(
      DcmsClient client, String regionAirportCode, String rackNumber, String buildingId) {
    String cacheKey =
        "getHandoverRackDetails-" + regionAirportCode + "-" + rackNumber + "-" + buildingId;
    HandoverRackDetails handoverDetails = (HandoverRackDetails) CACHE_MAP.get(cacheKey);
    if (handoverDetails != null) {
      return handoverDetails;
    }
    var rackPosition =
        DataHandoverUtils.getRackPositionByRackNumberAndBuildingId(client, rackNumber, buildingId);
    var rackModelSummary =
        DataHandoverUtils.getRackModelAtPosition(client, regionAirportCode, rackPosition);
    var rackInstanceSummary =
        getRackInstanceForRackModel(client, regionAirportCode, rackModelSummary.getId());

    Set<DeviceInstanceSummary> devices =
        new HashSet<>(
            client
                .listDeviceInstances(
                    ListDeviceInstancesRequest.builder()
                        .region(regionAirportCode)
                        .limit(LIST_LIMIT)
                        .rackInstanceId(rackInstanceSummary.getId())
                        .build())
                .getDeviceInstanceCollection()
                .getItems());

    Map<String, Device> deviceModels =
        devices.stream()
            .map(
                device ->
                    client
                        .getDevice(
                            GetDeviceRequest.builder()
                                .region(regionAirportCode)
                                .deviceId(device.getDeviceModelId())
                                .build())
                        .getDevice())
            .collect(Collectors.toMap(Device::getId, Function.identity()));

    LOG.info(
        "Found Rack Details for position {}: [ Rack Serial = {} ] [ Device Serials = {} ]",
        rackNumber,
        rackInstanceSummary.getSerialNumber(),
        devices.stream().map(DeviceInstanceSummary::getSerialNumber).toList());

    HandoverRackDetails details =
        new HandoverRackDetails(
            rackNumber, rackPosition, rackModelSummary, rackInstanceSummary, devices, deviceModels);
    CACHE_MAP.put(cacheKey, details);
    return details;
  }

  public static Optional<Integer> getExpectedHealthyDeviceCountForRackPlatform(String platform) {
    if (platform.contains(GB200_PLATFORM)) {
      return Optional.of(GB200_DEVICE_COUNT);
    }
    return Optional.empty();
  }

  public static Set<AssetAttestationSummary> getAllAttestationsWithNameAndSuccessful(
      DcmsClient dcmsClient, String region, String name, boolean successful) {
    String cacheKey =
        "getAllAttestationsWithNameAndSuccessful-" + region + "-" + name + "-" + successful;
    Set<AssetAttestationSummary> cached = (Set<AssetAttestationSummary>) CACHE_MAP.get(cacheKey);
    if (cached != null) {
      return cached;
    }

    LOG.info(
        "Fetching all attestations with name {} and successful={} in region {}",
        name,
        successful,
        region);
    Set<AssetAttestationSummary> attestations = new HashSet<>();
    String page = null;
    do {
      ListAssetAttestationsResponse response =
          dcmsClient.listAssetAttestations(
              ListAssetAttestationsRequest.builder()
                  .region(region)
                  .name(name)
                  .limit(LIST_LIMIT)
                  .page(page)
                  .build());

      LOG.debug(
          "Fetched {} attestations with name '{}'",
          response.getAssetAttestationCollection().getAssetAttestations().size(),
          name);

      attestations.addAll(response.getAssetAttestationCollection().getAssetAttestations());
      page = response.getOpcNextPage();
    } while (page != null);

    record AttestationKey(String assetId, String name) {}

    // Get only the latest versions of attestations for assets
    Map<AttestationKey, Optional<AssetAttestationSummary>> latestAttestations =
        attestations.stream()
            .collect(
                Collectors.groupingBy(
                    att -> new AttestationKey(att.getAssetId(), att.getName()),
                    Collectors.maxBy(
                        Comparator.comparing(AssetAttestationSummary::getTimeCompleted))));

    attestations =
        latestAttestations.values().stream()
            .filter(Optional::isPresent)
            .map(Optional::get)
            .filter(
                att -> successful == AssetAttestationResultStates.Succeeded.equals(att.getResult()))
            .collect(Collectors.toSet());

    LOG.debug(
        "Found {} total attestations with name '{}' and successful='{}'",
        attestations.size(),
        name,
        successful);
    CACHE_MAP.put(cacheKey, attestations);
    return attestations;
  }

  public static Set<DeviceInstanceSummary> getUnhealthyDevicesInRack(
      DcmsClient dcmsClient, String region, HandoverRackDetails handoverRackDetails) {
    String cacheKey = "getUnhealthyDevicesInRack-" + region + "-" + handoverRackDetails.hashCode();
    Set<DeviceInstanceSummary> cachedSet = (Set<DeviceInstanceSummary>) CACHE_MAP.get(cacheKey);
    if (cachedSet != null) {
      return cachedSet;
    }

    Set<String> successfulAttestationAssetIds =
        getAllAttestationsWithNameAndSuccessful(
                dcmsClient, region, BOOTSTRAP_BURNIN_DEVICE_ATTESTATION, true)
            .stream()
            .map(AssetAttestationSummary::getAssetId)
            .collect(Collectors.toSet());

    Set<String> serverDeviceRoles =
        Set.of(DeviceRole.GPU.getValue(), DeviceRole.COMPUTE.getValue());
    Set<DeviceInstanceSummary> unhealthyDevices =
        handoverRackDetails.devices().stream()
            .filter(
                device ->
                    serverDeviceRoles.contains(
                        handoverRackDetails
                            .deviceModels()
                            .get(device.getDeviceModelId())
                            .getRole()))
            .filter(device -> !successfulAttestationAssetIds.contains(device.getAssetId()))
            .collect(Collectors.toSet());
    LOG.debug(
        "Found {} total unhealthy devices in rack {}",
        unhealthyDevices.size(),
        handoverRackDetails.rackPosition().getRackNumber());
    CACHE_MAP.put(cacheKey, unhealthyDevices);
    return unhealthyDevices;
  }

  public static Set<DeviceInstanceSummary> getHealthyDevicesInRack(
      DcmsClient dcmsClient, String region, HandoverRackDetails handoverRackDetails) {
    String cacheKey = "getHealthyDevicesInRack-" + region + "-" + handoverRackDetails.hashCode();
    Set<DeviceInstanceSummary> cachedSet = (Set<DeviceInstanceSummary>) CACHE_MAP.get(cacheKey);
    if (cachedSet != null) {
      return cachedSet;
    }

    Set<String> attestationAssetIds =
        getAllAttestationsWithNameAndSuccessful(
                dcmsClient, region, BOOTSTRAP_BURNIN_DEVICE_ATTESTATION, true)
            .stream()
            .map(AssetAttestationSummary::getAssetId)
            .collect(Collectors.toSet());

    Set<DeviceInstanceSummary> healthyDevices =
        handoverRackDetails.devices().stream()
            .filter(device -> attestationAssetIds.contains(device.getAssetId()))
            .collect(Collectors.toSet());
    LOG.debug(
        "Found {} total healthy devices in rack {}",
        healthyDevices.size(),
        handoverRackDetails.rackPosition().getRackNumber());
    CACHE_MAP.put(cacheKey, healthyDevices);
    return healthyDevices;
  }

  public static Integer getHealthyServerAttestationCount(
      DcmsClient dcmsClient, String region, HandoverRackDetails handoverRackDetails) {
    return getHealthyDevicesInRack(dcmsClient, region, handoverRackDetails).size();
  }

  /**
   * Generates the next tranche ID for a given data hall by examining the existing tranche files in
   * the specified Object Storage bucket.
   *
   * <p>The method performs the following steps:
   *
   * <ol>
   *   <li>Validates the format of the provided data hall name. It expects the name to be in the
   *       format "abl15.1" (alphanumeric followed by a dot and a number), and throws an {@link
   *       IllegalArgumentException} if the format is invalid.
   *   <li>Lists objects in the Object Storage bucket with the prefix "tranches/" and filters those
   *       ending with the formatted data hall name and ".json" extension.
   *   <li>Parses the file names to extract the sequence number and identifies the latest sequence
   *       number for the given data hall.
   *   <li>Generates the next tranche ID by incrementing the latest sequence number and formatting
   *       it according to the {@link #TRANCHE_ID_FORMAT}.
   *   <li>Logs the current and next tranche file names for the data hall.
   * </ol>
   *
   * @param namespace the namespace of the Object Storage bucket
   * @param bucket the name of the Object Storage bucket
   * @param client the {@link ObjectStorageClient} instance used to interact with Object Storage
   * @param dataHallName the name of the data hall for which to generate the next tranche ID
   * @return the next tranche ID for the given data hall
   * @throws IllegalArgumentException if the data hall name is not in the expected format
   */
  public static String getNextTrancheIdForDataHall(
      String namespace, String bucket, ObjectStorageClient client, String dataHallName) {
    ObjectStoragePaginators objectStoragePaginator = new ObjectStoragePaginators(client);
    // Validate supplied data hall format
    String formattedDataHall = dataHallName.trim().toLowerCase().replace('.', '_');
    if (!formattedDataHall.matches("^[a-z0-9]+_[0-9]+$")) {
      throw new IllegalArgumentException(
          "Invalid data hall name: " + dataHallName + ". Expected format: abl15.1");
    }

    ListObjectsRequest listObjectsRequest =
        ListObjectsRequest.builder()
            .prefix("tranches/")
            .bucketName(bucket)
            .namespaceName(namespace)
            .fields("name,timeCreated")
            .build();

    Pattern existingTrancheIdPattern = Pattern.compile(".*_(\\d+)_[a-z0-9]+_\\d+\\.json$");
    AtomicReference<String> latestTrancheFileName = new AtomicReference<>("");
    AtomicInteger latestSequenceForDataHall = new AtomicInteger(0);
    objectStoragePaginator
        .listObjectsRecordIterator(listObjectsRequest)
        .forEach(
            record -> {
              if (record.getName().endsWith(formattedDataHall + ".json")) {
                Matcher matcher = existingTrancheIdPattern.matcher(record.getName());
                if (matcher.find()) {
                  int sequence = Integer.parseInt(matcher.group(1));
                  if (sequence > latestSequenceForDataHall.get()) {
                    latestSequenceForDataHall.set(sequence);
                    latestTrancheFileName.set(record.getName());
                  }
                } else {
                  LOG.debug(
                      "File '{}' does not match expected pattern {}",
                      record.getName(),
                      existingTrancheIdPattern.pattern());
                }
              }
            });

    String trancheId =
        TRANCHE_ID_FORMAT.formatted(latestSequenceForDataHall.incrementAndGet(), formattedDataHall);
    LOG.info(
        "Current tranche file for data hall: {}. Next tranche ID for data hall {} : {}. ",
        latestTrancheFileName.get(),
        formattedDataHall,
        trancheId);
    return trancheId;
  }

  public static Instant findOldestHandoverInitRackTime(
      DcmsClient client, String region, Map<String, String> rackPositionBySerial) {
    LOG.info("Looking for oldest rack for which handover initiation/validation was completed");

    Instant oldestHandoverInitRackTime = null;
    String oldestRp = null;
    String oldestSrl = null;
    for (Map.Entry<String, String> entry : rackPositionBySerial.entrySet()) {

      String rackSerial = entry.getKey();
      String rackPosition = entry.getValue();

      // Fetch all handover validation attestations for rack
      RackInstanceSummary rackInstance =
          getInServiceValidatedRackInstanceForSerial(client, region, rackSerial);
      AssetAttestationDetails initAttestation =
          getAttestationForAsset(
              client, region, rackInstance.getAssetId(), HANDOVER_INITIATED_ATTESTATION_NAME);
      AssetAttestationDetails repairAttestation =
          getAttestationForAsset(
              client, region, rackInstance.getAssetId(), REPAIR_HANDOVER_READY_ATTESTATION_NAME);
      AssetAttestationDetails burninAttestation =
          getAttestationForAsset(
              client,
              region,
              rackInstance.getAssetId(),
              BURNINATOR_HANDOVER_READY_ATTESTATION_NAME);

      // Get the latest attestation of these (this will act as the "rackValidationCompleteTime")
      Instant rackValidationCompleteTime =
          Collections.max(
                  Arrays.asList(
                      initAttestation.getTimeCreated(),
                      repairAttestation.getTimeCreated(),
                      burninAttestation.getTimeCreated()))
              .toInstant();
      LOG.info(
          "Got rackValidationCompleteTime for rack [{}-{}]: {}",
          rackPosition,
          rackSerial,
          rackValidationCompleteTime);

      // Determine the oldest of the init times of all racks
      if (oldestHandoverInitRackTime == null
          || rackValidationCompleteTime.isBefore(oldestHandoverInitRackTime)) {
        oldestHandoverInitRackTime = rackValidationCompleteTime;
        oldestRp = rackPosition;
        oldestSrl = rackSerial;
      }
    }

    LOG.info(
        "Rack [{}-{}] has oldest handover init time of: {}",
        oldestRp,
        oldestSrl,
        oldestHandoverInitRackTime);
    return oldestHandoverInitRackTime;
  }

  public static Optional<String> findLatestObjectWithPrefix(
      String namespace, String bucket, String basePrefix, ObjectStorageClient objectStorageClient) {

    LOG.info("Looking for latest file in object storage with prefix {}", basePrefix);
    ListObjectsRequest listObjectsRequest =
        ListObjectsRequest.builder()
            .prefix(basePrefix)
            .bucketName(bucket)
            .namespaceName(namespace)
            .fields("name,timeCreated")
            .build();
    ListObjectsResponse response = objectStorageClient.listObjects(listObjectsRequest);
    LOG.info("Found {} Objects", response.getListObjects().getObjects().size());

    Optional<ObjectSummary> latestObject =
        response.getListObjects().getObjects().stream()
            .max(Comparator.comparing(obj -> obj.getTimeCreated().getTime()));

    return latestObject.map(ObjectSummary::getName);
  }

  public static File getTrancheManifestFile(File trancheFolder, String trancheId) {
    return new File(trancheFolder, String.format("%s.json", trancheId).toLowerCase(Locale.ROOT));
  }

  public static Optional<AssetAttestationSummary> getCryptoAttestation(
      DcmsClient client, String region, String assetId) {
    LOG.info("Fetching crypto attestations for asset: {}", assetId);

    // There should only be one crypto attestation for a server, set limit to 1
    ListAssetAttestationsResponse response =
        client.listAssetAttestations(
            ListAssetAttestationsRequest.builder()
                .region(region)
                .assetId(assetId)
                .name(CRYPTO_ATTESTATION_NAME)
                .limit(1)
                .build());

    if (response.getAssetAttestationCollection().getAssetAttestations().isEmpty()) {
      return Optional.empty();
    }

    LOG.info(
        "Found crypto attestation for asset {}: {}",
        assetId,
        response.getAssetAttestationCollection().getAssetAttestations());

    return Optional.of(response.getAssetAttestationCollection().getAssetAttestations().get(0));
  }

  /**
   * Clears the base path by deleting all files and subdirectories within it.
   *
   * <p>This method uses Files.walk to traverse the directory tree rooted at basePath and deletes
   * each file and directory in reverse order to avoid {@link
   * java.nio.file.DirectoryNotEmptyException}.
   */
  public static void clearBasePath(Path basePath) {
    if (!Files.exists(basePath)) {
      return;
    }
    LOG.info("Deleting all files in base path {}", basePath);
    try (Stream<Path> pathFiles = Files.walk(basePath)) {
      pathFiles
          .sorted((p1, p2) -> p2.compareTo(p1)) // reverse order to delete files before directories
          .forEach(
              p -> {
                try {
                  Files.delete(p);
                } catch (IOException e) {
                  LOG.error("Error deleting file in base path: {} ", p, e);
                }
              });
    } catch (IOException e) {
      LOG.error("Error deleting base path", e);
    }
  }

  public static RackInstanceSummary getInServiceValidatedRackInstanceForSerial(
      DcmsClient client, String region, String serialNumber) {
    return client
        .listRackInstances(
            ListRackInstancesRequest.builder()
                .region(region)
                .lifecycleState(RackInstanceLifecycleState.InService)
                .lifecycleSubState(RackInstanceLifecycleSubState.Validated)
                .serialNumber(serialNumber)
                .build())
        .getRackInstanceCollection()
        .getItems()
        .stream()
        .findFirst()
        .orElseThrow(
            () ->
                new IllegalArgumentException(
                    "Could not find IN_SERVICE/VALIDATED rack with serial " + serialNumber));
  }

  public static RackModelDetails getRackModelForRackInstance(
      DcmsClient client, String region, RackInstanceSummary rackInstance) {
    return client
        .getRackModel(
            GetRackModelRequest.builder()
                .rackModelId(rackInstance.getRackModelId())
                .region(region)
                .build())
        .getRackModelDetails();
  }

  public static RackPositionDetails getRackPositionDetailsForRackModel(
      DcmsClient client, RackModelDetails model) {

    return client
        .getRackPosition(
            GetRackPositionRequest.builder().rackPositionId(model.getRackPositionId()).build())
        .getRackPositionDetails();
  }

  public static BuildingDetails getBuildingForRackPosition(
      DcmsClient client, RackPositionDetails position) {

    return client
        .getBuilding(GetBuildingRequest.builder().buildingId(position.getBuildingId()).build())
        .getBuildingDetails();
  }

  public static Room getRoomForRackPosition(DcmsClient client, RackPositionDetails position) {
    return client.getRoom(GetRoomRequest.builder().roomId(position.getRoomId()).build()).getRoom();
  }

  /**
   * Retrieves a list of racks that are ready for validation.
   *
   * <p>This method performs the following steps:
   *
   * <ol>
   *   <li>Fetches a list of rack instances in the {@code IN_SERVICE} lifecycle state and {@code
   *       VALIDATED} lifecycle sub-state.
   *   <li>Filters out racks that are not E5 or GB200 platforms.
   *   <li>Filters out racks that already have a {@code HANDOVER_INITIATED} or {@code
   *       HANDOVER_READY} attestation.
   *   <li>For each remaining rack, retrieves additional details such as rack number, building name,
   *       and data hall name.
   *   <li>Returns a list of {@link ReadyRackDetails} objects containing the relevant details for
   *       racks that are ready for validation.
   * </ol>
   *
   * @param dcmsClient the DcmsClient instance used to interact with the DCMS service
   * @param region the region where the racks are located
   * @return a list of {@link ReadyRackDetails} objects representing racks that are ready for
   *     validation
   */
  public static List<ReadyRackDetails> getRacksReadyForValidation(
      DcmsClient dcmsClient, String region) {

    //  Get rack instances in VALIDATED sub-state. Ignore ones with HANDOVER_READY attestation.
    List<RackInstanceSummary> rackInstances =
        dcmsClient
            .listRackInstances(
                ListRackInstancesRequest.builder()
                    .region(region)
                    .lifecycleState(RackInstanceLifecycleState.InService)
                    .lifecycleSubState(RackInstanceLifecycleSubState.Validated)
                    .limit(LIST_LIMIT)
                    .build())
            .getRackInstanceCollection()
            .getItems();
    LOG.info("Found {} rack instances in IN_SERVICE/VALIDATED state", rackInstances.size());

    CountDownLatch countDownLatch = new CountDownLatch(rackInstances.size());
    List<ReadyRackDetails> rackDetails = new ArrayList<>();
    for (RackInstanceSummary rackInstance : rackInstances) {
      // Check if racks are ready for validation in parallel
      DATA_HANDOVER_EXECUTOR.submit(
          () -> {
            try {
              RackModelDetails rackModel =
                  getRackModelForRackInstance(dcmsClient, region, rackInstance);

              // Filter out racks that are not E5 or GB200s
              Optional<HandoverRackPlatform> rackPlatform =
                  getRackPlatformForRackModel(rackModel.getRackPlatform());
              if (rackPlatform.isEmpty()) {
                LOG.debug(
                    "Skipping rack {} with unsupported rack platform: {}",
                    rackInstance.getSerialNumber(),
                    rackModel.getRackPlatform());
                return;
              }

              // Filter out any racks which have the Handover_Initiated attestation
              if (!dcmsClient
                  .listAssetAttestations(
                      ListAssetAttestationsRequest.builder()
                          .region(region)
                          .assetId(rackInstance.getAssetId())
                          .name(HANDOVER_INITIATED_ATTESTATION_NAME)
                          .limit(1)
                          .build())
                  .getAssetAttestationCollection()
                  .getAssetAttestations()
                  .isEmpty()) {
                LOG.debug(
                    "Skipping rack {} as it already has Handover_Initiated attestation",
                    rackInstance.getSerialNumber());
                return;
              }

              // Filter out any racks which have the Handover_Ready attestation
              if (!dcmsClient
                  .listAssetAttestations(
                      ListAssetAttestationsRequest.builder()
                          .region(region)
                          .assetId(rackInstance.getAssetId())
                          .name(HANDOVER_READY_ATTESTATION_NAME)
                          .limit(1)
                          .build())
                  .getAssetAttestationCollection()
                  .getAssetAttestations()
                  .isEmpty()) {
                LOG.debug(
                    "Skipping rack {} as it already has Handover_Ready attestation",
                    rackInstance.getSerialNumber());
                return;
              }

              // If not filtered out yet, this rack is ready for validation. Get details about the
              // rack to
              // supply to handover tool
              LOG.info(
                  "[ Rack = {} ] is ready for handover. Getting position details for rack...",
                  rackInstance.getSerialNumber());
              RackPositionDetails rackPositionDetails =
                  getRackPositionDetailsForRackModel(dcmsClient, rackModel);

              String rackNumber = rackPositionDetails.getRackNumber();
              String building =
                  getBuildingForRackPosition(dcmsClient, rackPositionDetails).getName();
              String dataHall = getRoomForRackPosition(dcmsClient, rackPositionDetails).getName();

              LOG.debug(
                  "Rack: {}, Building: {}, Room (Data Hall): {}",
                  rackInstance.getSerialNumber(),
                  building,
                  dataHall);
              rackDetails.add(
                  new ReadyRackDetails(
                      rackInstance, rackNumber, rackPlatform.get(), building, dataHall));
            } catch (Exception ex) {
              LOG.error(
                  "Encountered exception when checking if rack {} is ready for handover",
                  rackInstance.getSerialNumber(),
                  ex);
            } finally {
              countDownLatch.countDown();
            }
          });
    }

    // Wait for all rack instances to be processed
    try {
      countDownLatch.await();
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt(); // Restore interrupted status
      throw new RuntimeException("Failed to wait for rack validation readiness check", e);
    }

    LOG.info(
        "Found {} racks ready for validation: [{}] ",
        rackDetails.size(),
        rackDetails.stream()
            .map(ReadyRackDetails::rackInstance)
            .map(RackInstanceSummary::getSerialNumber)
            .collect(Collectors.joining(", ")));

    return rackDetails;
  }

  public static List<ReadyRackDetails> getRacksReadyForHandover(
      DcmsClient dcmsClient, String region, String metricPrefix) {

    //  Get rack instances in VALIDATED sub-state. Ignore ones with HANDOVER_READY attestation.
    List<RackInstanceSummary> rackInstances =
        dcmsClient
            .listRackInstances(
                ListRackInstancesRequest.builder()
                    .region(region)
                    .lifecycleState(RackInstanceLifecycleState.InService)
                    .lifecycleSubState(RackInstanceLifecycleSubState.Validated)
                    .limit(LIST_LIMIT)
                    .build())
            .getRackInstanceCollection()
            .getItems();
    LOG.info("Found {} rack instances in IN_SERVICE/VALIDATED state", rackInstances.size());

    CountDownLatch countDownLatch = new CountDownLatch(rackInstances.size());
    Queue<String> repairAwaitingRackSerials = new ConcurrentLinkedQueue<>();
    Queue<String> burninAwaitingRackSerials = new ConcurrentLinkedQueue<>();
    List<ReadyRackDetails> rackDetails = new ArrayList<>();
    for (RackInstanceSummary rackInstance : rackInstances) {
      // Check if racks are ready for validation in parallel
      DATA_HANDOVER_EXECUTOR.submit(
          () -> {
            try {
              // Filter out any racks which have the Handover_Ready attestation
              if (!dcmsClient
                  .listAssetAttestations(
                      ListAssetAttestationsRequest.builder()
                          .region(region)
                          .assetId(rackInstance.getAssetId())
                          .name(HANDOVER_READY_ATTESTATION_NAME)
                          .limit(1)
                          .build())
                  .getAssetAttestationCollection()
                  .getAssetAttestations()
                  .isEmpty()) {
                LOG.debug(
                    "Skipping rack {} as it already has Handover_Ready attestation",
                    rackInstance.getSerialNumber());
                return;
              }

              // Filter out any racks which don't have the Handover_Initiated attestation
              if (dcmsClient
                  .listAssetAttestations(
                      ListAssetAttestationsRequest.builder()
                          .region(region)
                          .assetId(rackInstance.getAssetId())
                          .name(HANDOVER_INITIATED_ATTESTATION_NAME)
                          .limit(1)
                          .build())
                  .getAssetAttestationCollection()
                  .getAssetAttestations()
                  .isEmpty()) {
                LOG.debug(
                    "Skipping rack {} as it does not have Handover_Initiated attestation",
                    rackInstance.getSerialNumber());
                return;
              }

              boolean isRepairAttPresent = true;
              // Filter out any racks which don't have the Repair_Handover_Ready attestation
              if (dcmsClient
                  .listAssetAttestations(
                      ListAssetAttestationsRequest.builder()
                          .region(region)
                          .assetId(rackInstance.getAssetId())
                          .name(REPAIR_HANDOVER_READY_ATTESTATION_NAME)
                          .limit(1)
                          .build())
                  .getAssetAttestationCollection()
                  .getAssetAttestations()
                  .isEmpty()) {
                isRepairAttPresent = false;
                repairAwaitingRackSerials.add(rackInstance.getSerialNumber());
                LOG.debug(
                    "Skipping rack {} as it does not have Repair_Handover_Ready attestation",
                    rackInstance.getSerialNumber());
              }

              boolean isBurninAttPresent = true;
              // Filter out any racks which don't have the Burninator_Handover_Ready attestation
              if (dcmsClient
                  .listAssetAttestations(
                      ListAssetAttestationsRequest.builder()
                          .region(region)
                          .assetId(rackInstance.getAssetId())
                          .name(BURNINATOR_HANDOVER_READY_ATTESTATION_NAME)
                          .limit(1)
                          .build())
                  .getAssetAttestationCollection()
                  .getAssetAttestations()
                  .isEmpty()) {
                isBurninAttPresent = false;
                burninAwaitingRackSerials.add(rackInstance.getSerialNumber());
                LOG.debug(
                    "Skipping rack {} as it does not have Burninator_Handover_Ready attestation",
                    rackInstance.getSerialNumber());
              }

              if (!isRepairAttPresent || !isBurninAttPresent) {
                // Skip rack
                return;
              }

              // If not filtered out yet, this rack is ready for handover. Get details about the
              // rack to supply to handover tool
              LOG.info(
                  "[ Rack = {} ] is ready for handover. Getting position details for rack...",
                  rackInstance.getSerialNumber());

              RackModelDetails rackModel =
                  getRackModelForRackInstance(dcmsClient, region, rackInstance);
              Optional<HandoverRackPlatform> rackPlatform =
                  getRackPlatformForRackModel(rackModel.getRackPlatform());
              if (rackPlatform.isEmpty()) {
                LOG.debug(
                    "Skipping rack {} with unsupported rack platform: {}",
                    rackInstance.getSerialNumber(),
                    rackModel.getRackPlatform());
                return;
              }

              RackPositionDetails rackPositionDetails =
                  getRackPositionDetailsForRackModel(dcmsClient, rackModel);

              String rackNumber = rackPositionDetails.getRackNumber();
              String building =
                  getBuildingForRackPosition(dcmsClient, rackPositionDetails).getName();
              String dataHall = getRoomForRackPosition(dcmsClient, rackPositionDetails).getName();

              LOG.debug(
                  "Rack: {}, Building: {}, Room (Data Hall): {}",
                  rackInstance.getSerialNumber(),
                  building,
                  dataHall);
              rackDetails.add(
                  new ReadyRackDetails(
                      rackInstance, rackNumber, rackPlatform.get(), building, dataHall));
            } catch (Exception ex) {
              LOG.error(
                  "Encountered exception when checking if rack {} is ready for handover",
                  rackInstance.getSerialNumber(),
                  ex);
            } finally {
              countDownLatch.countDown();
            }
          });
    }

    // Wait for all rack instances to be processed
    try {
      countDownLatch.await();
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt(); // Restore interrupted status
      throw new RuntimeException("Failed to wait for rack handover readiness check", e);
    }

    // Emit metrics for repair/burnin awaiting racks
    emitAwaitingRackSerialsMetric(
        String.format("%s.%s", metricPrefix, AWAITING_REPAIR_RACKS_METRIC_NAME),
        repairAwaitingRackSerials);
    emitAwaitingRackSerialsMetric(
        String.format("%s.%s", metricPrefix, AWAITING_BURNIN_RACKS_METRIC_NAME),
        burninAwaitingRackSerials);

    LOG.info(
        "Found {} racks ready for handover: [{}] ",
        rackDetails.size(),
        rackDetails.stream()
            .map(ReadyRackDetails::rackInstance)
            .map(RackInstanceSummary::getSerialNumber)
            .collect(Collectors.joining(", ")));

    return rackDetails;
  }

  private static void emitAwaitingRackSerialsMetric(String name, Queue<String> rackSerials) {
    DataHandoverUtils.emitCountMetric(
        name, Map.of(RACK_SERIALS_DIM_NAME, String.join(",", rackSerials)), rackSerials.size());
  }

  public static AssetAttestationDetails getAttestationForAsset(
      DcmsClient client, String region, String assetId, String attestationName) {
    AssetAttestationSummary attestationSummary =
        client
            .listAssetAttestations(
                ListAssetAttestationsRequest.builder()
                    .region(region)
                    .assetId(assetId)
                    .name(attestationName)
                    .limit(1)
                    .build())
            .getAssetAttestationCollection()
            .getAssetAttestations()
            .stream()
            .findFirst()
            .orElseThrow(
                () ->
                    new IllegalArgumentException(
                        String.format(
                            "No attestation found for asset: %s and name: %s",
                            assetId, attestationName)));

    return client
        .getAssetAttestation(
            GetAssetAttestationRequest.builder()
                .region(region)
                .assetAttestationId(attestationSummary.getId())
                .build())
        .getAssetAttestationDetails();
  }

  public static Map<BuildingDataHall, RackPositionsAndPlatforms> groupReadyRacksByLocation(
      List<ReadyRackDetails> readyRacks) {
    return Optional.of(readyRacks).orElse(List.of()).stream()
        .collect(
            Collectors.groupingBy(
                record -> new BuildingDataHall(record.buildingName(), record.dataHallName()),
                Collectors.collectingAndThen(
                    Collectors.toList(),
                    list ->
                        new RackPositionsAndPlatforms(
                            list.stream()
                                .map(ReadyRackDetails::rackNumber)
                                .collect(Collectors.toList()),
                            list.stream()
                                .map(ReadyRackDetails::rackPlatform)
                                .collect(Collectors.toSet())))));
  }

  /**
   * Dynamically discovers the rack platforms for a given list of rack positions by querying the
   * DCMS client. It retrieves the rack model for each position, determines the corresponding rack
   * platform, and returns a list of unique rack platforms.
   *
   * @param dcmsClient the DCMS client instance used to interact with the DCMS service
   * @param rackPositions a list of rack positions to discover the platforms for
   * @return a list of unique {@link HandoverRackPlatform} values corresponding to the provided rack
   *     positions
   * @throws IllegalArgumentException if a rack position has an unsupported platform
   */
  public static List<HandoverRackPlatform> discoverRackPlatforms(
      DcmsClient dcmsClient, String region, List<String> rackPositions, String buildingName) {
    Set<HandoverRackPlatform> platforms = new HashSet<>();
    BuildingSummary building = getBuildingByName(dcmsClient, buildingName);
    for (String rackPosition : rackPositions) {
      RackPositionDetails rackPositionDetails =
          getRackPositionByRackNumberAndBuildingId(dcmsClient, rackPosition, building.getId());
      RackModelSummary rackModel =
          DataHandoverUtils.getRackModelForRackNumberAndRoomId(
              dcmsClient, region, rackPosition, rackPositionDetails.getRoomId());
      HandoverRackPlatform platform =
          DataHandoverUtils.getRackPlatformForRackModel(rackModel.getRackPlatform())
              .orElseThrow(
                  () ->
                      new IllegalArgumentException(
                          "Rack position"
                              + rackPosition
                              + " has an unsupported platform: "
                              + rackModel.getRackPlatform()));
      LOG.debug("Retrieved rack platform for position {}: {}", rackPosition, platform);
      platforms.add(platform);
    }

    return new ArrayList<>(platforms);
  }

  public static RackModelSummary getRackModelForRackNumberAndRoomId(
      DcmsClient dcmsClient, String region, String rackNumber, String roomId) {
    return dcmsClient
        .listRackModels(
            ListRackModelsRequest.builder()
                .region(region)
                .rackNumber(rackNumber)
                .roomId(roomId)
                .build())
        .getRackModelCollection()
        .getItems()
        .stream()
        .filter(model -> model.getLifecycleState() != Deleted)
        .findFirst()
        .orElseThrow(
            () ->
                new IllegalStateException(
                    "Unable to find Rack Model for Rack number: " + rackNumber));
  }

  public static Optional<HandoverRackPlatform> getRackPlatformForRackModel(
      String rackPlatformName) {
    if (rackPlatformName.contains(GB200_PLATFORM)) {
      return Optional.of(HandoverRackPlatform.GB200);
    } else if (rackPlatformName.contains(E5_PLATFORM)) {
      return Optional.of(HandoverRackPlatform.E5);
    } else {
      return Optional.empty();
    }
  }

  public static Map<String, String> getUploadedRackSerialsToTrancheMapForDataHall(
      ObjectStorageClient objectStorageClient,
      String namespace,
      String bucket,
      String dataHallName,
      String metricsPrefix) {
    ObjectStoragePaginators objectStoragePaginator =
        new ObjectStoragePaginators(objectStorageClient);
    ListObjectsRequest listObjectsRequest =
        ListObjectsRequest.builder()
            .prefix("tranches/")
            .bucketName(bucket)
            .namespaceName(namespace)
            .fields("name,timeCreated")
            .build();

    String formattedDataHall = dataHallName.trim().toLowerCase().replace('.', '_');

    AtomicReference<Date> lastUploadedTranche = new AtomicReference<>(null);
    Map<String, String> rackSerialToTranche = new HashMap<>();
    objectStoragePaginator
        .listObjectsRecordIterator(listObjectsRequest)
        .forEach(
            tranche -> {
              if (tranche.getName().endsWith(formattedDataHall + ".json")) {
                // Download tranche and deserialize
                byte[] manifestBytes =
                    downloadFromObjectStorage(
                        objectStorageClient, namespace, bucket, tranche.getName(), false, null);
                TrancheManifest manifest =
                    deSerializeTrancheManifest(new String(manifestBytes, StandardCharsets.UTF_8));

                // Collect rack serials from tranche
                List<String> rackSerials =
                    manifest.racks().stream().map(TrancheManifestRackData::serialNumber).toList();

                rackSerials.forEach(
                    serial -> rackSerialToTranche.put(serial, manifest.trancheId()));

                // Get latest tranche
                Date current = lastUploadedTranche.get();
                if (current == null || tranche.getTimeCreated().after(current)) {
                  lastUploadedTranche.set(tranche.getTimeCreated());
                }
              }
            });

    Date lastUploadedTrancheDate = lastUploadedTranche.get();
    if (lastUploadedTrancheDate != null) {
      // Emit metric to indicate minutes passed since last tranche upload
      int minutesElapsed =
          (int) Duration.between(lastUploadedTrancheDate.toInstant(), Instant.now()).toMinutes();
      DataHandoverUtils.emitCountMetric(
          String.format("%s.%s", metricsPrefix, MINUTES_SINCE_LAST_TRANCHE_METRIC_NAME),
          Map.of(DATA_HALL_DIM_NAME, dataHallName),
          minutesElapsed);
    }

    return rackSerialToTranche;
  }

  public static RackPositionDetails getRackPositionForRackSerial(
      DcmsClient client, String rackSerial, String region) {
    RackInstanceSummary rackInstance =
        client
            .listRackInstances(
                ListRackInstancesRequest.builder().serialNumber(rackSerial).region(region).build())
            .getRackInstanceCollection()
            .getItems()
            .stream()
            .findFirst()
            .orElseThrow(
                () ->
                    new IllegalArgumentException(
                        "No Rack Instance found at supplied serialNumber and region"));

    RackModelDetails rackModel = getRackModelForRackInstance(client, region, rackInstance);

    return getRackPositionDetailsForRackModel(client, rackModel);
  }

  public static TrancheOtsTicketMetadata buildOtsTicketMetadata(
      DcmsClient dcmsClient,
      RepairOrderClient repairOrderClient,
      String region,
      String namespace,
      String bucket,
      List<String> rackPositions,
      File trancheManifestFile)
      throws IOException {
    TrancheManifest manifestData =
        DataHandoverUtils.deSerializeTrancheManifest(
            Files.readString(trancheManifestFile.toPath(), StandardCharsets.UTF_8));
    BuildingSummary building =
        DataHandoverUtils.getBuildingByName(dcmsClient, manifestData.buildingName());

    List<TrancheOtsTicketMetadata.TrancheRackMetadata> handoverDetails =
        rackPositions.stream()
            .map(
                pos ->
                    DataHandoverUtils.getHandoverRackDetails(
                        dcmsClient, region, pos, building.getId()))
            .map(
                details -> {
                  String serialNumber = details.rackInstance().getSerialNumber();

                  TrancheManifestRackData rackData =
                      manifestData.racks().stream()
                          .filter(rack -> rack.serialNumber().equals(serialNumber))
                          .findFirst()
                          .orElseThrow(
                              () ->
                                  new IllegalArgumentException(
                                      "Could not find rack in tranche data: " + serialNumber));
                  Map<String, String> failedServerSerialToRepairOrder =
                      rackData.repairOrders().stream()
                          .map(
                              order -> {
                                RepairOrder repairOrder =
                                    repairOrderClient
                                        .getInternalRepairOrder(
                                            GetInternalRepairOrderRequest.builder()
                                                .repairOrderId(order)
                                                .build())
                                        .getRepairOrder();
                                String replacementSerialNumber =
                                    DataHandoverUtils.getReplacementSerialNumberIfExists(
                                        repairOrder);
                                return Pair.of(replacementSerialNumber, order);
                              })
                          .collect(Collectors.toMap(Pair::getKey, Pair::getValue));

                  return new TrancheOtsTicketMetadata.TrancheRackMetadata(
                      details.rackModel().getRackPlatform(),
                      details.rackNumber(),
                      serialNumber,
                      details.rackPosition().getShortRackId(),
                      failedServerSerialToRepairOrder);
                })
            .toList();

    return TrancheOtsTicketMetadataBuilder.builder()
        .objectStorageNamespace(namespace)
        .objectStorageBucket(bucket)
        .trancheId(manifestData.trancheId())
        .racks(handoverDetails)
        .trancheFileContents(DataHandoverUtils.prettySerializeTrancheManifest(manifestData))
        .build();
  }

  public static void emitFaultMetricsForEachTask(
      String metricNamePrefix, List<TaskResult> taskResults, Map<String, String> dimensions) {
    // Emit metrics
    for (TaskResult taskResult : taskResults) {
      MetricName taskFaultMetric =
          Metrics.name(String.format("%s.%s.Fault", metricNamePrefix, taskResult.taskName()))
              .withDimensions(dimensions);
      int faultMetricValue = 0;
      if (!taskResult.isCompletedSuccessfully()) {
        faultMetricValue = 1;
      }
      Metrics.emit(taskFaultMetric, faultMetricValue);
    }
  }

  public static void emitCountMetric(String name, Map<String, String> dimensions, int count) {
    Map<String, String> safeDims = dimensions != null ? dimensions : Collections.emptyMap();
    MetricName countMetric = Metrics.name(String.format("%s.Count", name)).withDimensions(safeDims);
    Metrics.emit(countMetric, count);
  }

  public static void emitResultMetric(String name, Map<String, String> dimensions) {
    Map<String, String> safeDims = dimensions != null ? dimensions : Collections.emptyMap();
    MetricName countMetric =
        Metrics.name(String.format("%s.Result", name)).withDimensions(safeDims);
    Metrics.emit(countMetric, 1);
  }

  /**
   * Retrieves the replacement serial number for a given repair order if it exists.
   *
   * <p>This method checks the first action of the repair order for a replacement. If a replacement
   * is found, its serial number is returned. If no replacement is found or if the repair order has
   * no actions, the device serial number associated with the repair order is returned.
   *
   * @param order the repair order to check for a replacement serial number
   * @return the replacement serial number if it exists, otherwise the device serial number of the
   *     repair order
   */
  public static String getReplacementSerialNumberIfExists(RepairOrder order) {
    return Optional.ofNullable(order.getActions())
        .filter(actions -> !actions.isEmpty())
        .map(actions -> actions.get(0).getReplacements())
        .filter(replacements -> !replacements.isEmpty())
        .map(replacements -> replacements.get(0).getReplacement().getValue())
        .orElse(order.getDeviceSerialNumber());
  }

  /**
   * Extracts and returns a set of replacement serial numbers from a list of repair orders.
   *
   * <p>For each repair order, this method attempts to retrieve the replacement serial number by
   * checking the first action's first replacement (if available). If no replacement is found, it
   * defaults to the device serial number of the repair order.
   *
   * <p>The resulting serial numbers are collected into a set to eliminate duplicates.
   *
   * @param repairOrders the list of repair orders to extract replacement serial numbers from
   * @return a set of unique replacement serial numbers
   */
  public static Set<String> getReplacementSerialNumbers(List<RepairOrder> repairOrders) {
    return repairOrders.stream()
        .map(order -> getReplacementSerialNumberIfExists(order))
        .collect(Collectors.toSet());
  }
}
