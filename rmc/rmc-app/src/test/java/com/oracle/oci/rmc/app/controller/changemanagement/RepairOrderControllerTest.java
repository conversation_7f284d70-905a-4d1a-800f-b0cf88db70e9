package com.oracle.oci.rmc.app.controller.changemanagement;

import static com.oracle.oci.rmc.app.util.DcmsTestUtil.TEST_USER;
import static com.oracle.oci.rmc.changemanagement.api.service.ProbeService.YAML_MAPPER;
import static com.oracle.oci.rmc.changemanagement.api.service.ProbeService.log;
import static com.oracle.oci.rmc.changemanagement.impl.service.UnitTestOtsService.PROJECT_KEY;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.JsonNode;
import com.oracle.bmc.model.BmcException;
import com.oracle.oci.rmc.auditevent.dal.AuditEventRepository;
import com.oracle.oci.rmc.auditevent.model.AuditEventContext;
import com.oracle.oci.rmc.auditevent.service.AuditEventContextHolder;
import com.oracle.oci.rmc.catalog.api.model.entity.Part;
import com.oracle.oci.rmc.catalog.api.model.entity.PartCategory;
import com.oracle.oci.rmc.catalog.api.model.object.part.PartDetailsSource;
import com.oracle.oci.rmc.catalog.api.service.PartCategoryService;
import com.oracle.oci.rmc.catalog.api.service.PartService;
import com.oracle.oci.rmc.changemanagement.api.model.common.ChangeOrderType;
import com.oracle.oci.rmc.changemanagement.api.model.common.OtsTicket;
import com.oracle.oci.rmc.changemanagement.api.model.common.OtsTicketBuilder;
import com.oracle.oci.rmc.changemanagement.api.model.common.ProbeType;
import com.oracle.oci.rmc.changemanagement.api.model.common.TicketData;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeOrder;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ProbeResultDetails;
import com.oracle.oci.rmc.changemanagement.api.model.entity.TicketFieldEntity;
import com.oracle.oci.rmc.changemanagement.api.service.CoreOtsService;
import com.oracle.oci.rmc.changemanagement.api.service.NetworkRolePartsManagementService;
import com.oracle.oci.rmc.changemanagement.api.service.ProbeService;
import com.oracle.oci.rmc.changemanagement.impl.dal.AssetsToActionRepository;
import com.oracle.oci.rmc.changemanagement.impl.dal.ChangeManagementCleanup;
import com.oracle.oci.rmc.changemanagement.impl.dal.ChangeManagerCommandRepository;
import com.oracle.oci.rmc.changemanagement.impl.dal.ChangeOrderRepository;
import com.oracle.oci.rmc.changemanagement.impl.dal.TicketDataRepository;
import com.oracle.oci.rmc.changemanagement.impl.dal.TicketFieldRepository;
import com.oracle.oci.rmc.changemanagement.impl.service.DefaultChangeManagementService;
import com.oracle.oci.rmc.client.RepairOrderClient;
import com.oracle.oci.rmc.client.model.CancelRepairOrderDetails;
import com.oracle.oci.rmc.client.model.CloseInternalRepairOrderDetails;
import com.oracle.oci.rmc.client.model.CloseRepairOrderDetails;
import com.oracle.oci.rmc.client.model.CreateE5ServerRepairOrderDetails;
import com.oracle.oci.rmc.client.model.CreateInternalE5ServerRepairOrderDetails;
import com.oracle.oci.rmc.client.model.CreateInternalGb200PowerShelfRepairOrderDetails;
import com.oracle.oci.rmc.client.model.CreateInternalMultiRepairOrderDetails;
import com.oracle.oci.rmc.client.model.CreateInternalNetworkLeafSpineLinkRepairOrderDetails;
import com.oracle.oci.rmc.client.model.CreateInternalServerRepairOrderDetails;
import com.oracle.oci.rmc.client.model.CreateServerRepairOrderDetails;
import com.oracle.oci.rmc.client.model.InternalRepairOrderRevision;
import com.oracle.oci.rmc.client.model.InternalRepairOrderSourceType;
import com.oracle.oci.rmc.client.model.PrioritizeRepairOrderDetails;
import com.oracle.oci.rmc.client.model.RepairAction;
import com.oracle.oci.rmc.client.model.RepairOrder;
import com.oracle.oci.rmc.client.model.RepairOrderCollection;
import com.oracle.oci.rmc.client.model.RepairOrderRevision;
import com.oracle.oci.rmc.client.model.RepairOrderSummary;
import com.oracle.oci.rmc.client.model.RepairOrderType;
import com.oracle.oci.rmc.client.model.ServerRepairOrder;
import com.oracle.oci.rmc.client.model.SortOrder;
import com.oracle.oci.rmc.client.model.TicketField;
import com.oracle.oci.rmc.client.requests.ApproveInternalRepairOrderRequest;
import com.oracle.oci.rmc.client.requests.ApproveRepairOrderRequest;
import com.oracle.oci.rmc.client.requests.CancelRepairOrderRequest;
import com.oracle.oci.rmc.client.requests.CloseInternalRepairOrderRequest;
import com.oracle.oci.rmc.client.requests.CloseRepairOrderRequest;
import com.oracle.oci.rmc.client.requests.CreateInternalRepairOrderRequest;
import com.oracle.oci.rmc.client.requests.CreateRepairOrderRequest;
import com.oracle.oci.rmc.client.requests.FailRepairOrderRequest;
import com.oracle.oci.rmc.client.requests.GetRepairOrderRequest;
import com.oracle.oci.rmc.client.requests.ListInternalRepairOrdersRequest;
import com.oracle.oci.rmc.client.requests.ListRepairOrdersRequest;
import com.oracle.oci.rmc.client.requests.PrioritizeRepairOrderRequest;
import com.oracle.oci.rmc.client.requests.ReopenRepairOrderRequest;
import com.oracle.oci.rmc.client.requests.ReplyToRepairOrderRequest;
import com.oracle.oci.rmc.client.responses.GetRepairOrderResponse;
import com.oracle.oci.rmc.dcms.api.model.common.RackInstanceAccessLevel;
import com.oracle.oci.rmc.dcms.api.model.common.RackType;
import com.oracle.oci.rmc.dcms.api.model.entity.LinkRepairAssetDetails;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.Asset;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetDetailsDto;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetLifecycleState;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetRackLabel;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetSpecification;
import com.oracle.oci.rmc.dcms.api.service.AssetSpecificationService;
import com.oracle.oci.rmc.dcms.api.service.LinkRepairAssetService;
import com.oracle.oci.rmc.dcms.api.service.dto.AssetFilterBuilder;
import com.oracle.oci.rmc.dcms.api.service.inventory.AssetService;
import com.oracle.oci.rmc.dcms.impl.dal.testutil.DcmsRepositoryTestUtil;
import com.oracle.oci.rmc.model.RmcId;
import com.oracle.oci.rmc.model.RmcIdType;
import com.oracle.oci.sfw.micronaut.client.offline.OfflineAuthProperties;
import com.oracle.oci.sfw.micronaut.http.pagination.PageQuery;
import com.oracle.oci.sfw.micronaut.http.pagination.PaginatedList;
import io.micronaut.context.annotation.Requires;
import io.micronaut.test.annotation.MockBean;
import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import jakarta.inject.Inject;
import java.time.Instant;
import java.util.EnumSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.assertj.core.api.AutoCloseableSoftAssertions;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@OfflineAuthProperties
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@Requires(property = "database.type", value = "ORACLE")
@MicronautTest(transactional = false)
public class RepairOrderControllerTest {
  @Inject private final ChangeManagerCommandRepository changeManagerCommandRepository;
  @Inject private final ChangeOrderRepository changeOrderRepository;
  @Inject private final TicketDataRepository ticketDataRepository;
  @Inject private final TicketFieldRepository ticketFieldRepository;
  @Inject private final ProbeService probeService;
  @Inject private final AuditEventRepository auditEventRepository;
  @Inject private final AssetsToActionRepository assetsToActionRepository;
  @Inject private final CoreOtsService otsService;
  @Inject private final ChangeManagementCleanup cleanup;
  private final DcmsRepositoryTestUtil repositoryUtil;
  private final RepairOrderClient repairOrderClient;

  @Mock private AssetService mockedAssetService;
  @Mock private PartService mockedPartService;
  @Mock private PartCategoryService mockedPartCategoryService;
  @Mock private LinkRepairAssetService mockedLinkRepairAssetService;
  @Mock private AssetSpecificationService mockedAssetSpecificationService;
  @Mock private NetworkRolePartsManagementService mockedNetworkRolePartsManagementService;

  public static final String RACK_SERIAL_NUMBER = "RackSerialNumberTest";
  public static final String TEST_COMPARTMENT_ID = "testCompartmentId";
  public static final String TEST_REGION = "trg";
  public static List<String> LABELS = List.of("a", "b", "a");
  public static List<String> EXPECTED_LABELS = List.of("a", "b");
  public static final String PROBE_RESULT =
      """
        ---
        textKey: textValue
        quoteKey: some text with "a quote"
        intKey: 42
        booleanKey: true
        arrayKey:
        - 2
        - b
        - false
        objectKey:
          nestedKey: nestedValue
        """;
  public static final Object DIAGNOSTIC_PROBE_OUTPUT = diagnosticProbeOutputAsMap(PROBE_RESULT);
  public static final String DIAGNOSTIC_REPORT_OBJECT_STORAGE_URL =
      "https://example.com/diagnostic-report";
  public static final Boolean IS_DEVICE_INACCESSIBLE = false;

  public static List<TicketField> TICKET_FIELDS =
      List.of(
          TicketField.builder().name("component").build(),
          TicketField.builder().name("item").build());

  public static final Map<String, Object> TICKET_DATA =
      Map.of("number", 42, "text", "hello world", "list", List.of("a", 1, "c", true));
  @Inject private DefaultChangeManagementService defaultChangeManagementService;

  @MockBean(AssetService.class)
  AssetService mockedAssetService() {
    return mockedAssetService;
  }

  @MockBean(PartService.class)
  PartService mockedPartService() {
    return mockedPartService;
  }

  @MockBean(PartCategoryService.class)
  PartCategoryService mockedPartCategoryService() {
    return mockedPartCategoryService;
  }

  @MockBean(LinkRepairAssetService.class)
  LinkRepairAssetService mockedLinkRepairAssetService() {
    return mockedLinkRepairAssetService;
  }

  @MockBean(AssetSpecificationService.class)
  AssetSpecificationService mockAssetSpecificationService() {
    return mockedAssetSpecificationService;
  }

  @MockBean(NetworkRolePartsManagementService.class)
  NetworkRolePartsManagementService networkRolePartsManagementService() {
    return mockedNetworkRolePartsManagementService;
  }

  public RepairOrderControllerTest(
      ChangeManagerCommandRepository changeManagerCommandRepository,
      RepairOrderClient repairOrderClient,
      ChangeOrderRepository changeOrderRepository,
      TicketDataRepository ticketDataRepository,
      TicketFieldRepository ticketFieldRepository,
      ProbeService probeService,
      AuditEventRepository auditEventRepository,
      AssetsToActionRepository assetsToActionRepository,
      DcmsRepositoryTestUtil repositoryUtil,
      CoreOtsService otsService,
      ChangeManagementCleanup cleanup) {
    this.changeManagerCommandRepository = changeManagerCommandRepository;
    this.repairOrderClient = repairOrderClient;
    this.changeOrderRepository = changeOrderRepository;
    this.ticketDataRepository = ticketDataRepository;
    this.ticketFieldRepository = ticketFieldRepository;
    this.probeService = probeService;
    this.auditEventRepository = auditEventRepository;
    this.assetsToActionRepository = assetsToActionRepository;
    this.otsService = otsService;
    this.cleanup = cleanup;
    this.repositoryUtil = repositoryUtil;
  }

  @BeforeEach
  void setUp() {
    cleanup.execute();
    AuditEventContextHolder.setContext(new AuditEventContext(TEST_USER));
  }

  @AfterEach
  void tearDown() {
    cleanup.execute();
  }

  private void setupMockedServices(String serialNumber) {
    Part testPart = getPartData();
    PartCategory testPartCategory = new PartCategory(testPart.partCategoryId(), "SSD");
    Asset expectedAsset =
        new Asset(
            RmcId.generate(RmcIdType.Inventory.ASSET),
            "expectedRegionId",
            serialNumber,
            RmcId.generate(RmcIdType.Catalog.PART), // categoryPartId
            null, // locationId
            RmcId.generate(RmcIdType.DCMS.RACK_INSTANCE), // rackInstanceId
            null, // parentAssetId
            null, // reservationId
            null, // transferId
            null, // elevation
            null, // compartmentOcid
            null, // tagSlug
            null, // oracleTag
            null, // machineSerialNumber
            null, // isDisabled
            null, // additionalProperties
            null, // createdBy
            null, // updatedBy
            AssetLifecycleState.INSTALLED, // lifecycleState
            null, // subInventory
            null // burnInMacAddresses
            );
    when(mockedAssetService.listAssets(any(), any(), any()))
        .thenReturn(new PaginatedList<>(List.of(new AssetDetailsDto(expectedAsset))));
    when(mockedPartService.getPart(any())).thenReturn(testPart);
    when(mockedPartCategoryService.getPartCategory(any())).thenReturn(testPartCategory);
    AssetSpecification assetSpecification =
        getExpectedAssetSpecificationImpactedNodes(expectedAsset.id());
    when(mockedAssetSpecificationService.getAssetSpecification(any()))
        .thenReturn(Optional.of(assetSpecification));
    when(mockedAssetSpecificationService.getParentAssetSpecification(any()))
        .thenReturn(Optional.of(assetSpecification));
    when(mockedAssetSpecificationService.getServerAssetSpecificationInRack(any(), any()))
        .thenReturn(List.of(assetSpecification));
    Optional<AssetRackLabel> assetRackLabel =
        Optional.of(new AssetRackLabel(RACK_SERIAL_NUMBER, RackInstanceAccessLevel.REGULAR));
    when(mockedAssetSpecificationService.getAssetRackLabel(any())).thenReturn(assetRackLabel);
  }

  private AssetSpecification getExpectedAssetSpecificationImpactedNodes(RmcId assetId) {
    return new AssetSpecification(
        assetId,
        RmcId.generate(RmcIdType.Inventory.ASSET),
        "rack-number",
        RmcId.generate(RmcIdType.DCMS.RACK_POSITION),
        "elevation",
        "serial-number",
        RmcId.generate(RmcIdType.DCMS.RACK_INSTANCE),
        RmcId.generate(RmcIdType.Catalog.PART),
        "data-hall-name",
        "building-name",
        RmcId.generate(RmcIdType.Inventory.DATA_HALL),
        Instant.now(),
        Instant.now(),
        RackType.GPU);
  }

  @Test
  void testCreateRepairOrder() {
    String assetSerial = "testCreateRepairOrder";
    setupMockedServices(assetSerial);
    RepairOrder repairOrder = createTestRepairOrder(assetSerial, "Server issue", 1);
    assertNotNull(repairOrder.getId());
    List<RepairAction> repairActions = repairOrder.getActions();
    List<String> impactedComputeNodes =
        repairActions.stream()
            .flatMap(repairAction -> repairAction.getImpactedComputeNodes().stream())
            .collect(Collectors.toList());
    assertNotNull(impactedComputeNodes);
    assertEquals(1, impactedComputeNodes.size());
    assertEquals("Server issue", repairOrder.getDetails());
    assertEquals(assetSerial, repairOrder.getDeviceSerialNumber());
    assertEquals(1, repairOrder.getPriority());
    assertEquals(RepairOrder.LifecycleState.Accepted, repairOrder.getLifecycleState());

    GetRepairOrderResponse response =
        repairOrderClient.getRepairOrder(
            GetRepairOrderRequest.builder().repairOrderId(repairOrder.getId()).build());

    RepairOrder actual = response.getRepairOrder();
    assertThat(actual).isNotNull();
    assertThat(actual.getLabels()).isEqualTo(EXPECTED_LABELS);
  }

  @Test
  void testCreateE5RepairOrder() {
    String assetSerial = "testCreateE5RepairOrder";
    setupMockedServices(assetSerial);
    RepairOrder repairOrder = createTestE5ServerRepairOrder(assetSerial, "E5 server issue", 1);
    assertNotNull(repairOrder.getId());
    List<RepairAction> repairActions = repairOrder.getActions();
    List<String> impactedComputeNodes =
        repairActions.stream()
            .flatMap(repairAction -> repairAction.getImpactedComputeNodes().stream())
            .collect(Collectors.toList());
    assertNotNull(impactedComputeNodes);
    assertEquals(1, impactedComputeNodes.size());
    assertEquals("E5 server issue", repairOrder.getDetails());
    assertEquals(assetSerial, repairOrder.getDeviceSerialNumber());
    assertEquals(1, repairOrder.getPriority());
    assertEquals(RepairOrder.LifecycleState.Accepted, repairOrder.getLifecycleState());
  }

  @Test
  @Disabled("Mocks are not set up properly")
  void testCreateNetworkLeafSpineLinkRepairOrder() {
    LinkRepairAssetDetails sourceAsset =
        new LinkRepairAssetDetails(
            RmcId.generate(RmcIdType.DCMS.RACK_INSTANCE), // rackInstanceId
            RmcId.generate(RmcIdType.DCMS.RACK_POSITION), // rackPositionId
            "rack1", // rackNumber
            "dataHall1", // dataHall
            "SNSourceLeafSpine", // serialNumber
            "elevation1", // elevation
            "sourcePluggablePort", // pluggablePortId
            "sku1" // transceiverSku
            );
    LinkRepairAssetDetails destinationAsset =
        new LinkRepairAssetDetails(
            RmcId.generate(RmcIdType.DCMS.RACK_INSTANCE), // rackInstanceId
            RmcId.generate(RmcIdType.DCMS.RACK_POSITION), // rackPositionId
            "rack1", // rackNumber
            "dataHall1", // dataHall
            "SNDestinationLeafSpine", // serialNumber
            "elevation2", // elevation
            "destinationPluggablePort", // pluggablePortId
            "sku1" // transceiverSku
            );
    when(mockedLinkRepairAssetService.getLinkRepairAssetDetails(eq("interfaceA")))
        .thenReturn(sourceAsset);
    when(mockedLinkRepairAssetService.getLinkRepairAssetDetails(eq("interfaceB")))
        .thenReturn(destinationAsset);
    Part testPart = getPartData();
    PartCategory testPartCategory = new PartCategory(testPart.partCategoryId(), "SSD");
    when(mockedPartService.getPart(any())).thenReturn(testPart);
    when(mockedPartCategoryService.getPartCategory(any())).thenReturn(testPartCategory);
    when(mockedPartService.findPartsByOPNAndMPN(any(), any())).thenReturn(List.of(testPart));
    RmcId assetIdA = RmcId.generate(RmcIdType.Inventory.ASSET);
    Asset assetA =
        new Asset(
            assetIdA,
            "expectedRegionId",
            "SNSourceLeafSpine",
            testPart.id(), // categoryPartId
            null, // locationId
            null, // rackInstanceId
            null, // parentAssetId
            null, // reservationId
            null, // transferId
            null, // elevation
            null, // compartmentOcid
            null, // tagSlug
            null, // oracleTag
            null, // machineSerialNumber
            null, // isDisabled
            null, // additionalProperties
            null, // createdBy
            null, // updatedBy
            AssetLifecycleState.INSTALLED, // lifecycleState
            null, // subInventory
            null // burnInMacAddresses
            );
    RmcId assetIdB = RmcId.generate(RmcIdType.Inventory.ASSET);
    Asset assetB =
        new Asset(
            assetIdB,
            "expectedRegionId",
            "SNDestinationLeafSpine",
            testPart.id(), // categoryPartId
            null, // locationId
            null, // rackInstanceId
            null, // parentAssetId
            null, // reservationId
            null, // transferId
            null, // elevation
            null, // compartmentOcid
            null, // tagSlug
            null, // oracleTag
            null, // machineSerialNumber
            null, // isDisabled
            null, // additionalProperties
            null, // createdBy
            null, // updatedBy
            AssetLifecycleState.INSTALLED, // lifecycleState
            null, // subInventory
            null // burnInMacAddresses
            );
    when(mockedAssetService.listAssets(
            any(),
            eq(
                AssetFilterBuilder.builder()
                    .serialNumber("SNSourceLeafSpine")
                    .lifecycleState(
                        com.oracle.oci.rmc.client.model.AssetLifecycleState.Installed.getValue())
                    .build()),
            any()))
        .thenReturn(new PaginatedList<>(List.of(new AssetDetailsDto(assetA))));
    when(mockedAssetService.listAssets(
            any(),
            eq(
                AssetFilterBuilder.builder()
                    .serialNumber("SNDestinationLeafSpine")
                    .lifecycleState(
                        com.oracle.oci.rmc.client.model.AssetLifecycleState.Installed.getValue())
                    .build()),
            any()))
        .thenReturn(new PaginatedList<>(List.of(new AssetDetailsDto(assetB))));
    PartCategory partCategory = repositoryUtil.createPartCategory("Server System");
    repositoryUtil.createPart("sku1", "sku1", partCategory.id());
    when(mockedAssetSpecificationService.getAssetSpecification(eq(assetIdA)))
        .thenReturn(Optional.of(getExpectedAssetSpecificationImpactedNodes(assetIdA)));
    when(mockedAssetSpecificationService.getAssetSpecification(eq(assetIdB)))
        .thenReturn(Optional.of(getExpectedAssetSpecificationImpactedNodes(assetIdB)));
    RepairOrder repairOrder =
        repairOrderClient
            .createInternalRepairOrder(
                CreateInternalRepairOrderRequest.builder()
                    .createInternalRepairOrderDetails(
                        CreateInternalNetworkLeafSpineLinkRepairOrderDetails.builder()
                            .labels(LABELS)
                            .details("Network Leaf-Spine LinkedTickets issue")
                            .priority(2)
                            .interfaceA("interfaceA")
                            .interfaceB("interfaceB")
                            .logicalTargetA("logicalTargetA")
                            .logicalTargetB("logicalTargetB")
                            .source(InternalRepairOrderSourceType.Network)
                            .diagnosticReportObjectStorageUrl("http://www.example.com")
                            .isSharedWithCustomer(false)
                            .build())
                    .overrideCompartmentId(TEST_COMPARTMENT_ID)
                    .region(TEST_REGION)
                    .build())
            .getRepairOrder();
    assertNotNull(repairOrder.getId());
    assertEquals("Network Leaf-Spine LinkedTickets issue", repairOrder.getDetails());
    assertEquals(2, repairOrder.getPriority());
    assertEquals(RepairOrder.LifecycleState.Accepted, repairOrder.getLifecycleState());
    var repairOrderListByType =
        repairOrderClient
            .listInternalRepairOrders(
                ListInternalRepairOrdersRequest.builder()
                    .repairOrderId(repairOrder.getId())
                    .repairOrderType(RepairOrderType.NetworkLeafSpineLinkRepair.getValue())
                    .build())
            .getRepairOrderCollection();

    assertFalse(repairOrderListByType.getItems().isEmpty());
    assertEquals(1, repairOrderListByType.getItems().size());
    assertEquals(
        RepairOrderType.NetworkLeafSpineLinkRepair,
        repairOrderListByType.getItems().get(0).getRepairOrderType());
  }

  @Test
  void testCreateInternalRepairOrder() throws InterruptedException {
    String assetSerial = "testCreateInternalRepairOrder";
    setupMockedServices(assetSerial);
    RepairOrder internalRepairOrder =
        createTestInternalRepairOrder(
            assetSerial, "Internal server issue", 2, InternalRepairOrderSourceType.Network);
    assertNotNull(internalRepairOrder.getId());
    assertEquals("Internal server issue", internalRepairOrder.getDetails());
    assertEquals(assetSerial, internalRepairOrder.getDeviceSerialNumber());
    assertEquals(2, internalRepairOrder.getPriority());
    assertEquals(InternalRepairOrderSourceType.Network, internalRepairOrder.getSource());
    assertEquals(RepairOrder.LifecycleState.Accepted, internalRepairOrder.getLifecycleState());

    RmcId id = RmcId.fromString(internalRepairOrder.getId());
    ProbeResultDetails result =
        probeService.getProbeResultByChangeOrderId(id, ProbeType.DIAGNOSTIC);
    String actual = result.resultDetails();
    assertThat(actual).isEqualTo(PROBE_RESULT);

    OtsTicket actualTicket =
        pollUntil(
            () -> {
              ChangeOrder updated = defaultChangeManagementService.get(id);
              String ticketId = updated.ticketId();
              OtsTicket ticket = ticketId == null ? null : otsService.get(ticketId).orElse(null);
              return ticket;
            },
            Objects::nonNull);

    ChangeOrder actualChangeOrder = defaultChangeManagementService.get(id);
    assertThat(actualChangeOrder.ticketId()).isNotNull();
    assertThat(actualChangeOrder.ticketUrl()).isNotNull();
    assertThat(actualTicket).isNotNull();
    assertThat(actualChangeOrder.ticketId()).isEqualTo(actualTicket.ticketId());
  }

  @Test
  void testCreateInternalRepairOrderWithExistingOtsTicket() throws InterruptedException {
    String originalDescription = "description";

    OtsTicket ticket =
        otsService.createTicket(
            OtsTicketBuilder.builder()
                .title("title")
                .description(originalDescription)
                .severity(4)
                .project(PROJECT_KEY)
                .build());

    String assetSerial = "testCreateInternalRepairOrderWithExistingOtsTicket";
    setupMockedServices(assetSerial);
    RepairOrder internalRepairOrder =
        createTestInternalRepairOrder(
            repairOrder -> repairOrder.existingOtsTicketKey(ticket.ticketKey()));
    assertNotNull(internalRepairOrder.getId());

    OtsTicket actual =
        pollUntil(
            () -> otsService.get(ticket.ticketId()).orElse(null),
            t -> !originalDescription.equals(t.description()));
    String actualDescription = actual.description();
    log.info("Updated description: {}", actualDescription);
    assertThat(actualDescription).isNotEqualTo(originalDescription);
    assertThat(actualDescription).contains(originalDescription);
  }

  public static <T> T pollUntil(Supplier<T> supplier, Predicate<T> condition)
      throws InterruptedException {
    long timeout = System.currentTimeMillis() + 10_000;
    T actual = null;
    do {
      Thread.sleep(200);
      actual = supplier.get();
    } while (System.currentTimeMillis() < timeout && !condition.test(actual));
    return actual;
  }

  @Test
  void testTicketDecoratorDataIsPersisted() {
    String assetSerial = "testCreateInternalRepairOrder";
    setupMockedServices(assetSerial);
    RepairOrder repairOrder =
        createTestInternalRepairOrder(
            assetSerial, "Internal server issue", 2, InternalRepairOrderSourceType.Network);
    RmcId id = RmcId.fromString(repairOrder.getId());
    TicketData ticketData = ticketDataRepository.get(id);
    List<TicketFieldEntity> ticketFields =
        ticketFieldRepository.listByChangeOrderIdOrderByIndex(id);

    TicketData expected = TicketData.fromApiModel(TICKET_DATA);
    try (AutoCloseableSoftAssertions softly = new AutoCloseableSoftAssertions()) {
      expected
          .keySet()
          .forEach(
              key -> {
                Object actualValue = ticketData.get(key);
                softly.assertThat(actualValue).as(key).isEqualTo(expected.get(key));
              });

      // Order of ticketFields matters
      for (int index = 0; index < TICKET_FIELDS.size(); index++) {
        TicketFieldEntity actualTicketField = ticketFields.get(index);
        softly.assertThat(actualTicketField.key().index()).as("index").isEqualTo(index);
        softly
            .assertThat(actualTicketField)
            .usingRecursiveComparison()
            .ignoringFields("key")
            .isEqualTo(TICKET_FIELDS.get(index));
      }
    }
  }

  @Test
  void testCreateInternalPowerShelfRepairOrder() {
    String assetSerial = "testCreateInternalPowerShelfRepairOrder";
    setupMockedServices(assetSerial);
    RepairOrder internalRepairOrder =
        createTestInternalPowerShelfRepairOrder(assetSerial, "Internal power shelf issue", 2);
    assertNotNull(internalRepairOrder.getId());
    assertEquals("Internal power shelf issue", internalRepairOrder.getDetails());
    assertEquals(assetSerial, internalRepairOrder.getDeviceSerialNumber());
    assertEquals(2, internalRepairOrder.getPriority());
    assertEquals(InternalRepairOrderSourceType.Network, internalRepairOrder.getSource());
    assertEquals(RepairOrder.LifecycleState.Accepted, internalRepairOrder.getLifecycleState());
    assertThat(internalRepairOrder.getLabels()).isEqualTo(EXPECTED_LABELS);
  }

  @Test
  void testCreateInternalE5RepairOrder() {
    String assetSerial = "testCreateInternalE5RepairOrder";
    setupMockedServices(assetSerial);
    RepairOrder internalRepairOrder =
        createTestInternalE5ServerRepairOrder(assetSerial, "Internal E5 server issue", 2);
    assertNotNull(internalRepairOrder.getId());
    assertEquals("Internal E5 server issue", internalRepairOrder.getDetails());
    assertEquals(assetSerial, internalRepairOrder.getDeviceSerialNumber());
    assertEquals(2, internalRepairOrder.getPriority());
    assertEquals(InternalRepairOrderSourceType.Network, internalRepairOrder.getSource());
    assertEquals(RepairOrder.LifecycleState.Accepted, internalRepairOrder.getLifecycleState());
    assertThat(internalRepairOrder.getLabels()).isEqualTo(EXPECTED_LABELS);
  }

  @Test
  void testListRepairOrders() {
    setupMockedServices("SN654321");
    final RepairOrder internalRepairOrder =
        createTestInternalRepairOrder(
            "SN654321", "Internal server issue", 2, InternalRepairOrderSourceType.Burninator);
    setupMockedServices("SN789012");
    final RepairOrder repairOrder = createTestRepairOrder("SN789012", "Test Get Order", 3);
    var repairOrderListById =
        repairOrderClient
            .listRepairOrders(
                ListRepairOrdersRequest.builder()
                    .repairOrderId(repairOrder.getId())
                    .overrideCompartmentId(TEST_COMPARTMENT_ID)
                    .region(TEST_REGION)
                    .build())
            .getRepairOrderCollection();
    assertFalse(repairOrderListById.getItems().isEmpty());
    assertEquals(1, repairOrderListById.getItems().size());

    var internalRepairOrderListById =
        repairOrderClient
            .listInternalRepairOrders(
                ListInternalRepairOrdersRequest.builder()
                    .repairOrderId(internalRepairOrder.getId())
                    .overrideCompartmentId(TEST_COMPARTMENT_ID)
                    .region(TEST_REGION)
                    .build())
            .getRepairOrderCollection();
    assertFalse(internalRepairOrderListById.getItems().isEmpty());
    assertEquals(1, internalRepairOrderListById.getItems().size());

    var internalRepairOrderListByRackSerialNumber =
        repairOrderClient
            .listInternalRepairOrders(
                ListInternalRepairOrdersRequest.builder()
                    .overrideCompartmentId(TEST_COMPARTMENT_ID)
                    .repairOrderId(internalRepairOrder.getId())
                    .rackSerialNumber(RACK_SERIAL_NUMBER)
                    .region(TEST_REGION)
                    .build())
            .getRepairOrderCollection();
    assertFalse(internalRepairOrderListByRackSerialNumber.getItems().isEmpty());
    assertEquals(1, internalRepairOrderListByRackSerialNumber.getItems().size());

    var internalRepairOrderListByRackSerialNumberNotPresent =
        repairOrderClient
            .listInternalRepairOrders(
                ListInternalRepairOrdersRequest.builder()
                    .overrideCompartmentId(TEST_COMPARTMENT_ID)
                    .repairOrderId(internalRepairOrder.getId())
                    .rackSerialNumber(RACK_SERIAL_NUMBER + "not_present")
                    .region(TEST_REGION)
                    .build())
            .getRepairOrderCollection();
    assertTrue(internalRepairOrderListByRackSerialNumberNotPresent.getItems().isEmpty());

    var acceptedRepairOrderList =
        repairOrderClient
            .listRepairOrders(
                ListRepairOrdersRequest.builder()
                    .lifecycleState(RepairOrder.LifecycleState.Accepted.getValue())
                    .overrideCompartmentId(TEST_COMPARTMENT_ID)
                    .region(TEST_REGION)
                    .build())
            .getRepairOrderCollection();
    assertFalse(acceptedRepairOrderList.getItems().isEmpty());

    var acceptedInternalRepairOrderList =
        repairOrderClient
            .listInternalRepairOrders(
                ListInternalRepairOrdersRequest.builder()
                    .lifecycleState(RepairOrder.LifecycleState.Accepted.getValue())
                    .overrideCompartmentId(TEST_COMPARTMENT_ID)
                    .region(TEST_REGION)
                    .build())
            .getRepairOrderCollection();
    assertFalse(acceptedInternalRepairOrderList.getItems().isEmpty());

    var repairOrderListBySerialNumber =
        repairOrderClient
            .listRepairOrders(
                ListRepairOrdersRequest.builder()
                    .deviceSerialNumber("SN789012")
                    .overrideCompartmentId(TEST_COMPARTMENT_ID)
                    .region(TEST_REGION)
                    .build())
            .getRepairOrderCollection();
    assertFalse(repairOrderListBySerialNumber.getItems().isEmpty());
    assertEquals(1, repairOrderListBySerialNumber.getItems().size());

    var internalRepairOrderListBySerialNumber =
        repairOrderClient
            .listInternalRepairOrders(
                ListInternalRepairOrdersRequest.builder()
                    .deviceSerialNumber("SN654321")
                    .overrideCompartmentId(TEST_COMPARTMENT_ID)
                    .region(TEST_REGION)
                    .build())
            .getRepairOrderCollection();
    assertFalse(internalRepairOrderListBySerialNumber.getItems().isEmpty());
    assertEquals(1, internalRepairOrderListBySerialNumber.getItems().size());

    var expectedEmptyRepairOrderList =
        repairOrderClient
            .listRepairOrders(
                ListRepairOrdersRequest.builder()
                    .lifecycleState(RepairOrder.LifecycleState.InProgress.getValue())
                    .overrideCompartmentId(TEST_COMPARTMENT_ID)
                    .region(TEST_REGION)
                    .build())
            .getRepairOrderCollection();
    assertTrue(expectedEmptyRepairOrderList.getItems().isEmpty());

    var repairOrderListByLabel =
        repairOrderClient
            .listRepairOrders(
                ListRepairOrdersRequest.builder()
                    .label("a")
                    .overrideCompartmentId(TEST_COMPARTMENT_ID)
                    .region(TEST_REGION)
                    .build())
            .getRepairOrderCollection();
    assertFalse(repairOrderListByLabel.getItems().isEmpty());
    assertEquals(1, repairOrderListBySerialNumber.getItems().size());
  }

  @Test
  void testGetRepairOrder() {
    String assetSerial = "testGetRepairOrder";
    setupMockedServices(assetSerial);
    RepairOrder repairOrder = createTestRepairOrder(assetSerial, "Test Get Order", 3);
    var getRepairOrderResponse =
        repairOrderClient.getRepairOrder(
            GetRepairOrderRequest.builder().repairOrderId(repairOrder.getId()).build());

    final RepairOrder actual = getRepairOrderResponse.getRepairOrder();
    assertEquals(200, getRepairOrderResponse.get__httpStatusCode__());
    assertEquals(assetSerial, getRepairOrderResponse.getRepairOrder().getDeviceSerialNumber());
    assertEquals("Test Get Order", getRepairOrderResponse.getRepairOrder().getDetails());
    assertEquals(3, getRepairOrderResponse.getRepairOrder().getPriority());
    assertThat(actual.getLabels()).isEqualTo(EXPECTED_LABELS);

    assertThrows(
        BmcException.class,
        () ->
            repairOrderClient.getRepairOrder(
                GetRepairOrderRequest.builder()
                    .repairOrderId(RmcId.generate(RmcIdType.CM.CHANGE_ORDER).toString())
                    .build()));
  }

  @Test
  void testChangeOrderTypeEnumValues() {
    // Expected enum values
    EnumSet<ChangeOrderType> expectedEnumValues =
        EnumSet.of(
            ChangeOrderType.E5_SERVER_REPAIR,
            ChangeOrderType.SERVER_REPAIR,
            ChangeOrderType.GB200_SERVER_REPAIR,
            ChangeOrderType.MULTI_REPAIR,
            ChangeOrderType.GB200_DISK_REPAIR,
            ChangeOrderType.GB200_POWER_SHELF_REPAIR,
            ChangeOrderType.GB200_POWER_SUPPLY_UNIT_REPAIR,
            ChangeOrderType.GB200_POWER_MANAGEMENT_CONTROLLER_REPAIR,
            ChangeOrderType.GB200_RACK_REPAIR,
            ChangeOrderType.MANAGEMENT_SWITCH_REPAIR,
            ChangeOrderType.NVSWITCH_REPAIR,
            ChangeOrderType.NETWORK_LINK_REPAIR,
            ChangeOrderType.DISK_REPAIR,
            ChangeOrderType.RACK_INGESTION,
            ChangeOrderType.NETWORK_LEAF_SPINE_LINK_REPAIR,
            ChangeOrderType.E5_RPDU_REPAIR,
            ChangeOrderType.NETWORK_GENERIC_LINK_REPAIR,
            ChangeOrderType.NETWORK_DEVICE_REPAIR);

    // Get actual enum values
    EnumSet<ChangeOrderType> actualEnumValues = EnumSet.allOf(ChangeOrderType.class);

    Set<ChangeOrderType> missing = EnumSet.copyOf(expectedEnumValues);
    missing.removeAll(actualEnumValues);

    Set<ChangeOrderType> extra = EnumSet.copyOf(actualEnumValues);
    extra.removeAll(expectedEnumValues);

    try (AutoCloseableSoftAssertions softly = new AutoCloseableSoftAssertions()) {
      // Test if the actual enum values match the expected values, error message from
      // 2nd & 3rd check are easier to understand
      softly.assertThat(actualEnumValues).isEqualTo(expectedEnumValues);
      softly.assertThat(missing).as("Missing ChangeOrderTypes").isEmpty();
      softly.assertThat(extra).as("Extra ChangeOrderTypes").isEmpty();
    }
  }

  @Test
  void testPrioritizeRepairOrder() {
    String assetSerial = "testPrioritizeRepairOrder";
    setupMockedServices(assetSerial);
    RepairOrder order = createTestRepairOrder(assetSerial, "Original prioritization", 1);
    assertNotNull(order.getId());
    assertEquals(assetSerial, order.getDeviceSerialNumber());
    assertEquals(RepairOrder.LifecycleState.Accepted, order.getLifecycleState());
    Integer expected = order.getPriority() + 1;
    PrioritizeRepairOrderDetails details =
        PrioritizeRepairOrderDetails.builder().priority(expected).build();

    PrioritizeRepairOrderRequest request =
        PrioritizeRepairOrderRequest.builder()
            .repairOrderId(order.getId())
            .prioritizeRepairOrderDetails(details)
            .build();

    repairOrderClient.prioritizeRepairOrder(request);
    GetRepairOrderResponse response =
        repairOrderClient.getRepairOrder(
            GetRepairOrderRequest.builder().repairOrderId(order.getId()).build());

    assertEquals(expected, response.getRepairOrder().getPriority());
  }

  @Test
  void testPrioritizeRepairOrder_TerminalStateThrowsException() {
    String assetSerial = "testPrioritizeRepairOrder_TerminalStateThrowsException";
    setupMockedServices(assetSerial);
    RepairOrder order = createTestRepairOrder(assetSerial, "Original prioritization", 1);
    repairOrderClient.cancelRepairOrder(
        CancelRepairOrderRequest.builder()
            .repairOrderId(order.getId())
            .cancelRepairOrderDetails(
                CancelRepairOrderDetails.builder()
                    .reason("testPrioritizeRepairOrder_TerminalStateThrowsException")
                    .build())
            .build());

    Integer expected = order.getPriority();
    PrioritizeRepairOrderDetails details =
        PrioritizeRepairOrderDetails.builder().priority(expected + 1).build();

    PrioritizeRepairOrderRequest request =
        PrioritizeRepairOrderRequest.builder()
            .repairOrderId(order.getId())
            .prioritizeRepairOrderDetails(details)
            .build();

    BmcException ex =
        assertThrows(BmcException.class, () -> repairOrderClient.prioritizeRepairOrder(request));
    assertTrue(ex.getMessage().contains("Cannot update priority of a terminal repair order"));

    GetRepairOrderResponse response =
        repairOrderClient.getRepairOrder(
            GetRepairOrderRequest.builder().repairOrderId(order.getId()).build());

    assertEquals(expected, response.getRepairOrder().getPriority());
  }

  @Test
  void testReopenRepairOrder() {
    String assetSerial = "testReopenRepairOrder";
    setupMockedServices(assetSerial);
    RepairOrder originalOrder = createTestRepairOrder(assetSerial, "Original issue", 1);
    assertNotNull(originalOrder.getId());
    assertEquals(assetSerial, originalOrder.getDeviceSerialNumber());
    assertEquals(RepairOrder.LifecycleState.Accepted, originalOrder.getLifecycleState());
    String cancelReason = "Issue resolved temporarily";
    var canceledResponse =
        repairOrderClient.cancelRepairOrder(
            com.oracle.oci.rmc.client.requests.CancelRepairOrderRequest.builder()
                .repairOrderId(originalOrder.getId())
                .cancelRepairOrderDetails(
                    com.oracle.oci.rmc.client.model.CancelRepairOrderDetails.builder()
                        .reason(cancelReason)
                        .build())
                .build());
    assertEquals(200, canceledResponse.get__httpStatusCode__());
    RepairOrder canceledOrder = canceledResponse.getRepairOrder();
    assertEquals(RepairOrder.LifecycleState.Canceled, canceledOrder.getLifecycleState());
    assertEquals(cancelReason, canceledOrder.getReason());

    var reopenDetails =
        CreateServerRepairOrderDetails.builder()
            .deviceSerialNumber(assetSerial)
            .priority(1)
            .details("Reopening due to unresolved issue")
            .diagnosticReportObjectStorageUrl("https://example.com/diagnostic-report")
            .errorCodes(List.of())
            .build();

    var reopenResponse =
        repairOrderClient.reopenRepairOrder(
            ReopenRepairOrderRequest.builder()
                .repairOrderId(originalOrder.getId())
                .reopenRepairOrderDetails(reopenDetails)
                .build());

    assertNotNull(reopenResponse);
    assertEquals(200, reopenResponse.get__httpStatusCode__());
    RepairOrder reopenedOrder = reopenResponse.getRepairOrder();
    assertNotNull(reopenedOrder);
    assertEquals(assetSerial, reopenedOrder.getDeviceSerialNumber());

    assertEquals(RepairOrder.LifecycleState.Accepted, reopenedOrder.getLifecycleState());
    assertInstanceOf(ServerRepairOrder.class, reopenedOrder);

    PaginatedList<ChangeOrder> coList =
        changeOrderRepository.list(
            null,
            assetSerial,
            getFilterChangeOrderTypes(),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            PageQuery.fromEmptyToken(10));
  }

  @Test
  void testReopenRepairOrder_NonTerminalThrowsException() {
    String assetSerial = "testReopenRepairOrder_NonTerminalThrowsException";
    setupMockedServices(assetSerial);
    // Create an order not in terminal state
    RepairOrder originalOrder =
        createTestRepairOrder(assetSerial, "Original issue non terminal", 1);
    assertNotNull(originalOrder.getId());
    assertEquals(RepairOrder.LifecycleState.Accepted, originalOrder.getLifecycleState());

    var reopenDetails =
        CreateServerRepairOrderDetails.builder()
            .deviceSerialNumber(assetSerial)
            .priority(1)
            .details("Reopen request")
            .diagnosticReportObjectStorageUrl("https://example.com/diagnostic-report")
            .errorCodes(List.of())
            .build();

    // Reopen an order not in terminal state
    BmcException ex =
        assertThrows(
            BmcException.class,
            () ->
                repairOrderClient.reopenRepairOrder(
                    ReopenRepairOrderRequest.builder()
                        .repairOrderId(originalOrder.getId())
                        .reopenRepairOrderDetails(reopenDetails)
                        .build()));
    assertTrue(ex.getMessage().contains("Cannot reopen a non-terminal repair order chain."));
    PaginatedList<ChangeOrder> coList =
        changeOrderRepository.list(
            null,
            assetSerial,
            getFilterChangeOrderTypes(),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            PageQuery.fromEmptyToken(10));
  }

  @Test
  void testCloseInternalRepairOrder_FailureStateNotAllowed() {
    String assetSerial = "testCloseInternalRepairOrder_FailureStateNotAllowed";

    setupMockedServices(assetSerial);

    RepairOrder internalRepairOrder =
        createTestInternalRepairOrder(
            assetSerial, "Issue to be closed", 3, InternalRepairOrderSourceType.Network);
    assertNotNull(internalRepairOrder.getId());
    assertEquals(assetSerial, internalRepairOrder.getDeviceSerialNumber());

    CloseInternalRepairOrderDetails closeDetails =
        CloseInternalRepairOrderDetails.builder()
            .reason("Hardware failure - Unrecoverable")
            .build();

    BmcException ex =
        assertThrows(
            BmcException.class,
            () ->
                repairOrderClient.closeInternalRepairOrder(
                    CloseInternalRepairOrderRequest.builder()
                        .repairOrderId(internalRepairOrder.getId())
                        .closeInternalRepairOrderDetails(closeDetails)
                        .build()));
    assertTrue(ex.getMessage().contains("Order cannot be closed in its current state."));
  }

  @Test
  void testCreateRepairOrderWithSubComponentSerialNumbers() {
    String assetSerial = "testCreateRepairOrderWithSubComponentSerialNumbers";

    setupMockedServices(assetSerial);

    CreateServerRepairOrderDetails details =
        CreateServerRepairOrderDetails.builder()
            .deviceSerialNumber(assetSerial)
            .priority(1)
            .details("Server with subComponents")
            .diagnosticReportObjectStorageUrl("https://example.com/diag")
            .errorCodes(List.of())
            .subComponentSerialNumbers(List.of("SUB-SN-1", "SUB-SN-2"))
            .build();

    RepairOrder createdOrder =
        repairOrderClient
            .createRepairOrder(
                CreateRepairOrderRequest.builder()
                    .createRepairOrderDetails(details)
                    .overrideCompartmentId(TEST_COMPARTMENT_ID)
                    .region(TEST_REGION)
                    .build())
            .getRepairOrder();
    assertEquals(assetSerial, createdOrder.getDeviceSerialNumber());
    assertEquals(RepairOrder.LifecycleState.Accepted, createdOrder.getLifecycleState());

    RepairOrder fetchedOrder =
        repairOrderClient
            .getRepairOrder(
                GetRepairOrderRequest.builder().repairOrderId(createdOrder.getId()).build())
            .getRepairOrder();
    assertNotNull(fetchedOrder);

    assertNotNull(fetchedOrder.getActions());
    assertFalse(fetchedOrder.getActions().isEmpty());
    assertEquals(1, fetchedOrder.getActions().size());

    var action = fetchedOrder.getActions().get(0);
    assertEquals(List.of("SUB-SN-1", "SUB-SN-2"), action.getSubComponentSerialNumbers());
    assertTrue(action.getReplacements() == null || action.getReplacements().isEmpty());
  }

  @Test
  void testCreateRepairOrderWithActions() {
    String assetSerial = "testCreateRepairOrderWithActions";

    setupMockedServices(assetSerial);
    RepairOrder createdOrder = createTestRepairOrder(assetSerial, "Repair with actions", 2);

    RepairOrder fetchedOrder =
        repairOrderClient
            .getRepairOrder(
                GetRepairOrderRequest.builder().repairOrderId(createdOrder.getId()).build())
            .getRepairOrder();

    assertNotNull(fetchedOrder.getActions(), "actions field must not be null");
    assertFalse(fetchedOrder.getActions().isEmpty(), "actions should not be empty");
  }

  @Test
  void testFailInternalRepairOrder_FailureStateNotAllowed() {
    String assetSerial = "testFailInternalRepairOrder_FailureStateNotAllowed";
    setupMockedServices(assetSerial);

    RepairOrder internalRepairOrder =
        createTestInternalRepairOrder(
            assetSerial, "Internal issue for fail test", 3, InternalRepairOrderSourceType.Network);
    assertNotNull(internalRepairOrder.getId());
    assertEquals(assetSerial, internalRepairOrder.getDeviceSerialNumber());
    assertEquals(RepairOrder.LifecycleState.Accepted, internalRepairOrder.getLifecycleState());

    InternalRepairOrderRevision failRevision =
        InternalRepairOrderRevision.builder()
            .comment("Cannot fix it, let's fail")
            .details(
                CreateInternalServerRepairOrderDetails.builder()
                    .deviceSerialNumber(assetSerial)
                    .priority(3)
                    .details("We are failing the internal repair")
                    .isSharedWithCustomer(false)
                    .source(InternalRepairOrderSourceType.Network)
                    .diagnosticReportObjectStorageUrl("https://example.com/fail-diagnostics")
                    .build())
            .build();

    BmcException ex =
        assertThrows(
            BmcException.class,
            () ->
                repairOrderClient.failInternalRepairOrder(
                    com.oracle.oci.rmc.client.requests.FailInternalRepairOrderRequest.builder()
                        .failInternalRepairOrderDetails(failRevision)
                        .repairOrderId(internalRepairOrder.getId())
                        .build()));

    assertTrue(
        ex.getMessage().contains("Cannot fail repair order that the change order"),
        "Expected a message complaining about a non-validated state");
  }

  @Test
  void testReplyToInternalRepairOrder_FailureStateNotAllowed() {
    String assetSerial = "testReplyToInternalRepairOrder_FailureStateNotAllowed";
    setupMockedServices(assetSerial);

    RepairOrder internalRepairOrder =
        createTestInternalRepairOrder(
            assetSerial, "Internal issue for reply test", 2, InternalRepairOrderSourceType.Network);
    assertNotNull(internalRepairOrder.getId());
    assertEquals(assetSerial, internalRepairOrder.getDeviceSerialNumber());
    assertEquals(RepairOrder.LifecycleState.Accepted, internalRepairOrder.getLifecycleState());

    InternalRepairOrderRevision replyRevision =
        InternalRepairOrderRevision.builder()
            .comment("Need to supply extra data for the internal repair")
            .details(
                CreateInternalServerRepairOrderDetails.builder()
                    .deviceSerialNumber(assetSerial)
                    .priority(2)
                    .details("Some more internal details to fix it")
                    .isSharedWithCustomer(false)
                    .source(InternalRepairOrderSourceType.Network)
                    .diagnosticReportObjectStorageUrl("https://example.com/reply-diagnostics")
                    .build())
            .build();

    BmcException ex =
        assertThrows(
            BmcException.class,
            () ->
                repairOrderClient.replyToInternalRepairOrder(
                    com.oracle.oci.rmc.client.requests.ReplyToInternalRepairOrderRequest.builder()
                        .replyToInternalRepairOrderDetails(replyRevision)
                        .repairOrderId(internalRepairOrder.getId())
                        .build()));
    assertTrue(
        ex.getMessage().contains("Repair order does not have open needs attention details."));
  }

  @Test
  void testApproveInternalRepairOrder_NotSupported() {
    String assetSerial = "testApproveInternalRepairOrder_NotSupported";
    setupMockedServices(assetSerial);

    RepairOrder internalRepairOrder =
        createTestInternalRepairOrder(
            assetSerial,
            "Internal issue for approve test",
            1,
            InternalRepairOrderSourceType.Network);
    assertNotNull(internalRepairOrder.getId());
    assertEquals(RepairOrder.LifecycleState.Accepted, internalRepairOrder.getLifecycleState());

    BmcException ex =
        assertThrows(
            BmcException.class,
            () ->
                repairOrderClient.approveInternalRepairOrder(
                    ApproveInternalRepairOrderRequest.builder()
                        .repairOrderId(internalRepairOrder.getId())
                        .build()));

    assertTrue(
        ex.getMessage()
            .contains("ApproveRepairOrder is not supported for the current repair order."),
        "Expected a 'not supported' exception message");
  }

  @Test
  void testApproveInternalRepairOrder_Success() {
    String assetSerial = "SN_APPROVE_001";
    setupMockedServices(assetSerial);
    RepairOrder internalOrder =
        createTestInternalRepairOrder(
            assetSerial, "Internal approval test", 1, InternalRepairOrderSourceType.Burninator);
    assertNotNull(internalOrder.getId());
    assertEquals(InternalRepairOrderSourceType.Burninator, internalOrder.getSource());
    var approveRequest =
        ApproveInternalRepairOrderRequest.builder().repairOrderId(internalOrder.getId()).build();
    RepairOrder approvedOrder =
        repairOrderClient.approveInternalRepairOrder(approveRequest).getRepairOrder();
    assertNotNull(approvedOrder);
    assertEquals(RepairOrder.LifecycleState.Accepted, approvedOrder.getLifecycleState());
  }

  @Test
  void testListRepairOrders_IncludesOnlySharedInternalROs() {
    String serialShared = "SN_SHARED_001";
    String serialNonShared = "SN_NONSHARED_001";
    setupMockedServices(serialShared);
    RepairOrder sharedRo =
        createTestInternalRepairOrder(
            serialShared, "shared ro", 1, InternalRepairOrderSourceType.Network, true);

    setupMockedServices(serialNonShared);
    createTestInternalRepairOrder(
        serialNonShared, "non-shared ro", 1, InternalRepairOrderSourceType.Network, false);

    RepairOrderCollection collection =
        repairOrderClient
            .listRepairOrders(
                ListRepairOrdersRequest.builder()
                    .overrideCompartmentId(TEST_COMPARTMENT_ID)
                    .region(TEST_REGION)
                    .build())
            .getRepairOrderCollection();

    assertTrue(collection.getItems().stream().anyMatch(ro -> ro.getId().equals(sharedRo.getId())));

    assertFalse(
        collection.getItems().stream()
            .anyMatch(ro -> ro.getDeviceSerialNumber().equals(serialNonShared)));
  }

  @Test
  void testCreateRepairOrder_InvalidDiagnosticReportObjectStorageUrl() {
    String assetSerial = "testCreateRepairOrder_InvalidDiagnosticReportObjectStorageUrl";
    setupMockedServices(assetSerial);

    CreateServerRepairOrderDetails details =
        CreateServerRepairOrderDetails.builder()
            .deviceSerialNumber(assetSerial)
            .priority(1)
            .details("Server issue")
            .diagnosticReportObjectStorageUrl(null) // Invalid URL
            .isDeviceInaccessible(IS_DEVICE_INACCESSIBLE)
            .errorCodes(List.of())
            .build();

    BmcException ex =
        assertThrows(
            BmcException.class,
            () ->
                repairOrderClient.createRepairOrder(
                    CreateRepairOrderRequest.builder()
                        .createRepairOrderDetails(details)
                        .overrideCompartmentId(TEST_COMPARTMENT_ID)
                        .region(TEST_REGION)
                        .build()));

    assertTrue(
        ex.getMessage()
            .contains(
                "diagnosticReportObjectStorageUrl is required when isDeviceInaccessible is set to"
                    + " false."));
  }

  @Test
  void testCreateRepairOrder_ValidFields() {
    String assetSerial = "testCreateRepairOrder_ValidFields";
    setupMockedServices(assetSerial);

    CreateServerRepairOrderDetails details =
        CreateServerRepairOrderDetails.builder()
            .deviceSerialNumber(assetSerial)
            .priority(1)
            .details("Server issue")
            .diagnosticReportObjectStorageUrl(DIAGNOSTIC_REPORT_OBJECT_STORAGE_URL)
            .isDeviceInaccessible(IS_DEVICE_INACCESSIBLE)
            .errorCodes(List.of())
            .build();

    RepairOrder repairOrder =
        repairOrderClient
            .createRepairOrder(
                CreateRepairOrderRequest.builder()
                    .createRepairOrderDetails(details)
                    .overrideCompartmentId(TEST_COMPARTMENT_ID)
                    .region(TEST_REGION)
                    .build())
            .getRepairOrder();

    assertNotNull(repairOrder);
    assertEquals(assetSerial, repairOrder.getDeviceSerialNumber());
    assertEquals(RepairOrder.LifecycleState.Accepted, repairOrder.getLifecycleState());
  }

  private RepairOrder createTestRepairOrder(String serialNumber, String details, int priority) {
    RepairOrder repairOrder =
        repairOrderClient
            .createRepairOrder(
                CreateRepairOrderRequest.builder()
                    .createRepairOrderDetails(
                        CreateServerRepairOrderDetails.builder()
                            .labels(LABELS)
                            .details(details)
                            .deviceSerialNumber(serialNumber)
                            .priority(priority)
                            .diagnosticReportObjectStorageUrl(
                                "https://example.com/diagnostic-report")
                            .errorCodes(List.of())
                            .build())
                    .overrideCompartmentId(TEST_COMPARTMENT_ID)
                    .region(TEST_REGION)
                    .build())
            .getRepairOrder();
    return repairOrder;
  }

  private RepairOrder createTestE5ServerRepairOrder(
      String serialNumber, String details, int priority) {
    RepairOrder repairOrder =
        repairOrderClient
            .createRepairOrder(
                CreateRepairOrderRequest.builder()
                    .createRepairOrderDetails(
                        CreateE5ServerRepairOrderDetails.builder()
                            .labels(LABELS)
                            .details(details)
                            .deviceSerialNumber(serialNumber)
                            .priority(priority)
                            .diagnosticReportObjectStorageUrl(
                                "https://example.com/diagnostic-report")
                            .errorCodes(List.of())
                            .build())
                    .overrideCompartmentId(TEST_COMPARTMENT_ID)
                    .region(TEST_REGION)
                    .build())
            .getRepairOrder();
    return repairOrder;
  }

  private RepairOrder createTestInternalRepairOrder(
      String serialNumber, String details, int priority, InternalRepairOrderSourceType sourceType) {
    return createTestInternalRepairOrder(
        repairOrder ->
            repairOrder
                .details(details)
                .deviceSerialNumber(serialNumber)
                .priority(priority)
                .source(sourceType));
  }

  private RepairOrder createTestInternalRepairOrder(
      Consumer<CreateInternalServerRepairOrderDetails.Builder> setters) {
    CreateInternalServerRepairOrderDetails.Builder builder =
        CreateInternalServerRepairOrderDetails.builder()
            .labels(LABELS)
            .diagnosticProbeOutput(DIAGNOSTIC_PROBE_OUTPUT)
            .details("Hardware Failure")
            .deviceSerialNumber("SN-" + System.currentTimeMillis())
            .priority(4)
            .source(InternalRepairOrderSourceType.Burninator)
            .isSharedWithCustomer(false)
            .diagnosticReportObjectStorageUrl("https://example.com/diagnostic-report")
            .errorCodes(List.of())
            .ticketData(TICKET_DATA)
            .ticketFields(TICKET_FIELDS)
            .targetRepairOperationsTeam("CPVTier1");
    setters.accept(builder);
    RepairOrder internalRepairOrder =
        repairOrderClient
            .createInternalRepairOrder(
                CreateInternalRepairOrderRequest.builder()
                    .createInternalRepairOrderDetails(builder.build())
                    .overrideCompartmentId(TEST_COMPARTMENT_ID)
                    .region(TEST_REGION)
                    .build())
            .getRepairOrder();
    return internalRepairOrder;
  }

  private RepairOrder createTestInternalRepairOrder(
      String serialNumber,
      String details,
      int priority,
      InternalRepairOrderSourceType sourceType,
      boolean sharedWithCustomer) {
    return repairOrderClient
        .createInternalRepairOrder(
            CreateInternalRepairOrderRequest.builder()
                .createInternalRepairOrderDetails(
                    CreateInternalServerRepairOrderDetails.builder()
                        .labels(LABELS)
                        .deviceSerialNumber(serialNumber)
                        .priority(priority)
                        .details(details)
                        .source(sourceType)
                        .isSharedWithCustomer(sharedWithCustomer)
                        .diagnosticReportObjectStorageUrl("https://example.com/diag")
                        .errorCodes(List.of())
                        .build())
                .overrideCompartmentId(TEST_COMPARTMENT_ID)
                .region(TEST_REGION)
                .build())
        .getRepairOrder();
  }

  private RepairOrder createTestInternalPowerShelfRepairOrder(
      String serialNumber, String details, int priority) {
    RepairOrder internalRepairOrder =
        repairOrderClient
            .createInternalRepairOrder(
                CreateInternalRepairOrderRequest.builder()
                    .createInternalRepairOrderDetails(
                        CreateInternalGb200PowerShelfRepairOrderDetails.builder()
                            .labels(LABELS)
                            .details(details)
                            .deviceSerialNumber(serialNumber)
                            .priority(priority)
                            .source(InternalRepairOrderSourceType.Network)
                            .isSharedWithCustomer(false)
                            .diagnosticReportObjectStorageUrl(
                                "https://example.com/diagnostic-report")
                            .errorCodes(List.of())
                            .build())
                    .overrideCompartmentId(TEST_COMPARTMENT_ID)
                    .region(TEST_REGION)
                    .build())
            .getRepairOrder();
    return internalRepairOrder;
  }

  private RepairOrder createTestInternalE5ServerRepairOrder(
      String serialNumber, String details, int priority) {
    RepairOrder internalRepairOrder =
        repairOrderClient
            .createInternalRepairOrder(
                CreateInternalRepairOrderRequest.builder()
                    .createInternalRepairOrderDetails(
                        CreateInternalE5ServerRepairOrderDetails.builder()
                            .labels(LABELS)
                            .details(details)
                            .deviceSerialNumber(serialNumber)
                            .priority(priority)
                            .source(InternalRepairOrderSourceType.Network)
                            .isSharedWithCustomer(false)
                            .diagnosticReportObjectStorageUrl(
                                "https://example.com/diagnostic-report")
                            .errorCodes(List.of())
                            .build())
                    .overrideCompartmentId(TEST_COMPARTMENT_ID)
                    .region(TEST_REGION)
                    .build())
            .getRepairOrder();
    return internalRepairOrder;
  }

  private RepairOrder createTestMultiServerRepairOrder(
      String serialNumber, String details, int priority) {

    RepairOrder ro =
        repairOrderClient
            .createInternalRepairOrder(
                CreateInternalRepairOrderRequest.builder()
                    .createInternalRepairOrderDetails(
                        CreateInternalMultiRepairOrderDetails.builder()
                            .priority(priority)
                            .labels(LABELS)
                            .details(details)
                            .deviceSerialNumber(serialNumber)
                            .source(InternalRepairOrderSourceType.Burninator)
                            .isSharedWithCustomer(false)
                            .diagnosticReportObjectStorageUrl(
                                "https://example.com/diagnostic-report")
                            .errorCodes(List.of())
                            .build())
                    .overrideCompartmentId(TEST_COMPARTMENT_ID)
                    .region(TEST_REGION)
                    .build())
            .getRepairOrder();

    return ro;
  }

  @Test
  void testCreateMultiServerRepairOrder() {
    String sn = "SN_MULTI_00";
    setupMockedServices(sn);

    RepairOrder ro = createTestMultiServerRepairOrder(sn, "Multi‑server repair issue", 2);

    assertNotNull(ro.getId());
    assertEquals(sn, ro.getDeviceSerialNumber());
    assertEquals(RepairOrder.LifecycleState.Accepted, ro.getLifecycleState());
    assertThat(ro.getLabels()).containsExactlyInAnyOrderElementsOf(EXPECTED_LABELS);

    assertNotNull(ro.getActions());
    assertEquals(1, ro.getActions().size());
  }

  @Test
  void testListMultiServerRepairOrders() {
    String sn = "SN_LIST_MULTI_001";
    setupMockedServices(sn);

    RepairOrder created = createTestMultiServerRepairOrder(sn, "List‑API verification", 1);

    RepairOrderCollection col =
        repairOrderClient
            .listInternalRepairOrders(
                ListInternalRepairOrdersRequest.builder()
                    .repairOrderType(RepairOrderType.MultiRepair.getValue())
                    .overrideCompartmentId(TEST_COMPARTMENT_ID)
                    .region(TEST_REGION)
                    .build())
            .getRepairOrderCollection();

    assertFalse(col.getItems().isEmpty());
    assertTrue(col.getItems().stream().anyMatch(i -> i.getId().equals(created.getId())));

    assertThat(
            col.getItems().stream()
                .map(RepairOrderSummary::getRepairOrderType)
                .collect(Collectors.toSet()))
        .containsOnly(RepairOrderType.MultiRepair);
  }

  @Test
  void testCloseRepairOrder_FailureStateNotAllowed() {
    String assetSerial = "testCloseRepairOrder_FailureStateNotAllowed";

    setupMockedServices(assetSerial);

    RepairOrder repairOrder = createTestRepairOrder(assetSerial, "Issue to be closed", 3);
    assertNotNull(repairOrder.getId());
    assertEquals(assetSerial, repairOrder.getDeviceSerialNumber());

    CloseRepairOrderDetails closeRepairOrderDetails = CloseRepairOrderDetails.builder().build();

    BmcException ex =
        assertThrows(
            BmcException.class,
            () ->
                repairOrderClient.closeRepairOrder(
                    CloseRepairOrderRequest.builder()
                        .repairOrderId(repairOrder.getId())
                        .closeRepairOrderDetails(closeRepairOrderDetails)
                        .build()));
    assertTrue(ex.getMessage().contains("Order cannot be closed in its current state."));
  }

  @Test
  void testApproveRepairOrder_FailureStateNotAllowed() {
    String assetSerial = "testApproveRepairOrder_FailureStateNotAllowed";

    setupMockedServices(assetSerial);

    RepairOrder repairOrder = createTestRepairOrder(assetSerial, "Approve the repairOrder", 3);
    assertNotNull(repairOrder.getId());
    assertEquals(assetSerial, repairOrder.getDeviceSerialNumber());

    BmcException ex =
        assertThrows(
            BmcException.class,
            () ->
                repairOrderClient.approveRepairOrder(
                    ApproveRepairOrderRequest.builder()
                        .repairOrderId(repairOrder.getId())
                        .build()));
    assertTrue(ex.getMessage().contains("ApproveRepairOrder is not supported at the moment"));
  }

  @Test
  void testReplyToRepairOrder_FailureStateNotAllowed() {
    String assetSerial = "testReplyToRepairOrder_FailureStateNotAllowed";

    setupMockedServices(assetSerial);

    RepairOrder repairOrder = createTestRepairOrder(assetSerial, "Add comments", 3);
    assertNotNull(repairOrder.getId());
    assertEquals(assetSerial, repairOrder.getDeviceSerialNumber());

    RepairOrderRevision repairOrderRevision =
        RepairOrderRevision.builder()
            .comment("the repair doesn't work...")
            .details(
                CreateServerRepairOrderDetails.builder()
                    .deviceSerialNumber(assetSerial)
                    .priority(2)
                    .labels(LABELS)
                    .details("unresolved issues...")
                    .diagnosticReportObjectStorageUrl("https://example.com/diagnostic-report")
                    .errorCodes(List.of())
                    .build())
            .build();

    BmcException ex =
        assertThrows(
            BmcException.class,
            () ->
                repairOrderClient.replyToRepairOrder(
                    ReplyToRepairOrderRequest.builder()
                        .replyToRepairOrderDetails(repairOrderRevision)
                        .repairOrderId(repairOrder.getId())
                        .build()));
    assertTrue(
        ex.getMessage().contains("Repair order does not have open needs attention details."));
  }

  @Test
  void testFailRepairOrder_FailureStateNotAllowed() {
    String assetSerial = "testFailRepairOrder_FailureStateNotAllowed";

    setupMockedServices(assetSerial);

    RepairOrder repairOrder = createTestRepairOrder(assetSerial, "Fail repair order", 3);
    assertNotNull(repairOrder.getId());
    assertEquals(assetSerial, repairOrder.getDeviceSerialNumber());

    RepairOrderRevision repairOrderRevision =
        RepairOrderRevision.builder()
            .comment("the repair doesn't work...")
            .details(
                CreateServerRepairOrderDetails.builder()
                    .deviceSerialNumber(assetSerial)
                    .priority(2)
                    .labels(LABELS)
                    .details("unresolved issues...")
                    .diagnosticReportObjectStorageUrl("https://example.com/diagnostic-report")
                    .errorCodes(List.of())
                    .build())
            .build();

    BmcException ex =
        assertThrows(
            BmcException.class,
            () ->
                repairOrderClient.failRepairOrder(
                    FailRepairOrderRequest.builder()
                        .failRepairOrderDetails(repairOrderRevision)
                        .repairOrderId(repairOrder.getId())
                        .build()));
    assertTrue(ex.getMessage().contains("Cannot fail repair order that the change order"));
  }

  @Test
  void testListInternalRepairOrders_WithNeedsAttentionAndAcceptedFilter() {
    String assetSerial = "testNeedsAttention1";
    setupMockedServices(assetSerial);
    RepairOrder internalRepairOrder =
        createTestInternalRepairOrder(
            assetSerial, "Needs attention issue", 1, InternalRepairOrderSourceType.Burninator);
    assertNotNull(internalRepairOrder.getId());

    ListInternalRepairOrdersRequest listRequest =
        ListInternalRepairOrdersRequest.builder()
            .repairOrderId(internalRepairOrder.getId())
            .lifecycleState(RepairOrder.LifecycleState.NeedsAttention.getValue())
            .overrideCompartmentId(TEST_COMPARTMENT_ID)
            .region(TEST_REGION)
            .build();
    RepairOrderCollection collection =
        repairOrderClient.listInternalRepairOrders(listRequest).getRepairOrderCollection();

    assertTrue(collection.getItems().isEmpty());

    ListInternalRepairOrdersRequest acceptedListRequest =
        ListInternalRepairOrdersRequest.builder()
            .repairOrderId(internalRepairOrder.getId())
            .lifecycleState(RepairOrder.LifecycleState.Accepted.getValue())
            .overrideCompartmentId(TEST_COMPARTMENT_ID)
            .region(TEST_REGION)
            .build();
    RepairOrderCollection acceptedCollection =
        repairOrderClient.listInternalRepairOrders(acceptedListRequest).getRepairOrderCollection();
    assertFalse(acceptedCollection.getItems().isEmpty());
    RepairOrderSummary listedOrder = acceptedCollection.getItems().get(0);
    assertEquals(RepairOrder.LifecycleState.Accepted, listedOrder.getLifecycleState());
  }

  @Test
  void testCanaryCleanUpChangeOrders() {
    String assetSerial = "testCanaryCleanUpChangeOrders";
    setupMockedServices(assetSerial);
    RepairOrder internalRepairOrder =
        createTestInternalRepairOrder(
            assetSerial, "Needs attention issue", 1, InternalRepairOrderSourceType.Burninator);
    assertNotNull(internalRepairOrder.getId());
    ListInternalRepairOrdersRequest listRequest =
        ListInternalRepairOrdersRequest.builder()
            .repairOrderId(internalRepairOrder.getId())
            .overrideCompartmentId(TEST_COMPARTMENT_ID)
            .region(TEST_REGION)
            .build();
    RepairOrderCollection collection =
        repairOrderClient.listInternalRepairOrders(listRequest).getRepairOrderCollection();
    assertFalse(collection.getItems().isEmpty());
    var response =
        repairOrderClient.canaryCleanUpChangeOrders(
            com.oracle.oci.rmc.client.requests.CanaryCleanUpChangeOrdersRequest.builder()
                .region(TEST_REGION)
                .build());

    assertEquals(200, response.get__httpStatusCode__());
    assertNotNull(response.getCanaryDataProcessingResult());
    assertEquals(
        "Canary repair-order data purge completed successfully.",
        response.getCanaryDataProcessingResult().getResult());

    RepairOrderCollection collectionAfterCleanup =
        repairOrderClient.listInternalRepairOrders(listRequest).getRepairOrderCollection();
    assertTrue(collectionAfterCleanup.getItems().isEmpty());
  }

  @Test
  void testListRepairOrders_PaginationNextPage() {
    setupMockedServices("SN_PAG_1");
    final String ro1Id = createTestRepairOrder("SN_PAG_1", "page‑test‑1", 1).getId();
    setupMockedServices("SN_PAG_2");
    final String ro2Id = createTestRepairOrder("SN_PAG_2", "page‑test‑2", 1).getId();
    setupMockedServices("SN_PAG_3");
    final String ro3Id = createTestRepairOrder("SN_PAG_3", "page‑test‑3", 1).getId();

    var respPage1 =
        repairOrderClient.listRepairOrders(
            ListRepairOrdersRequest.builder()
                .limit(2)
                .sortBy(ListRepairOrdersRequest.SortBy.TimeCreated)
                .sortOrder(SortOrder.Asc)
                .overrideCompartmentId(TEST_COMPARTMENT_ID)
                .region(TEST_REGION)
                .build());

    RepairOrderCollection col1 = respPage1.getRepairOrderCollection();
    assertEquals(2, col1.getItems().size());
    String nextToken = respPage1.getOpcNextPage();
    assertNotNull(nextToken);

    var respPage2 =
        repairOrderClient.listRepairOrders(
            ListRepairOrdersRequest.builder()
                .page(nextToken)
                .limit(2)
                .sortBy(ListRepairOrdersRequest.SortBy.TimeCreated)
                .sortOrder(SortOrder.Asc)
                .overrideCompartmentId(TEST_COMPARTMENT_ID)
                .region(TEST_REGION)
                .build());

    RepairOrderCollection col2 = respPage2.getRepairOrderCollection();
    assertEquals(1, col2.getItems().size());

    Set<String> ids =
        Stream.concat(col1.getItems().stream(), col2.getItems().stream())
            .map(RepairOrderSummary::getId)
            .collect(Collectors.toSet());

    assertThat(ids).containsExactlyInAnyOrder(ro1Id, ro2Id, ro3Id);
  }

  private Part getPartData() {
    return new Part(
        RmcId.generate(RmcIdType.Catalog.PART),
        null, // oraclePartNumber
        null, // manufacturerPartNumber
        null, // vendorName
        "Intel", // manufacturerName
        null, // sourceFrom
        null, // marketingPartNumber
        null, // description
        RmcId.generate(RmcIdType.Catalog.PART_CATEGORY), // partCategoryId
        false, // isSerialized
        null, // partDestructionDetails
        false, // isRma
        null, // catalogSourceUrl
        PartDetailsSource.Mpc, // source
        false, // isAliased
        null, // aliasedPartId
        null // isSpared
        );
  }

  private List<String> getFilterChangeOrderTypes() {
    return List.of(
        ChangeOrderType.DISK_REPAIR.name(),
        ChangeOrderType.GB200_DISK_REPAIR.name(),
        ChangeOrderType.NVSWITCH_REPAIR.name(),
        ChangeOrderType.NETWORK_LINK_REPAIR.name(),
        ChangeOrderType.SERVER_REPAIR.name(),
        ChangeOrderType.GB200_SERVER_REPAIR.name(),
        ChangeOrderType.NETWORK_LEAF_SPINE_LINK_REPAIR.name());
  }

  public static Map<?, ?> diagnosticProbeOutputAsMap(String diagnosticProbeOutput) {
    JsonNode json = ProbeService.asJson(diagnosticProbeOutput);
    Map<?, ?> map = YAML_MAPPER.convertValue(json, Map.class);
    return map;
  }
}
