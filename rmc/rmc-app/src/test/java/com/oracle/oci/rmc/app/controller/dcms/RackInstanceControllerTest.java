package com.oracle.oci.rmc.app.controller.dcms;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import com.oracle.bmc.model.BmcException;
import com.oracle.oci.rmc.changemanagement.api.model.common.ChangeOrderState;
import com.oracle.oci.rmc.client.DcmsClient;
import com.oracle.oci.rmc.client.model.AssetLifecycleState;
import com.oracle.oci.rmc.client.model.CreateRackInstanceDetails;
import com.oracle.oci.rmc.client.model.MoveAssetDetails;
import com.oracle.oci.rmc.client.model.MoveRackInstanceDetails;
import com.oracle.oci.rmc.client.model.RackInstance;
import com.oracle.oci.rmc.client.model.RackInstanceAccessLevel;
import com.oracle.oci.rmc.client.model.RackInstanceLifecycleState;
import com.oracle.oci.rmc.client.model.UpdateRackInstanceDetails;
import com.oracle.oci.rmc.client.model.UpdateRackInstancesBulkDetails;
import com.oracle.oci.rmc.client.requests.CreateRackInstanceRequest;
import com.oracle.oci.rmc.client.requests.DeleteRackInstanceRequest;
import com.oracle.oci.rmc.client.requests.GetAssetRequest;
import com.oracle.oci.rmc.client.requests.GetRackInstanceRequest;
import com.oracle.oci.rmc.client.requests.ListRackInstancesRequest;
import com.oracle.oci.rmc.client.requests.MoveAssetRequest;
import com.oracle.oci.rmc.client.requests.MoveRackInstanceRequest;
import com.oracle.oci.rmc.client.requests.UpdateRackInstanceRequest;
import com.oracle.oci.rmc.client.requests.UpdateRackInstancesBulkRequest;
import com.oracle.oci.rmc.client.responses.CreateRackInstanceResponse;
import com.oracle.oci.rmc.client.responses.GetAssetResponse;
import com.oracle.oci.rmc.client.responses.GetRackInstanceResponse;
import com.oracle.oci.rmc.client.responses.UpdateRackInstanceResponse;
import com.oracle.oci.rmc.client.responses.UpdateRackInstancesBulkResponse;
import com.oracle.oci.rmc.dcms.api.model.entity.AssetLocation;
import com.oracle.oci.rmc.dcms.api.model.entity.RackInstanceDetails;
import com.oracle.oci.rmc.dcms.api.model.entity.RackModel;
import com.oracle.oci.rmc.dcms.api.model.entity.RackPosition;
import com.oracle.oci.rmc.dcms.api.model.entity.RegionEntity;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.Asset;
import com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetCreationDetailsBuilder;
import com.oracle.oci.rmc.dcms.impl.dal.testutil.DcmsRepositoryTestUtil;
import com.oracle.oci.rmc.model.RmcId;
import com.oracle.oci.rmc.model.RmcIdType;
import com.oracle.oci.sfw.micronaut.client.offline.OfflineAuthProperties;
import io.micronaut.context.annotation.Requires;
import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import jakarta.inject.Inject;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInfo;

@MicronautTest
@OfflineAuthProperties
@Requires(property = "database.type", value = "ORACLE")
public class RackInstanceControllerTest {

  @Inject private DcmsClient dcmsClient;
  @Inject private DcmsRepositoryTestUtil databaseTestUtil;

  private RegionEntity regionEntity;
  private String regionCode;
  private RackModel rackModel;
  private RackModel rackModel2;
  private RackPosition rackPosition2;
  private AssetLocation assetLocation;
  private String serial;
  private RackInstanceDetails deleteRackInstanceDetails;
  private RackInstanceDetails moveRackInstanceDetails;

  private Asset asset;
  private Asset ring1Asset;

  public RackInstanceControllerTest(
      DcmsClient dcmsClient, DcmsRepositoryTestUtil databaseTestUtil) {
    this.dcmsClient = dcmsClient;
    this.databaseTestUtil = databaseTestUtil;
  }

  @BeforeEach
  void init(TestInfo testInfo) {
    databaseTestUtil.createRackPlatform("Reg_Comp_E2-2C_Rack.01", "sk-platform");
    databaseTestUtil.setupMockInventoryData();
    this.regionEntity = databaseTestUtil.getRegionEntity();
    this.regionCode = regionEntity.airportCode();
    setupForDataRackInstance();
    setupForTestDeleteRackInstance(testInfo);
    setupForTestMoveRackInstance(testInfo);
  }

  public void setupForDataRackInstance() {
    assetLocation = databaseTestUtil.createAssetLocationWithAisle(DcmsRepositoryTestUtil.AISLE_A);
    // Create additional rackModels to test pagination
    var rackPosition =
        databaseTestUtil.createRackPosition("101", databaseTestUtil.getRoomEntity(), 0, 5);
    rackModel =
        databaseTestUtil.createAndGetComputeRackModel(
            rackPosition, regionEntity.airportCode(), databaseTestUtil.getSiteEntity());
    serial = "SN." + UUID.randomUUID();
    asset =
        databaseTestUtil.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(databaseTestUtil.getRegionEntity().id())
                .serialNumber(serial)
                .additionalProperties(
                    new com.oracle.oci.rmc.dcms.api.model.entity.inventory
                        .AssetAdditionalProperties(
                        null,
                        null,
                        null,
                        "sk-platform",
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null))
                .lifecycleState(
                    com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetLifecycleState
                        .IN_TRANSIT)
                .build());
    ring1Asset =
        databaseTestUtil.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(databaseTestUtil.getRegionEntity().id())
                .serialNumber(RmcId.generate(RmcIdType.Inventory.ASSET).toString())
                .additionalProperties(
                    new com.oracle.oci.rmc.dcms.api.model.entity.inventory
                        .AssetAdditionalProperties(
                        null,
                        null,
                        null,
                        "sk-platform",
                        "RING1",
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null))
                .lifecycleState(
                    com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetLifecycleState
                        .IN_TRANSIT)
                .build());
  }

  public void setupForTestDeleteRackInstance(TestInfo testInfo) {
    Asset asset =
        databaseTestUtil.createAsset(
            AssetCreationDetailsBuilder.builder()
                .regionId(databaseTestUtil.getRegionEntity().id())
                .serialNumber("SN." + UUID.randomUUID())
                .additionalProperties(
                    new com.oracle.oci.rmc.dcms.api.model.entity.inventory
                        .AssetAdditionalProperties(
                        null,
                        null,
                        null,
                        "sk-platform",
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null))
                .lifecycleState(
                    com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetLifecycleState
                        .AVAILABLE)
                .build());
    databaseTestUtil.updateAssetLifecycleState(
        asset,
        asset.version(),
        com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetLifecycleState.IN_TRANSIT);
    if (testInfo.getDisplayName().contains("testDeleteRackInstance")) {
      deleteRackInstanceDetails =
          databaseTestUtil.createRackInstance(databaseTestUtil.getRegionEntity(), asset, rackModel);
      Asset inTransitAsset = databaseTestUtil.getAsset(asset.id());
      databaseTestUtil.updateAssetLifecycleState(
          inTransitAsset,
          inTransitAsset.version(),
          com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetLifecycleState.RECEIVED);
      Asset receivedAsset = databaseTestUtil.getAsset(inTransitAsset.id());
      databaseTestUtil.updateAssetLifecycleState(
          receivedAsset,
          receivedAsset.version(),
          com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetLifecycleState.INSTALLED);
    }
  }

  public void setupForTestMoveRackInstance(TestInfo testInfo) {
    if (testInfo.getDisplayName().contains("testMoveRackInstance")) {
      Asset asset =
          databaseTestUtil.createAsset(
              AssetCreationDetailsBuilder.builder()
                  .regionId(databaseTestUtil.getRegionEntity().id())
                  .serialNumber("SN." + UUID.randomUUID())
                  .additionalProperties(
                      new com.oracle.oci.rmc.dcms.api.model.entity.inventory
                          .AssetAdditionalProperties(
                          null,
                          null,
                          null,
                          "sk-platform",
                          null,
                          null,
                          null,
                          null,
                          null,
                          null,
                          null,
                          null))
                  .lifecycleState(
                      com.oracle.oci.rmc.dcms.api.model.entity.inventory.AssetLifecycleState
                          .IN_TRANSIT)
                  .build());

      moveRackInstanceDetails =
          databaseTestUtil.createRackInstance(databaseTestUtil.getRegionEntity(), asset, rackModel);

      rackPosition2 =
          databaseTestUtil.createRackPosition("102", databaseTestUtil.getRoomEntity(), 0, 6);
      rackModel2 =
          databaseTestUtil.createAndGetComputeRackModel(
              rackPosition2, regionEntity.airportCode(), databaseTestUtil.getSiteEntity());
      databaseTestUtil.createAndGetChangeOrder(
          asset.id(), asset.serialNumber(), ChangeOrderState.ACTIONABLE, regionCode);
    }
  }

  @AfterEach
  void tearDown() {
    databaseTestUtil.deleteAllEntities();
  }

  @Test
  public void testCreateRackInstance() {
    var response = createRackInstance();

    assertNotNull(response);
    assertNotNull(response.getRackInstance());
    assertNotNull(response.getRackInstance().getId());
  }

  @Test
  public void testListRackInstances() {
    createRackInstance();
    var response =
        dcmsClient.listRackInstances(ListRackInstancesRequest.builder().region(regionCode).build());

    assertNotNull(response);
    assertNotNull(response.getRackInstanceCollection());
    assertEquals(3, response.getRackInstanceCollection().getItems().size());

    response =
        dcmsClient.listRackInstances(
            ListRackInstancesRequest.builder().region(regionCode).serialNumber(serial).build());
    assertFalse(response.getRackInstanceCollection().getItems().isEmpty());
    assertEquals(1, response.getRackInstanceCollection().getItems().size());

    response =
        dcmsClient.listRackInstances(
            ListRackInstancesRequest.builder()
                .region(regionCode)
                .manufacturerName("dummyMN")
                .build());
    assertTrue(response.getRackInstanceCollection().getItems().isEmpty());

    response =
        dcmsClient.listRackInstances(
            ListRackInstancesRequest.builder()
                .region(regionCode)
                .lifecycleState(RackInstanceLifecycleState.Shipping)
                .build());
    assertFalse(response.getRackInstanceCollection().getItems().isEmpty());
  }

  @Test
  void testListRackInstancesWithPagination() {
    createRackInstance();
    // Pagination with limit value - Success
    var listedRackInstances =
        dcmsClient.listRackInstances(
            ListRackInstancesRequest.builder().region(regionCode).limit(1).build());
    assertNotNull(listedRackInstances);
    assertEquals(200, listedRackInstances.get__httpStatusCode__());
    assertEquals(1, listedRackInstances.getRackInstanceCollection().getItems().size());

    String page = listedRackInstances.getOpcNextPage();

    listedRackInstances =
        dcmsClient.listRackInstances(
            ListRackInstancesRequest.builder().region(regionCode).limit(1).page(page).build());

    // At least 1 more (created above) should exist
    var count = listedRackInstances.getRackInstanceCollection().getItems().size();
    Assertions.assertTrue(count >= 1);

    // higher pagination parameters - limit
    var listedRackInstances3 =
        dcmsClient.listRackInstances(
            ListRackInstancesRequest.builder().region(regionCode).limit(1001).build());
    assertNotNull(listedRackInstances3);
    assertEquals(200, listedRackInstances3.get__httpStatusCode__());
    assertEquals(3, listedRackInstances3.getRackInstanceCollection().getItems().size());

    var listedRackInstances4 =
        assertThrows(
            BmcException.class,
            () ->
                dcmsClient.listRackInstances(
                    ListRackInstancesRequest.builder().region(regionCode).page("2").build()));
    assertNotNull(listedRackInstances4);
    assertEquals(400, listedRackInstances4.getStatusCode());
    assertThat(listedRackInstances4.getMessage()).contains("page token");
  }

  @Test
  public void testUpdateRackInstance() {

    var createRackInstanceResponse = createRackInstance();

    assertTrue(
        createRackInstanceResponse.get__httpStatusCode__() >= 200
            && createRackInstanceResponse.get__httpStatusCode__() < 300);
    assertNotNull(createRackInstanceResponse.getRackInstance());
    assertNotNull(createRackInstanceResponse.getRackInstance().getId());

    // Empty payload
    try {
      dcmsClient.updateRackInstance(
          UpdateRackInstanceRequest.builder()
              .region(regionCode)
              .rackInstanceId(createRackInstanceResponse.getRackInstance().getId())
              .updateRackInstanceDetails(UpdateRackInstanceDetails.builder().build())
              .build());
      fail("Expected RenderableException was not thrown");
    } catch (BmcException re) {
      assertTrue(
          re.getMessage()
              .contains(
                  "Payload cannot be empty for update RackInstance: instanceId = "
                      + createRackInstanceResponse.getRackInstance().getId()));
    }

    // Update Time fields

    assertNull(createRackInstanceResponse.getRackInstance().getTimeHandedOver());
    assertNull(createRackInstanceResponse.getRackInstance().getTimeMeterOn());
    assertNull(createRackInstanceResponse.getRackInstance().getTransitionComment());

    ZonedDateTime meterOnTime = ZonedDateTime.now();
    ZonedDateTime poweredOnTime = meterOnTime.minusDays(3);
    ZonedDateTime handedOverTime = meterOnTime.minusDays(5);

    UpdateRackInstanceResponse updatedResponse =
        dcmsClient.updateRackInstance(
            UpdateRackInstanceRequest.builder()
                .region(regionCode)
                .rackInstanceId(createRackInstanceResponse.getRackInstance().getId())
                .updateRackInstanceDetails(
                    UpdateRackInstanceDetails.builder()
                        .timeHandedOver(handedOverTime)
                        .timeMeterOn(meterOnTime)
                        .transitionComment("testComment")
                        .build())
                .build());

    assertTrue(
        updatedResponse.get__httpStatusCode__() >= 200
            && updatedResponse.get__httpStatusCode__() < 300);
    assertNotNull(updatedResponse.getRackInstance());

    assertNotNull(updatedResponse.getRackInstance().getTimeMeterOn().toLocalDateTime());
    assertNotNull(updatedResponse.getRackInstance().getTimeHandedOver().toLocalDateTime());
    assertEquals("testComment", updatedResponse.getRackInstance().getTransitionComment());

    // List API
    var listRackInstancesResponse =
        dcmsClient.listRackInstances(
            ListRackInstancesRequest.builder()
                .region(regionCode)
                .serialNumber(asset.serialNumber())
                .build());
    assertFalse(listRackInstancesResponse.getRackInstanceCollection().getItems().isEmpty());
    assertEquals(1, listRackInstancesResponse.getRackInstanceCollection().getItems().size());

    var rackInstance = listRackInstancesResponse.getRackInstanceCollection().getItems().get(0);

    assertNotNull(rackInstance.getTimeMeterOn().toLocalDateTime());
    assertNotNull(rackInstance.getTimeHandedOver().toLocalDateTime());
    assertEquals("testComment", rackInstance.getTransitionComment());
  }

  @Test
  public void testUpdateRackInstancesBulk() {
    // Create a rack instance
    var createRackInstanceResponse = createRackInstance();
    assertRackInstanceCreatedSuccessfully(createRackInstanceResponse);

    // Verify initial state
    assertNull(createRackInstanceResponse.getRackInstance().getTimePoweredOn());
    assertNull(createRackInstanceResponse.getRackInstance().getTimeHandedOver());
    assertNull(createRackInstanceResponse.getRackInstance().getTimeMeterOn());
    assertNull(createRackInstanceResponse.getRackInstance().getTransitionComment());

    // Define time fields
    ZonedDateTime meterOnTime = ZonedDateTime.now();
    ZonedDateTime poweredOnTime = meterOnTime.minusDays(3);
    ZonedDateTime handedOverTime = meterOnTime.minusDays(5);
    ZonedDateTime futureHandedOverTime = meterOnTime.plusDays(1);

    // Test failure scenarios
    testFailureScenario(
        createRackInstanceResponse,
        futureHandedOverTime,
        meterOnTime,
        "Incorrect Rack Serial Number",
        "incorrect serial");

    // Test success scenario with dryRun true
    testSuccessScenario(
        createRackInstanceResponse, futureHandedOverTime, meterOnTime, poweredOnTime, true);

    var listRackInstancesResponse =
        dcmsClient.listRackInstances(
            ListRackInstancesRequest.builder()
                .region(regionCode)
                .serialNumber(createRackInstanceResponse.getRackInstance().getSerialNumber())
                .build());
    assertFalse(listRackInstancesResponse.getRackInstanceCollection().getItems().isEmpty());
    assertEquals(1, listRackInstancesResponse.getRackInstanceCollection().getItems().size());

    var rackInstance = listRackInstancesResponse.getRackInstanceCollection().getItems().get(0);

    // Values will be empty since dryRun = true
    assertNull(rackInstance.getTimeMeterOn());
    assertNull(rackInstance.getTimeHandedOver());
    assertNull(rackInstance.getTimePoweredOn());
    assertNull(rackInstance.getTransitionComment());

    // Test success scenario with dryRun false
    testSuccessScenario(
        createRackInstanceResponse, futureHandedOverTime, meterOnTime, poweredOnTime, false);

    var listRackInstancesResponse1 =
        dcmsClient.listRackInstances(
            ListRackInstancesRequest.builder()
                .region(regionCode)
                .serialNumber(createRackInstanceResponse.getRackInstance().getSerialNumber())
                .build());
    assertFalse(listRackInstancesResponse1.getRackInstanceCollection().getItems().isEmpty());
    assertEquals(1, listRackInstancesResponse1.getRackInstanceCollection().getItems().size());

    var rackInstance1 = listRackInstancesResponse1.getRackInstanceCollection().getItems().get(0);

    assertNotNull(rackInstance1.getTimeMeterOn().toLocalDateTime());
    assertNotNull(rackInstance1.getTimeHandedOver().toLocalDateTime());
    assertEquals("testComment", rackInstance1.getTransitionComment());
  }

  @Test
  public void testGetRackInstanceById() {

    var rackInstanceResponse = createRackInstance();

    var getRackInstanceResponse =
        dcmsClient.getRackInstance(
            GetRackInstanceRequest.builder()
                .region(regionCode)
                .rackInstanceId(rackInstanceResponse.getRackInstance().getId())
                .build());
    assertNotNull(getRackInstanceResponse);
    assertNotNull(getRackInstanceResponse.getRackInstance());
    assertEquals(
        getRackInstanceResponse.getRackInstance().getId(),
        rackInstanceResponse.getRackInstance().getId());

    var notFoundError =
        Assertions.assertThrows(
            BmcException.class,
            () ->
                dcmsClient.getRackInstance(
                    GetRackInstanceRequest.builder()
                        .region(regionCode)
                        .rackInstanceId("1-dcms-rackinstance-e27f6c4da5884529bbf6d2733e7b366c")
                        .build()));
    assertEquals(404, notFoundError.getStatusCode());
  }

  @Test
  public void testRackInstanceListGetForAccessLevel() {
    var rackInstanceResponse = createRackInstance();

    var getRackInstanceResponse =
        dcmsClient.getRackInstance(
            GetRackInstanceRequest.builder()
                .region(regionCode)
                .rackInstanceId(rackInstanceResponse.getRackInstance().getId())
                .build());
    assertNotNull(getRackInstanceResponse);
    assertNotNull(getRackInstanceResponse.getRackInstance().getAccessLevel());

    var listRackInstancesResponse1 =
        dcmsClient.listRackInstances(
            ListRackInstancesRequest.builder()
                .region(regionCode)
                .serialNumber(getRackInstanceResponse.getRackInstance().getSerialNumber())
                .build());
    assertFalse(listRackInstancesResponse1.getRackInstanceCollection().getItems().isEmpty());
    assertEquals(1, listRackInstancesResponse1.getRackInstanceCollection().getItems().size());
    assertNotNull(
        listRackInstancesResponse1.getRackInstanceCollection().getItems().get(0).getAccessLevel());
  }

  @Test
  public void testRackInstanceGetForProtectedAccessLevel() {
    var rackInstanceResponse =
        dcmsClient.createRackInstance(
            CreateRackInstanceRequest.builder()
                .region(rackModel.region())
                .createRackInstanceDetails(
                    CreateRackInstanceDetails.builder()
                        .rackSerial(ring1Asset.serialNumber())
                        .rackNumber(rackModel.rackPosition().rackNumber())
                        .isSkipDevicesInRackValidation(true)
                        .build())
                .build());

    var getRackInstanceResponse =
        dcmsClient.getRackInstance(
            GetRackInstanceRequest.builder()
                .region(regionCode)
                .rackInstanceId(rackInstanceResponse.getRackInstance().getId())
                .build());
    assertNotNull(getRackInstanceResponse);
    assertNotNull(getRackInstanceResponse.getRackInstance().getAccessLevel());
    assertEquals(
        RackInstanceAccessLevel.Ring1, getRackInstanceResponse.getRackInstance().getAccessLevel());
  }

  @Test
  public void testDeleteRackInstance() {
    GetRackInstanceResponse getRackInstanceResponse =
        dcmsClient.getRackInstance(
            GetRackInstanceRequest.builder()
                .region(regionCode)
                .rackInstanceId(deleteRackInstanceDetails.id())
                .build());

    // try to delete rack instance when asset is installed.
    assertThrows(
        BmcException.class,
        () ->
            dcmsClient.deleteRackInstance(
                DeleteRackInstanceRequest.builder()
                    .region(regionCode)
                    .rackInstanceId(getRackInstanceResponse.getRackInstance().getId())
                    .ifMatch(getRackInstanceResponse.getEtag())
                    .build()));

    // Move the rack's asset to a storage location before trying to delete the RackInstance,
    // this will unset rackInstance id from asset and will allow rackInstance delete/unassign
    GetAssetResponse rackAsset =
        dcmsClient.getAsset(
            GetAssetRequest.builder()
                .assetId(getRackInstanceResponse.getRackInstance().getAssetId())
                .region(regionCode)
                .build());
    dcmsClient.moveAsset(
        MoveAssetRequest.builder()
            .assetId(getRackInstanceResponse.getRackInstance().getAssetId())
            .region(regionCode)
            .ifMatch(rackAsset.getEtag())
            .moveAssetDetails(
                MoveAssetDetails.builder()
                    .locationId(assetLocation.id().toString())
                    .toLifecycleState(AssetLifecycleState.Failed)
                    .build())
            .build());

    // Now delete the rack instance itself
    var response =
        dcmsClient.deleteRackInstance(
            DeleteRackInstanceRequest.builder()
                .region(regionCode)
                .rackInstanceId(getRackInstanceResponse.getRackInstance().getId())
                .ifMatch(getRackInstanceResponse.getEtag())
                .build());
    assertNotNull(response);
    assertEquals(response.get__httpStatusCode__(), 204);
    var rackInstances1 =
        dcmsClient.listRackInstances(
            ListRackInstancesRequest.builder().region(regionCode).serialNumber(serial).build());
    assertTrue(rackInstances1.getRackInstanceCollection().getItems().isEmpty());
  }

  @Test
  public void testMoveRackInstance() {
    GetRackInstanceResponse getRackInstanceResponse =
        dcmsClient.getRackInstance(
            GetRackInstanceRequest.builder()
                .rackInstanceId(moveRackInstanceDetails.id())
                .region(regionCode)
                .build());
    var response =
        dcmsClient.moveRackInstance(
            MoveRackInstanceRequest.builder()
                .region(regionCode)
                .rackInstanceId(moveRackInstanceDetails.id())
                .moveRackInstanceDetails(
                    MoveRackInstanceDetails.builder()
                        .rackNumber(rackPosition2.rackNumber())
                        .build())
                .ifMatch(getRackInstanceResponse.getEtag())
                .build());
    assertNotNull(response);
    assertEquals(response.get__httpStatusCode__(), 200);
    RackInstance rackInstance = response.getRackInstance();
    assertEquals(rackModel2.id().toString(), rackInstance.getRackModelId());

    List<RackModel> rackModels = databaseTestUtil.getRackModelByRegion(regionCode);
    RackModel updatedRackModel =
        rackModels.stream().filter(rm -> rm.id().equals(rackModel2.id())).toList().get(0);
    assertEquals("INSTANCE_ASSIGNED", updatedRackModel.lifecycleState().toString());
    RackModel oldRackModel =
        rackModels.stream().filter(rm -> rm.id().equals(rackModel.id())).toList().get(0);
    assertEquals("PLATFORM_ASSIGNED", oldRackModel.lifecycleState().toString());
  }

  private CreateRackInstanceResponse createRackInstance() {

    return dcmsClient.createRackInstance(
        CreateRackInstanceRequest.builder()
            .region(rackModel.region())
            .createRackInstanceDetails(
                CreateRackInstanceDetails.builder()
                    .rackSerial(asset.serialNumber())
                    .rackNumber(rackModel.rackPosition().rackNumber())
                    .isSkipDevicesInRackValidation(true)
                    .build())
            .build());
  }

  private void assertRackInstanceCreatedSuccessfully(CreateRackInstanceResponse response) {
    assertTrue(response.get__httpStatusCode__() >= 200 && response.get__httpStatusCode__() < 300);
    assertNotNull(response.getRackInstance());
    assertNotNull(response.getRackInstance().getId());
  }

  private void testFailureScenario(
      CreateRackInstanceResponse createRackInstanceResponse,
      ZonedDateTime handedOverTime,
      ZonedDateTime meterOnTime,
      String expectedErrorMessage,
      String serialNumber) {
    UpdateRackInstanceDetails updateDetails =
        buildUpdateDetails(handedOverTime, meterOnTime, serialNumber);
    UpdateRackInstancesBulkResponse response = sendBulkUpdateRequest(updateDetails, false);
    assertFailureResponse(response, serialNumber, expectedErrorMessage);
  }

  private void testSuccessScenario(
      CreateRackInstanceResponse createRackInstanceResponse,
      ZonedDateTime handedOverTime,
      ZonedDateTime meterOnTime,
      ZonedDateTime poweredOnTime,
      boolean dryRun) {
    UpdateRackInstanceDetails updateDetails =
        buildUpdateDetails(
            handedOverTime,
            meterOnTime,
            createRackInstanceResponse.getRackInstance().getSerialNumber());
    UpdateRackInstancesBulkResponse response = sendBulkUpdateRequest(updateDetails, dryRun);
    assertSuccessResponse(response, createRackInstanceResponse.getRackInstance().getSerialNumber());
  }

  private UpdateRackInstanceDetails buildUpdateDetails(
      ZonedDateTime handedOverTime, ZonedDateTime meterOnTime, String serialNumber) {
    return UpdateRackInstanceDetails.builder()
        .rackSerial(serialNumber)
        .timeHandedOver(handedOverTime)
        .timeMeterOn(meterOnTime)
        .transitionComment("testComment")
        .build();
  }

  private UpdateRackInstancesBulkResponse sendBulkUpdateRequest(
      UpdateRackInstanceDetails updateDetails, boolean dryRun) {
    List<UpdateRackInstanceDetails> updateDetailsList = List.of(updateDetails);
    var bulkUpdateRequest =
        UpdateRackInstancesBulkRequest.builder()
            .region(regionCode)
            .updateRackInstancesBulkDetails(
                UpdateRackInstancesBulkDetails.builder()
                    .isDryRun(dryRun)
                    .items(updateDetailsList)
                    .build())
            .build();
    return dcmsClient.updateRackInstancesBulk(bulkUpdateRequest);
  }

  private void assertFailureResponse(
      UpdateRackInstancesBulkResponse response,
      String expectedSerialNumber,
      String expectedErrorMessage) {
    assertEquals(
        "Failure", response.getRackInstanceUpdateStatusCollection().getItems().get(0).getStatus());
    assertEquals(
        expectedSerialNumber,
        response.getRackInstanceUpdateStatusCollection().getItems().get(0).getSerialNumber());
    assertEquals(
        expectedErrorMessage,
        response.getRackInstanceUpdateStatusCollection().getItems().get(0).getErrorMessage());
  }

  private void assertSuccessResponse(
      UpdateRackInstancesBulkResponse response, String expectedSerialNumber) {
    assertEquals(
        "Success", response.getRackInstanceUpdateStatusCollection().getItems().get(0).getStatus());
    assertEquals(
        expectedSerialNumber,
        response.getRackInstanceUpdateStatusCollection().getItems().get(0).getSerialNumber());
    assertNull(
        response.getRackInstanceUpdateStatusCollection().getItems().get(0).getErrorMessage());
  }
}
