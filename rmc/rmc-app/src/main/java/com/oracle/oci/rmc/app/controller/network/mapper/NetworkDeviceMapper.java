package com.oracle.oci.rmc.app.controller.network.mapper;

import com.oracle.oci.rmc.app.controller.mapper.TypeConverters;
import com.oracle.oci.rmc.app.rest.model.DeviceLifeCycleStateDetails;
import com.oracle.oci.rmc.app.rest.model.DeviceRoutingContextResponse;
import com.oracle.oci.rmc.app.rest.model.DeviceStateLifeCycleState;
import com.oracle.oci.rmc.app.rest.model.FabricMetadata;
import com.oracle.oci.rmc.app.rest.model.HostRecordResponse;
import com.oracle.oci.rmc.app.rest.model.LocationMetadata;
import com.oracle.oci.rmc.app.rest.model.NetworkDeviceResponse;
import com.oracle.oci.rmc.app.rest.model.NetworkInterfaceResponse;
import com.oracle.oci.rmc.app.rest.model.NetworkPortResponse;
import com.oracle.oci.rmc.app.rest.model.PeerInterfaceResponse;
import com.oracle.oci.rmc.network.api.device.Device;
import com.oracle.oci.rmc.network.api.device.DeviceRoutingContext;
import com.oracle.oci.rmc.network.api.device.NetworkDeviceDto;
import com.oracle.oci.rmc.network.api.fabric.NetworkFabricDto;
import com.oracle.oci.rmc.network.api.host.HostRecord;
import com.oracle.oci.rmc.network.api.location.DeviceLocation;
import com.oracle.oci.rmc.network.api.netinterface.NetInterface;
import com.oracle.oci.rmc.network.api.port.NetworkPortDto;
import com.oracle.oci.rmc.network.api.state.DeviceState;
import io.micronaut.context.annotation.Mapper;
import io.micronaut.core.annotation.Nullable;
import jakarta.inject.Singleton;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
@Mapper(conflictStrategy = Mapper.ConflictStrategy.CONVERT)
@SuppressWarnings({
  "PMD.AvoidDeeplyNestedIfStmts",
  "PMD.AvoidInstantiatingObjectsInLoops",
  "PMD.CognitiveComplexity",
  "PMD.CyclomaticComplexity",
  "PMD.UseConcurrentHashMap"
})
public interface NetworkDeviceMapper {
  Logger LOG = LoggerFactory.getLogger(NetworkDeviceMapper.class);

  /**
   * Mapper to map port, interface, routing context and location
   *
   * @param networkDeviceDto
   * @param networkPortMapper
   * @param networkInterfaceMapper
   * @param deviceRoutingContextMapper
   * @return
   */
  default NetworkDeviceResponse toNetworkDeviceResponseObject(
      NetworkDeviceDto networkDeviceDto,
      NetworkPortMapper networkPortMapper,
      NetworkInterfaceMapper networkInterfaceMapper,
      DeviceRoutingContextMapper deviceRoutingContextMapper) {

    List<NetworkPortResponse> portResponses =
        networkPortMapper.toApiList(new ArrayList<>(networkDeviceDto.deviceModel().ports()));

    List<DeviceRoutingContextResponse> routingContexts = new ArrayList<>();

    for (DeviceRoutingContext deviceRoutingContext :
        networkDeviceDto.deviceModel().routingContexts()) {
      DeviceRoutingContextResponse deviceRoutingContextResponse =
          deviceRoutingContextMapper.toApi(deviceRoutingContext);
      routingContexts.add(deviceRoutingContextResponse);
    }

    LocationMetadata locationMetadata =
        extractDeviceLocationMetadata(networkDeviceDto.deviceLocation());

    List<NetworkInterfaceResponse> interfaceResponses =
        createPeerInterfaceResponse(networkDeviceDto, networkInterfaceMapper);

    List<NetworkDeviceResponse> childrenDevices = mapChildrenDevices(networkDeviceDto);

    return toApi(
        networkDeviceDto,
        locationMetadata,
        portResponses,
        interfaceResponses,
        routingContexts,
        childrenDevices);
  }

  /**
   * Method to create peer interface details
   *
   * @return
   */
  private List<NetworkInterfaceResponse> createPeerInterfaceResponse(
      NetworkDeviceDto networkDeviceDto, NetworkInterfaceMapper networkInterfaceMapper) {
    List<NetworkInterfaceResponse> interfaceResponses = new ArrayList<>();

    Map<String, NetworkPortDto> portDTOMap = new HashMap<>();
    for (NetworkPortDto networkPortDTO : networkDeviceDto.networkPortDTO()) {
      portDTOMap.put(networkPortDTO.parentInterfaceId(), networkPortDTO);
    }

    for (NetInterface netInterface : networkDeviceDto.deviceModel().interfaces()) {

      List<PeerInterfaceResponse> peerInterfaceResponseList = new ArrayList<>();

      NetworkPortDto networkPortDTO = portDTOMap.get(netInterface.id().toString());
      if (networkPortDTO != null) {
        peerInterfaceResponseList.add(
            PeerInterfaceResponse.builder()
                .dcmsDeviceId(String.valueOf(networkPortDTO.deviceDcmsId()))
                .deviceName(networkPortDTO.deviceName())
                .interfaceName(networkPortDTO.netInterfaceName())
                .build());
      }

      List<HostRecordResponse> hostRecordResponseList = new ArrayList<>();
      for (HostRecord record : netInterface.hostRecords()) {
        hostRecordResponseList.add(
            HostRecordResponse.builder()
                .id(record.id().toString())
                .ipAddress(record.ipAddress())
                .prefixLength(record.prefixLength())
                .createdBy(record.createdBy())
                .build());
      }

      NetworkInterfaceResponse networkInterfaceResponse =
          networkInterfaceMapper.toApiModelWithPeerInterface(
              netInterface, peerInterfaceResponseList, hostRecordResponseList);

      interfaceResponses.add(networkInterfaceResponse);
    }
    return interfaceResponses;
  }

  default NetworkDeviceResponse toApi(
      NetworkDeviceDto networkDeviceDto,
      LocationMetadata locationMetadata,
      List<NetworkPortResponse> ports,
      List<NetworkInterfaceResponse> interfaces,
      List<DeviceRoutingContextResponse> routingContexts,
      List<NetworkDeviceResponse> childrenDevices) {
    Device model = networkDeviceDto.deviceModel();

    FabricMetadata fabricMetadata = extractFabricMetadata(networkDeviceDto.fabricMetadata());

    DeviceLifeCycleStateDetails deviceLifeCycleStateDetails =
        extractDeviceLifeCycleStateDetails(model.deviceState());

    return NetworkDeviceResponse.builder()
        .id(model.id().toString())
        .serialNumber(networkDeviceDto.serialNumber())
        .deviceName(model.name())
        .dcmsDeviceId(model.dcmsDeviceId().toString())
        .parentDeviceId(model.parentDeviceId() == null ? null : model.parentDeviceId().toString())
        .deviceModel(model.deviceModel())
        .fabricMetadata(fabricMetadata)
        .role(model.role().role())
        .isMonitored(model.isMonitored())
        .configPlatform(toApi(model.configPlatform()))
        .ipAddress(model.ipAddress())
        .deviceIndex(model.deviceIndex())
        .ports(ports)
        .state(deviceLifeCycleStateDetails)
        .locationMetadata(locationMetadata)
        .interfaces(interfaces)
        .routingContexts(routingContexts)
        .childrenDevices(childrenDevices)
        .timeCreated(
            model.timeCreated() == null ? null : model.timeCreated().atZone(ZoneOffset.UTC))
        .timeUpdated(
            model.timeUpdated() == null ? null : model.timeUpdated().atZone(ZoneOffset.UTC))
        .build();
  }

  private static String toApi(@Nullable Device.Platform platform) {
    return platform == null ? null : platform.name();
  }

  default ZonedDateTime toZoneDateTime(Instant instant) {
    return instant == null ? null : TypeConverters.toZoneDateTime(instant);
  }

  default LocationMetadata extractDeviceLocationMetadata(DeviceLocation deviceLocation) {
    if (deviceLocation == null) {
      return LocationMetadata.builder().build();
    }

    return LocationMetadata.builder()
        .building(deviceLocation.buildingName())
        .room(deviceLocation.roomNumber())
        .rackPosition(deviceLocation.rackNumber())
        .rackElevation(deviceLocation.deviceElevation())
        .site(deviceLocation.siteName())
        .dataHall(deviceLocation.dataHallName())
        .build();
  }

  private FabricMetadata extractFabricMetadata(@Nullable NetworkFabricDto fabricDto) {
    if (fabricDto == null) {
      return null;
    }
    return FabricMetadata.builder()
        .id(fabricDto.fabricId())
        .displayName(fabricDto.name())
        .fabricType(fabricDto.type())
        .instance(fabricDto.fabricIndex())
        .fabricVersion(fabricDto.version())
        .build();
  }

  private static DeviceLifeCycleStateDetails extractDeviceLifeCycleStateDetails(
      DeviceState deviceState) {
    return DeviceLifeCycleStateDetails.builder()
        .intendedState(
            deviceState == null
                ? null
                : DeviceStateLifeCycleState.create(
                    deviceState.intendedState().toString().toUpperCase(Locale.US)))
        .currentState(
            deviceState == null
                ? null
                : DeviceStateLifeCycleState.create(
                    deviceState.currentState().toString().toUpperCase(Locale.US)))
        .processState(
            deviceState == null
                ? null
                : deviceState.processState().toString().toUpperCase(Locale.US))
        .timeUpdated(
            deviceState == null || deviceState.timeUpdated() == null
                ? null
                : deviceState.timeUpdated().atZone(ZoneOffset.UTC))
        .build();
  }

  private List<NetworkDeviceResponse> mapChildrenDevices(NetworkDeviceDto networkDeviceDto) {
    if (networkDeviceDto.childrenDevices() == null
        || networkDeviceDto.childrenDevices().isEmpty()) {
      return Collections.emptyList();
    }

    List<NetworkDeviceResponse> childrenDeviceResponses = new ArrayList<>();
    for (Device childDevice : networkDeviceDto.childrenDevices()) {

      NetworkDeviceResponse childDeviceResponse =
          NetworkDeviceResponse.builder()
              .id(childDevice.id().toString())
              .deviceName(childDevice.name())
              .dcmsDeviceId(childDevice.dcmsDeviceId().toString())
              .parentDeviceId(
                  childDevice.parentDeviceId() == null
                      ? null
                      : childDevice.parentDeviceId().toString())
              .deviceModel(childDevice.deviceModel())
              .role(childDevice.role().role())
              .isMonitored(childDevice.isMonitored())
              .configPlatform(toApi(childDevice.configPlatform()))
              .ipAddress(childDevice.ipAddress())
              .deviceIndex(childDevice.deviceIndex())
              .build();
      childrenDeviceResponses.add(childDeviceResponse);
    }

    return childrenDeviceResponses;
  }
}
