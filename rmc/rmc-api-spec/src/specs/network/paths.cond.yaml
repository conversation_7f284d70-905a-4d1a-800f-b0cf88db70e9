# Rotate Secrets
# action : roll forward/rollback
# rotateAllSecrets: boolean. default is false. secretName can be any string value(not null) if rotateAllSecrets is true
# =============================================================================
/regions/{region}/network/config/secretsRotation:
  put:
    operationId: RotateSecrets
    summary: roll forward/back device secret by secretName. if rotateAllSecrets is true, roll forward all secrets
    description: roll forward/back device secret by secretName. if rotateAllSecrets is true, roll forward all secrets
    tags:
      - secrets
      - nmc
    x-obmcs-splat:
      apiType: Asynchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      authorization:
        mode: none
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/IfMatchHeader'
      - $ref: '#/parameters/RetryTokenHeader'
      - name: RotateSecretDetails
        description: Details for secret rotations
        required: true
        in: body
        schema:
          $ref: '#/definitions/RotateSecretDetails'
    x-related-resource: '#/definitions/WorkRequest'
    responses:
      202:
        description: Accepted the request. The file will be saved to object storage
        headers:
          opc-request-id: *ref-opc-request-id
      409:
        $ref: '#/responses/409'
      @include_indented /responses-api-spec-include/source/update-with-if-match-responses.cond.yaml

# DHCP Configs Update Definitions
# =============================================================================
/regions/{region}/network/dhcp/config:
  put:
    operationId: UpdateNetworkDhcpConfig
    summary: Render and update latest configs for DHCP
    description: |
      Render DHCP configs
    tags:
      - UpdateNetworkDhcpConfig
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkDeviceConfig:
          serviceResourceName: NetworkDeviceConfig
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
          actionKind: update
          permissions: [ "UPDATE_DHCP_CONFIG" ]
          # TODO - check if we need this
          reconciliationCanStartAfterSecs: 30
          freeformTags: null
          definedTags: null
      authorization:
        mode: automated
        check: resources['networkDeviceConfig'].grantedPermissions.contains('UPDATE_DHCP_CONFIG')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RetryTokenHeader'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/IfMatchHeader'
    x-related-resource: '#/definitions/UpdateDhcpConfigResponse'
    responses:
      200:
        description: Accepted the request. The file will be saved to object storage
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
        schema:
          $ref: '#/definitions/UpdateDhcpConfigResponse'
      @include_indented /responses-api-spec-include/source/update-with-if-match-responses.cond.yaml

# NMC Get list of network Models and their regions
# =============================================================================
/regions/network/regions:
  get:
    tags:
      - networkRegions
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkRegion:
          actionKind: LIST
          permissions: [ "NET_REGION_READ" ]
          serviceResourceName: NetworkRegion
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkRegion'].grantedPermissions.contains('NET_REGION_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: ListNetworkRegions
    summary: Retrieves regions
    description: Retrieves regions within this application instance.
    parameters:
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/IsActiveQueryParam'
    responses:
      200:
        description: The value of the specified attribute.
        schema:
          $ref: '#/definitions/NetworkRegionCollection'
        headers:
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

# NMC Set Active Region
# =============================================================================
/regions/{region}/network/regions/activate:
  put:
    tags:
      - networkRegions
      - nmc
    x-obmcs-terraform:
      actionType: START
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkRegion:
          actionKind: UPDATE
          permissions: [ "NET_REGION_ACTIVATE" ]
          serviceResourceName: NetworkRegion
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkRegion'].grantedPermissions.contains('NET_REGION_ACTIVATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: ActivateNetworkRegion
    summary: Activate network region
    description: Sets network region as the active region.
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/RequestIdHeader'
    responses:
      200:
        description: The active region.
        schema:
          $ref: "#/definitions/NetworkRegionResponse"
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/update-with-if-match-responses.cond.yaml

# Template Development Definitions
# =============================================================================
/regions/{region}/network/devices/renderConfig:
  post:
    operationId: RenderNetworkDevicesConfig
    summary: Render configs for some devices
    description: |
      Render configs for small subset of devices.
    tags:
      - RenderNetworkDevicesConfig
      - nmc
    x-obmcs-splat:
      apiType: Asynchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkDeviceConfig:
          serviceResourceName: NetworkDeviceConfig
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
          actionKind: CREATE
          permissions: [ "RENDER_DEVICE_CONFIG" ]
          #  TODO - do we need this?
          reconciliationCanStartAfterSecs: 30
          freeformTags: request.body.freeformTags
          definedTags: request.body.definedTags
      authorization:
        mode: automated
        check: resources['networkDeviceConfig'].grantedPermissions.contains('RENDER_DEVICE_CONFIG')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/RetryTokenHeader'
      - $ref: '#/parameters/RequestIdHeader'
      - name: RenderNetworkDevicesConfigDetails
        description: Details for the new config render.
        required: true
        in: body
        schema:
          $ref: '#/definitions/RenderNetworkDevicesConfigDetails'
    x-related-resource: '#/definitions/WorkRequest'
    responses:
      202:
        description: Accepted the request. The file will be saved to object storage
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/create-with-retry-token-responses.cond.yaml

# =============================================================================
/regions/{region}/network/devices/name/{deviceName}/installCertificates:
  post:
    operationId: InstallCertificate
    description: Install/Rotate certificates on device
    tags:
      - networkDeviceCertificate
      - nmc
    x-obmcs-splat:
      apiType: Asynchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        certificates:
          serviceResourceName: NetworkDevice
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
          actionKind: CREATE
          permissions: [ "INSTALL_DEVICE_CERT" ]
          reconciliationCanStartAfterSecs: 30
          freeformTags: request.body.freeformTags
          definedTags: request.body.definedTags
      authorization:
        mode: automated
        check: resources['certificates'].grantedPermissions.contains('INSTALL_DEVICE_CERT')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/RetryTokenHeader'
      - $ref: '#/parameters/DeviceNamePathParam'
      - name: InstallCertificateDetails
        description: Details for certificate installation
        required: true
        in: body
        schema:
          $ref: '#/definitions/InstallCertificateDetails'
    x-related-resource: '#/definitions/WorkRequest'
    responses:
      202:
        description: Accepted the request.
        headers:
          opc-request-id: *ref-opc-request-id
      409:
        $ref: '#/responses/409'
      @include_indented /responses-api-spec-include/source/update-with-if-match-responses.cond.yaml

/regions/{region}/network/devices/name/{deviceName}/renderConfig:
  post:
    operationId: RenderNetworkDevicesConfigSync
    summary: Render config for single device
    description: |
      Render config for Single device.
    tags:
      - RenderNetworkDevicesConfig
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkDeviceConfig:
          serviceResourceName: NetworkDeviceConfig
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
          actionKind: CREATE
          permissions: [ "RENDER_DEVICE_CONFIG" ]
          #  TODO - do we need this?
          reconciliationCanStartAfterSecs: 30
          freeformTags: request.body.freeformTags
          definedTags: request.body.definedTags
      authorization:
        mode: automated
        check: resources['networkDeviceConfig'].grantedPermissions.contains('RENDER_DEVICE_CONFIG')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/RetryTokenHeader'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/IfMatchHeader'
      - $ref: '#/parameters/DeviceNamePathParam'
      - name: RenderNetworkDevicesConfigDetailsInSync
        description: Details for the new config render.
        required: true
        in: body
        schema:
          $ref: '#/definitions/RenderNetworkDevicesConfigDetailsInSync'
    x-related-resource: '#/definitions/ConfigsMetadataResponse'
    responses:
      200:
        description: Configs has been generated successfully
        schema:
          $ref: '#/definitions/ConfigsMetadataResponse'
        headers:
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/create-with-retry-token-responses.cond.yaml
# Namespace Entity Paths
# =============================================================================
/regions/{region}/network/ipam/namespaces:
  post:
    operationId: CreateNamespace
    summary: Creates a new Namespace
    description: |
      Creates a new Namespace
    tags:
      - namespace
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkNamespace:
          serviceResourceName: NetworkNamespace
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
          actionKind: CREATE
          permissions: [ "NET_NAMESPACE_CREATE" ]
      authorization:
        mode: automated
        check: resources['networkNamespace'].grantedPermissions.contains('NET_NAMESPACE_CREATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/RetryTokenHeader'
      - $ref: '#/parameters/RequestIdHeader'
      - name: CreateNamespaceDetails
        description: Details for creating new namespace.
        required: true
        in: body
        schema:
          $ref: '#/definitions/CreateNamespaceDetails'
    x-related-resource: '#/definitions/NamespaceResponse'
    responses:
      200:
        description: The namespace has been created.
        schema:
          $ref: '#/definitions/NamespaceResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/create-with-retry-token-responses.cond.yaml
  get:
    tags:
      - namespace
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkNamespace:
          serviceResourceName: NetworkNamespace
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
          actionKind: LIST
          permissions: [ "NET_NAMESPACE_READ" ]
      authorization:
        mode: automated
        check: resources['networkNamespace'].grantedPermissions.contains('NET_NAMESPACE_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: ListAllNamespace
    summary: List all namespaces
    description: List all namespaces
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/RequestIdHeader'
    responses:
      200:
        description: A Namespace object.
        schema:
          $ref: '#/definitions/NamespaceResponseCollection'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

/regions/{region}/network/ipam/namespaces/{namespace}:
  get:
    tags:
      - namespace
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkNamespace:
          serviceResourceName: NetworkNamespace
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
          actionKind: READ
          permissions: [ "NET_NAMESPACE_READ" ]
      authorization:
        mode: automated
        check: resources['networkNamespace'].grantedPermissions.contains('NET_NAMESPACE_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: GetNamespace
    summary: Get namespace by name
    description: Get namespace by name
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/NamespacePathParam'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/IfMatchHeader'
    responses:
      200:
        description: A Namespace object.
        schema:
          $ref: '#/definitions/NamespaceResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

# Prefix Entity Paths
# =============================================================================
/regions/{region}/network/ipam/namespaces/{namespace}/prefixes:
  post:
    operationId: CreatePrefix
    summary: Creates a new Prefix record.
    description: |
      Creates a new Prefix record.
    tags:
      - prefix
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkNamespaceAttribute:
          serviceResourceName: NetworkNamespaceAttribute
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
          actionKind: CREATE
          permissions: [ "NET_NAMESPACE_ATTRIBUTE_CREATE" ]
      authorization:
        mode: automated
        check: resources['networkNamespaceAttribute'].grantedPermissions.contains('NET_NAMESPACE_ATTRIBUTE_CREATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/NamespacePathParam'
      - $ref: '#/parameters/RetryTokenHeader'
      - $ref: '#/parameters/RequestIdHeader'
      - name: CreatePrefixDetails
        description: Details for creating new prefix.
        required: true
        in: body
        schema:
          $ref: '#/definitions/CreatePrefixDetails'
    x-related-resource: '#/definitions/PrefixResponse'
    responses:
      200:
        description: The Prefix Record has been created.
        schema:
          $ref: '#/definitions/PrefixResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      400:
        description: Bad request or not found
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
      @include_indented /responses-api-spec-include/source/create-with-retry-token-responses.cond.yaml
  get:
    tags:
      - prefix
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkNamespaceAttribute:
          serviceResourceName: NetworkNamespaceAttribute
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
          actionKind: LIST
          permissions: [ "NET_NAMESPACE_ATTRIBUTE_READ" ]
      authorization:
        mode: automated
        check: resources['networkNamespaceAttribute'].grantedPermissions.contains('NET_NAMESPACE_ATTRIBUTE_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: ListPrefixes
    summary: Get all Prefixes filter on query params.
    description: Get all Prefixes filter on query params.
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/NamespacePathParam'
      - $ref: '#/parameters/PrefixIdQueryParam'
      - $ref: '#/parameters/IpVersionQueryParam'
      - $ref: '#/parameters/PrefixGroupQueryParam'
      - $ref: '#/parameters/RequestIdHeader'
    responses:
      200:
        description: List of Prefix objects.
        schema:
          $ref: '#/definitions/PrefixResponseCollection'
        headers:
          opc-request-id: *ref-opc-request-id
      400:
        description: Bad request or not found
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

# IpamPoolTemplate Entity Paths
# =============================================================================
/regions/{region}/network/ipam/namespaces/{namespace}/pool-templates:
  post:
    operationId: CreateIpamPoolTemplate
    summary: Creates a new Ipam pool template record.
    description: |
      Creates a new Ipam pool template record.
    tags:
      - poolTemplate
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkNamespaceAttribute:
          serviceResourceName: NetworkNamespaceAttribute
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
          actionKind: CREATE
          permissions: [ "NET_NAMESPACE_ATTRIBUTE_CREATE" ]
      authorization:
        mode: automated
        check: resources['networkNamespaceAttribute'].grantedPermissions.contains('NET_NAMESPACE_ATTRIBUTE_CREATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/NamespacePathParam'
      - $ref: '#/parameters/RetryTokenHeader'
      - $ref: '#/parameters/RequestIdHeader'
      - name: CreatePoolTemplateDetails
        description: Details for creating new pool template.
        required: true
        in: body
        schema:
          $ref: '#/definitions/CreatePoolTemplateDetails'
    x-related-resource: '#/definitions/PoolTemplateResponse'
    responses:
      200:
        description: The Pool Record has been created.
        schema:
          $ref: '#/definitions/PoolTemplateResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      400:
        description: Bad request or not found
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
      @include_indented /responses-api-spec-include/source/create-with-retry-token-responses.cond.yaml
  get:
    operationId: GetPoolTemplateById
    summary: Get Pool Template by id.
    description: Get Pool Template by id.
    tags:
      - poolTemplate
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkNamespaceAttribute:
          serviceResourceName: NetworkNamespaceAttribute
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
          actionKind: LIST
          permissions: [ "NET_NAMESPACE_ATTRIBUTE_READ" ]
      authorization:
        mode: automated
        check: resources['networkNamespaceAttribute'].grantedPermissions.contains('NET_NAMESPACE_ATTRIBUTE_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/NamespacePathParam'
      - $ref: '#/parameters/FetchByIdQueryParam'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/IfMatchHeader'
    responses:
      200:
        description: A Pool template response object.
        schema:
          $ref: '#/definitions/PoolTemplateResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      400:
        description: Bad request or not found
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

# AllocationRule Entity Paths
# =============================================================================
/regions/{region}/network/ipam/namespaces/{namespace}/ip-allocation-rules:
  post:
    operationId: CreateAllocationRule
    summary: Creates a new ip allocation rule.
    description: |
      Creates a new ip allocation rule.
    tags:
      - allocationRule
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkNamespaceAttribute:
          serviceResourceName: NetworkNamespaceAttribute
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
          actionKind: CREATE
          permissions: [ "NET_NAMESPACE_ATTRIBUTE_CREATE" ]
      authorization:
        mode: automated
        check: resources['networkNamespaceAttribute'].grantedPermissions.contains('NET_NAMESPACE_ATTRIBUTE_CREATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/NamespacePathParam'
      - $ref: '#/parameters/RetryTokenHeader'
      - $ref: '#/parameters/RequestIdHeader'
      - name: CreateAllocationRuleDetails
        description: Details for creating new ip allocation rule.
        required: true
        in: body
        schema:
          $ref: '#/definitions/CreateAllocationRuleDetails'
    x-related-resource: '#/definitions/AllocationRuleResponse'
    responses:
      200:
        description: The allocation rule record that has been created.
        schema:
          $ref: '#/definitions/AllocationRuleResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      400:
        description: Bad request or not found
      @include_indented /responses-api-spec-include/source/create-with-retry-token-responses.cond.yaml
  get:
    operationId: GetAllocationRuleById
    summary: Get allocation rule by id.
    description: Get allocation rule by id.
    tags:
      - allocationRule
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkNamespaceAttribute:
          serviceResourceName: NetworkNamespaceAttribute
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
          actionKind: LIST
          permissions: [ "NET_NAMESPACE_ATTRIBUTE_READ" ]
      authorization:
        mode: automated
        check: resources['networkNamespaceAttribute'].grantedPermissions.contains('NET_NAMESPACE_ATTRIBUTE_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/NamespacePathParam'
      - $ref: '#/parameters/FetchByIdQueryParam'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/IfMatchHeader'
    responses:
      200:
        description: An allocation rule response object.
        schema:
          $ref: '#/definitions/AllocationRuleResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      400:
        description: Bad request or not found
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml
  put:
    operationId: UpdateAllocationRule
    summary: Update existing ip allocation rule
    description: |
      Update existing ip allocation rule.
    tags:
      - allocationRule
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkNamespaceAttribute:
          actionKind: UPDATE
          permissions: [ "NET_IP_ALLOCATION_RULE_UPDATE" ]
          serviceResourceName: NetworkNamespaceAttribute
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkNamespaceAttribute'].grantedPermissions.contains('NET_IP_ALLOCATION_RULE_UPDATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/NamespacePathParam'
      - $ref: '#/parameters/RetryTokenHeader'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/IfMatchHeader'
      - name: UpdateAllocationRuleDetails
        description: Details for updating ip allocation rule
        required: true
        in: body
        schema:
          $ref: '#/definitions/UpdateAllocationRuleDetails'
    x-related-resource: '#/definitions/AllocationRuleResponse'
    responses:
      200:
        description: Accepted the request.updated allocation rule
        headers:
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
        schema:
          $ref: '#/definitions/AllocationRuleResponse'
      409:
        description: Invalidated Retry Token
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id:
            description: Unique identifier for the request
            type: string
      @include_indented /responses-api-spec-include/source/update-with-if-match-responses.cond.yaml

# ASN Entity Paths
# =============================================================================
/regions/{region}/network/autonomous-systems/pools:
  post:
    operationId: CreateAsnpool
    summary: Creates a new Asnpool
    description: |
      Creates a new Asnpool
    tags:
      - asnPool
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkAsnPool:
          serviceResourceName: NetworkAsnPool
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
          actionKind: CREATE
          permissions: [ "NET_ASN_POOL_CREATE" ]
      authorization:
        mode: automated
        check: resources['networkAsnPool'].grantedPermissions.contains('NET_ASN_POOL_CREATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/RetryTokenHeader'
      - $ref: '#/parameters/RequestIdHeader'
      - name: CreateAsnPoolDetails
        description: Details for creating new asnpool
        required: true
        in: body
        schema:
          $ref: '#/definitions/CreateAsnPoolDetails'
    x-related-resource: '#/definitions/AsnPool'
    responses:
      201:
        description: The asnpool has been created.
        schema:
          $ref: '#/definitions/AsnPool'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/create-with-retry-token-responses.cond.yaml

/regions/{region}/network/autonomous-systems/pools/{asnPoolName}:
  get:
    tags:
      - asnPool
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkAsnPool:
          serviceResourceName: NetworkAsnPool
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
          actionKind: READ
          permissions: [ "NET_ASN_POOL_READ" ]
      authorization:
        mode: automated
        check: resources['networkAsnPool'].grantedPermissions.contains('NET_ASN_POOL_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: GetAsnPoolByName
    summary: Get asnpool by name
    description: Get asnpool by name
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/AsnpoolNamePathParam'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/IfMatchHeader'
    responses:
      200:
        description: A Asnpool object.
        schema:
          $ref: '#/definitions/AsnPool'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

/regions/{region}/network/rules/autonomous-systems/templates:
  post:
    operationId: CreateAsnTemplate
    summary: Creates a new AsnTemplate
    description: |
      Creates a new AsnTemplate
    tags:
      - asnTemplate
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkAsnTemplate:
          serviceResourceName: NetworkAsnTemplate
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
          actionKind: CREATE
          permissions: [ "NET_ASN_TEMPLATE_CREATE" ]
      authorization:
        mode: automated
        check: resources['networkAsnTemplate'].grantedPermissions.contains('NET_ASN_TEMPLATE_CREATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/RetryTokenHeader'
      - $ref: '#/parameters/RequestIdHeader'
      - name: CreateAsnTemplateDetails
        description: Details for creating new AsnTemplate
        required: true
        in: body
        schema:
          $ref: '#/definitions/CreateAsnTemplateDetails'
    x-related-resource: '#/definitions/AsnTemplateResponse'
    responses:
      201:
        description: The AsnTemplate has been created.
        schema:
          $ref: '#/definitions/AsnTemplateResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/create-with-retry-token-responses.cond.yaml
  get:
    tags:
      - asnTemplate
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkAsnTemplate:
          serviceResourceName: NetworkAsnTemplate
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
          actionKind: READ
          permissions: [ "NET_ASN_TEMPLATE_READ" ]
      authorization:
        mode: automated
        check: resources['networkAsnTemplate'].grantedPermissions.contains('NET_ASN_TEMPLATE_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: GetListAsnTemplate
    summary: Get list of asntemplate
    description: Get list of asntemplate
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/AsnTemplateRoleQueryParam'
      - $ref: '#/parameters/AsnTemplatePoolNameQueryParam'
      - $ref: '#/parameters/RequestIdHeader'
    x-related-resource: '#/definitions/AsnTemplateResponseCollection'
    responses:
      200:
        description: A list of asntemplate  object.
        schema:
          $ref: '#/definitions/AsnTemplateResponseCollection'
        headers:
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/list-responses.cond.yaml

/regions/{region}/network/rules/autonomous-systems/templates/{id}:
  get:
    tags:
      - asnTemplate
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkAsnTemplate:
          serviceResourceName: NetworkAsnTemplate
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
          actionKind: READ
          permissions: [ "NET_ASN_TEMPLATE_READ" ]
      authorization:
        mode: automated
        check: resources['networkAsnTemplate'].grantedPermissions.contains('NET_ASN_TEMPLATE_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: GetAsnTemplateById
    summary: Get asntemplate by id
    description: Get asntemplate by id
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/AsnTemplateIdPathParam'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/IfMatchHeader'
    x-related-resource: '#/definitions/AsnTemplateResponse'
    responses:
      200:
        description: A list of asntemplate  object.
        schema:
          $ref: '#/definitions/AsnTemplateResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

# Firmware image Entity Paths
# =============================================================================
/regions/{region}/network/rules/firmware-images:
  post:
    operationId: CreateFirmwareImage
    summary: Creates a new FirmwareImage
    description: |
      Creates a new FirmwareImage
    tags:
      - firmwareImage
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkFirmwareImage:
          actionKind: CREATE
          permissions: [ "NET_FIRMWARE_IMAGE_CREATE" ]
          serviceResourceName: NetworkFirmwareImage
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkFirmwareImage'].grantedPermissions.contains('NET_FIRMWARE_IMAGE_CREATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/RetryTokenHeader'
      - $ref: '#/parameters/RequestIdHeader'
      - name: CreateFirmwareImageDetails
        description: Details for creating new firmware image.
        required: true
        in: body
        schema:
          $ref: '#/definitions/CreateFirmwareImageDetails'
    x-related-resource: '#/definitions/FirmwareImageResponse'
    responses:
      200:
        description: The firmware image has been created.
        schema:
          $ref: '#/definitions/FirmwareImageResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/create-with-retry-token-responses.cond.yaml
  get:
    tags:
      - firmwareImage
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkFirmwareImage:
          actionKind: LIST
          permissions: [ "NET_FIRMWARE_IMAGE_READ" ]
          serviceResourceName: NetworkFirmwareImage
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkFirmwareImage'].grantedPermissions.contains('NET_FIRMWARE_IMAGE_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: GetFirmwareImages
    summary: Retrieve all FirmwareImages with optional filters
    description: Retrieve all FirmwareImages. Use query parameters to filter results by device model, role, or platform.
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - name: id
        in: query
        required: false
        type: string
        description: Filter by rmcid of the firmwareImage.
      - name: fileName
        in: query
        required: false
        type: string
        description: Filter by firmware file name.
      - $ref: '#/parameters/RequestIdHeader'
    responses:
      200:
        description: A firmware image object.
        schema:
          $ref: '#/definitions/FirmwareImageCollection'
        headers:
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

/regions/{region}/network/rules/firmware-images/{firmwareImageId}/actions/refreshPar:
  post:
    operationId: RefreshFirmwareImagePar
    summary: Manually refresh a firmware image PAR
    description: |
      Manually refresh a firmware image PAR and update the URL
    tags:
      - firmwareImage
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkFirmwareImage:
          actionKind: UPDATE
          permissions: [ "NET_FIRMWARE_IMAGE_UPDATE" ]
          serviceResourceName: NetworkFirmwareImage
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkFirmwareImage'].grantedPermissions.contains('NET_FIRMWARE_IMAGE_UPDATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/FirmwareImagePathParam'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/RegionPathParam'
    x-related-resource: '#/definitions/FirmwareImageResponse'
    responses:
      200:
        description: Accepted the request. The PAR was updated.
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
        schema:
          $ref: '#/definitions/FirmwareImageResponse'
      @include_indented /responses-api-spec-include/source/update-with-if-match-responses.cond.yaml

/regions/{region}/network/rules/firmware-mappings:
  post:
    operationId: CreateFirmwareMapping
    summary: Creates a new FirmwareMapping
    description: |
      Creates a new FirmwareMapping
    tags:
      - firmwareMapping
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkFirmwareMapping:
          actionKind: CREATE
          permissions: [ "NET_FIRMWARE_MAPPING_CREATE" ]
          serviceResourceName: NetworkFirmwareMapping
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkFirmwareMapping'].grantedPermissions.contains('NET_FIRMWARE_MAPPING_CREATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/RetryTokenHeader'
      - $ref: '#/parameters/RequestIdHeader'
      - name: CreateFirmwareMappingDetails
        description: Details for creating new firmware mapping.
        required: true
        in: body
        schema:
          $ref: '#/definitions/CreateFirmwareMappingDetails'
    x-related-resource: '#/definitions/FirmwareMappingResponse'
    responses:
      200:
        description: The firmware mapping has been created.
        schema:
          $ref: '#/definitions/FirmwareMappingResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/create-with-retry-token-responses.cond.yaml
  get:
    tags:
      - firmwareMapping
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkFirmwareMapping:
          actionKind: LIST
          permissions: [ "NET_FIRMWARE_MAPPING_READ" ]
          serviceResourceName: NetworkFirmwareMapping
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkFirmwareMapping'].grantedPermissions.contains('NET_FIRMWARE_MAPPING_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: GetFirmwareMappings
    summary: Retrieve all FirmwareMappings with optional filters
    description: Retrieve all FirmwareMappings. Use query parameters to filter results by device model, role, or platform
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - name: id
        in: query
        required: false
        type: string
        description: Filter by rmcid of the FirmwareMappings.
      - name: firmwareImageId
        in: query
        required: false
        type: string
        description: Filter by rmcid of the FirmwareMappings.
      - name: deviceModel
        in: query
        required: false
        type: string
        description: Filter by device model.
      - name: role
        in: query
        required: false
        type: string
        description: Filter by device role.
      - $ref: '#/parameters/RequestIdHeader'
    responses:
      200:
        description: A firmware mapping object.
        schema:
          $ref: '#/definitions/FirmwareMappingCollection'
        headers:
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

# NetInterfaceRule Entity Definitions
# =============================================================================
/regions/{region}/network/rules/interfaces/templates/{netInterfaceRuleId}:
  delete:
    operationId: DeleteNetInterfaceRule
    summary: Delete network interface rule by id
    description: |
      Delete network interface rule by id
    tags:
      - netInterfaceRule
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkExternalPeerInterface:
          actionKind: DELETE
          permissions: [ "NET_INTERFACE_TEMPLATE_DELETE" ]
          serviceResourceName: NetworkInterfaceTemplate
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkInterfaceTemplate'].grantedPermissions.contains('NET_INTERFACE_TEMPLATE_DELETE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/NetInterfaceRuleId'
      - $ref: '#/parameters/IfMatchHeader'
      - $ref: '#/parameters/RequestIdHeader'
    responses:
      204:
        description: The network interface rule has been deleted
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/update-with-if-match-responses.cond.yaml

/regions/{region}/network/rules/interfaces/templates/virtual:
  post:
    operationId: CreateVirtualNetInterfaceRule
    summary: Creates a new virtual network interface config rule
    description: Creates a new virtual network interface config rule
    tags:
      - netInterfaceRule
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkInterfaceTemplate:
          actionKind: CREATE
          permissions: [ "NET_INTERFACE_TEMPLATE_CREATE" ]
          serviceResourceName: NetworkInterfaceTemplate
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkInterfaceTemplate'].grantedPermissions.contains('NET_INTERFACE_TEMPLATE_CREATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/RetryTokenHeader'
      - $ref: '#/parameters/RequestIdHeader'
      - name: CreateVirtualNetInterfaceRuleDetails
        description: Details for creating a new network interface rule
        required: true
        in: body
        schema:
          $ref: '#/definitions/CreateVirtualNetInterfaceRuleDetails'
    x-related-resource: '#/definitions/NetInterfaceRuleResponse'
    responses:
      201:
        description: The network interface rule created
        schema:
          $ref: '#/definitions/NetInterfaceRuleResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/create-with-retry-token-responses.cond.yaml
  get:
    operationId: GetVirtualNetInterfaceRule
    summary: Get virtual network interface config rules with optional filters
    description: Get virtual network interface config rules. May use query parameters to retrieve rule by id or name.
    tags:
      - netInterfaceRule
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkInterfaceTemplate:
          actionKind: READ
          permissions: [ "NET_INTERFACE_TEMPLATE_READ" ]
          serviceResourceName: NetworkInterfaceTemplate
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkInterfaceTemplate'].grantedPermissions.contains('NET_INTERFACE_TEMPLATE_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - name: id
        in: query
        required: false
        type: string
        description: Retrieve single rule by id
      - name: name
        in: query
        required: false
        type: string
        description: Retrieve single rule by name
      - name: role
        in: query
        required: false
        type: string
        description: Retrieve list of rules by role
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/IfMatchHeader'
    responses:
      200:
        description: A Network Interface Rule
        schema:
          $ref: '#/definitions/NetInterfaceRuleCollection'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

/regions/{region}/network/rules/interfaces/templates/layer2:
  post:
    operationId: CreateLayer2NetInterfaceRule
    summary: Creates a new layer2 network interface config rule
    description: Creates a new layer2 network interface config rule
    tags:
      - netInterfaceRule
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkInterfaceTemplate:
          actionKind: CREATE
          permissions: [ "NET_INTERFACE_TEMPLATE_CREATE" ]
          serviceResourceName: NetworkInterfaceTemplate
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkInterfaceTemplate'].grantedPermissions.contains('NET_INTERFACE_TEMPLATE_CREATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/RetryTokenHeader'
      - $ref: '#/parameters/RequestIdHeader'
      - name: CreateLayer2NetInterfaceRuleDetails
        description: Details for creating a new network interface rule
        required: true
        in: body
        schema:
          $ref: '#/definitions/CreateLayer2NetInterfaceRuleDetails'
    x-related-resource: '#/definitions/NetInterfaceRuleResponse'
    responses:
      201:
        description: The network interface rule created
        schema:
          $ref: '#/definitions/NetInterfaceRuleResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/create-with-retry-token-responses.cond.yaml
  get:
    operationId: GetLayer2NetInterfaceRule
    summary: Get layer2 network interface config rules with optional filters
    description: Get layer2 network interface config rules. May use query parameters to retrieve rule by id or name.
    tags:
      - netInterfaceRule
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkInterfaceTemplate:
          actionKind: READ
          permissions: [ "NET_INTERFACE_TEMPLATE_READ" ]
          serviceResourceName: NetworkInterfaceTemplate
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkInterfaceTemplate'].grantedPermissions.contains('NET_INTERFACE_TEMPLATE_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - name: id
        in: query
        required: false
        type: string
        description: Retrieve single rule by id
      - name: name
        in: query
        required: false
        type: string
        description: Retrieve single rule by name
      - name: role
        in: query
        required: false
        type: string
        description: Retrieve list of rules by role
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/IfMatchHeader'
    responses:
      200:
        description: A Network Interface Rule
        schema:
          $ref: '#/definitions/NetInterfaceRuleCollection'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

/regions/{region}/network/rules/interfaces/templates/layer3:
  post:
    operationId: CreateLayer3NetInterfaceRule
    summary: Creates a new layer3 network interface config rule
    description: Creates a new layer3 network interface config rule
    tags:
      - netInterfaceRule
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkInterfaceTemplate:
          actionKind: CREATE
          permissions: [ "NET_INTERFACE_TEMPLATE_CREATE" ]
          serviceResourceName: NetworkInterfaceTemplate
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkInterfaceTemplate'].grantedPermissions.contains('NET_INTERFACE_TEMPLATE_CREATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/ForceApplyQueryParam'
      - $ref: '#/parameters/RetryTokenHeader'
      - $ref: '#/parameters/RequestIdHeader'
      - name: CreateLayer3NetInterfaceRuleDetails
        description: Details for creating a new network interface rule
        required: true
        in: body
        schema:
          $ref: '#/definitions/CreateLayer3NetInterfaceRuleDetails'
    x-related-resource: '#/definitions/NetInterfaceRuleResponse'
    responses:
      201:
        description: The network interface rule created
        schema:
          $ref: '#/definitions/NetInterfaceRuleResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/create-with-retry-token-responses.cond.yaml
  get:
    operationId: GetLayer3NetInterfaceRule
    summary: Get layer3 network interface config rules with optional filters
    description: Get layer3 network interface config rules. May use query parameters to retrieve rule by id or name.
    tags:
      - netInterfaceRule
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkInterfaceTemplate:
          actionKind: READ
          permissions: [ "NET_INTERFACE_TEMPLATE_READ" ]
          serviceResourceName: NetworkInterfaceTemplate
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkInterfaceTemplate'].grantedPermissions.contains('NET_INTERFACE_TEMPLATE_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - name: id
        in: query
        required: false
        type: string
        description: Retrieve single rule by id
      - name: name
        in: query
        required: false
        type: string
        description: Retrieve single rule by name
      - name: role
        in: query
        required: false
        type: string
        description: Retrieve list of rules by role
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/IfMatchHeader'
    responses:
      200:
        description: A Network Interface Rule
        schema:
          $ref: '#/definitions/NetInterfaceRuleCollection'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

/regions/{region}/network/model/interfaces/state:
  get:
    tags:
      - netInterface
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkInterfaceStateInternal:
          actionKind: READ
          permissions: [ "NET_INTERFACE_STATE_READ" ]
          serviceResourceName: NetworkInterfaceState
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkInterfaceStateInternal'].grantedPermissions.contains('NET_INTERFACE_STATE_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: ListAllNetInterfaceStates
    summary: Retrieve all network interface states
    description: Retrieve Network Interface states
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/isMonitoredQueryParam'
      - $ref: '#/parameters/RoleQueryParam'
      - $ref: '#/parameters/InterfaceCurrentStateValue'
      - $ref: '#/parameters/InterfaceIntendedStateValue'
      - $ref: '#/parameters/TimestampQueryParam'
      - $ref: '#/parameters/PaginationLimitQueryParam'
      - $ref: '#/parameters/PaginationTokenQueryParam'
      - $ref: '#/parameters/RequestIdHeader'
    responses:
      200:
        description: A page of interface states with all attributes.
        schema:
          $ref: '#/definitions/NetworkInterfaceStateResponseCollection'
        headers:
          opc-request-id: *ref-opc-request-id
          opc-next-page: *ref-opc-next-page
      @include_indented /responses-api-spec-include/source/list-responses.cond.yaml

# New endpoint for getting a specific interface state with all attribute
/regions/{region}/network/model/interfaces/{interface-Id}/state:
  get:
    tags:
      - netInterface
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkInterfaceStateInternal:
          actionKind: READ
          permissions: [ "NET_INTERFACE_STATE_READ" ]
          serviceResourceName: NetworkInterfaceState
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkInterfaceStateInternal'].grantedPermissions.contains('NET_INTERFACE_STATE_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: GetNetInterface
    summary: Get Network Interface State by interfaceId
    description: Get the state of a specific interface using its unique identifier.
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - name: interface-Id
        in: path
        required: true
        description: The unique identifier of the interface.
        type: string
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/IfMatchHeader'
    responses:
      204:
        description: Get interface state by interfaceId
        schema:
          $ref: '#/definitions/NetworkInterfaceResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

# New endpoint for getting a specific interface state attribute
/regions/{region}/network/model/interfaces/{interface-Id}/state/attributes/{attribute}:
  #  get:
  #    tags:
  #      - netInterface
  #      - nmc
  #    x-obmcs-splat:
  #      routing:
  #        strategy: route-to-any-ad
  #      serviceList: [ '${SERVICE_NAME}' ]
  #      authorization:
  #        mode: none
  #        allowCrossTenancy: true
  #      tagStore:
  #        mode: none
  #      audit:
  #        mode: none
  #      search:
  #        mode: none
  #      quotas:
  #        mode: none
  #      maximumAttemptCount: 3
  #    operationId: GetNetInterfaceAttribute
  #    summary: Retrieve a specific attribute of a interface's state using interfaceId
  #    description: Retrieve the value of a specified attribute for a given NetInterface identified by its interfaceId.
  #    parameters:
  #      - $ref: '#/parameters/RegionPathParam'
  #      - name: interface-Id
  #        in: path
  #        required: true
  #        description: The unique identifier of the interface.
  #        type: string
  #      - name: attribute
  #        in: path
  #        required: true
  #        description: The attribute to retrieve.
  #        type: string
  #      - $ref: '#/parameters/RequestIdHeader'
  #    responses:
  #      200:
  #        description: The value of the specified attribute.
  #        schema:
  #          type: string  # the attribute value
  #        headers:
  #          etag: *ref-etag
  #          opc-request-id: *ref-opc-request-id
  #      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml
  post:
    tags:
      - netInterface
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkInterfaceStateInternal:
          actionKind: CREATE
          permissions: [ "NET_INTERFACE_STATE_CREATE" ]
          serviceResourceName: NetworkInterfaceState
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkInterfaceStateInternal'].grantedPermissions.contains('NET_INTERFACE_STATE_CREATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: UpdateNetInterfaceAttribute
    summary: Update the state of an interface by InterfaceId
    description: Update the state of an interface by InterfaceId
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - name: interface-Id
        in: path
        required: true
        description: The unique identifier of the interface.
        type: string
      - name: attribute
        in: path
        required: true
        description: The attribute to retrieve.
        type: string
      - name: attributePair
        in: body
        description: the pair of <oldValue, newValue>
        required: true
        schema:
          $ref: '#/definitions/StateAttributePair'
      - $ref: '#/parameters/RequestIdHeader'
    responses:
      200:
        description: The value of the specified attribute.
        schema:
          $ref: '#/definitions/NetworkInterfaceResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/update-with-if-match-responses.cond.yaml

# Network Model Devices Definitions
# =============================================================================

/regions/{region}/network/model/devices:
  get:
    tags:
      - networkDevice
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkDevice:
          actionKind: LIST
          permissions: [ "NET_DEVICE_READ" ]
          serviceResourceName: NetworkDevice
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkDevice'].grantedPermissions.contains('NET_DEVICE_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: GetNetworkDevices
    summary: Retrieve all Devices with optional filters
    description: Retrieve all Network Devices. Use query parameters to filter results by role
    parameters:
      - $ref: '#/parameters/ResponseFields'
      - $ref: '#/parameters/isMonitoredQueryParam'
      - $ref: '#/parameters/IsWithStateQueryParam'
      - $ref: '#/parameters/RoleQueryParam'
      - $ref: '#/parameters/DeviceModelQueryParam'
      - $ref: '#/parameters/ConfigPlatformQueryParam'
      - $ref: '#/parameters/IpAddressQueryParam'
      - $ref: '#/parameters/TimestampQueryParam'
      - $ref: '#/parameters/PaginationLimitQueryParam'
      - $ref: '#/parameters/PaginationTokenQueryParam'
      - $ref: '#/parameters/SortOrderQueryParam'
      - $ref: '#/parameters/SortByQueryParam'
      - $ref: '#/parameters/IsIncludeSerialNumberQueryParam'
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/RequestIdHeader'
    responses:
      200:
        description: A page of devices with ports and interface info.
        schema:
          $ref: '#/definitions/NetworkDeviceResponseCollection'
        headers:
          opc-next-page: *ref-opc-next-page
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

/regions/{region}/network/model/devices/name/{deviceName}/ports:
  get:
    tags:
      - networkDevicePort
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkDevice:
          actionKind: READ
          permissions: [ "NET_DEVICE_READ" ]
          serviceResourceName: NetworkDevice
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkDevice'].grantedPermissions.contains('NET_DEVICE_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: GetNetworkDevicePortsByDeviceName
    summary: Retrieve all ports and peer-ports of a network device by name
    description: Retrieve all ports and peer-ports of a network device by name
    parameters:
      - $ref: '#/parameters/ResponseFields'
      - $ref: '#/parameters/DeviceNamePathParam'
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/RequestIdHeader'
    responses:
      200:
        description: A Network device with ports and peer ports information
        schema:
          $ref: '#/definitions/NetworkDevicePortAndPeerList'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

/regions/{region}/network/model/ports:
  get:
    tags:
      - networkDevicePort
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkDevice:
          actionKind: LIST
          permissions: [ "NET_DEVICE_READ" ]
          serviceResourceName: NetworkDevice
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkDevice'].grantedPermissions.contains('NET_DEVICE_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: GetAllNetworkDevicePorts
    summary: Retrieve all ports and peer-ports of a network model
    description: Retrieve all ports and peer-ports of a network model
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/PaginationLimitQueryParam'
      - $ref: '#/parameters/PaginationTokenQueryParam'
      - $ref: '#/parameters/SortOrderQueryParam'
      - $ref: '#/parameters/SortByQueryParam'
    responses:
      200:
        description: A Network device with ports and peer ports information
        schema:
          $ref: '#/definitions/NetworkDevicePortAndPeerCollection'
        headers:
          opc-next-page: *ref-opc-next-page
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

/regions/{region}/network/model/fabrics:
  get:
    tags:
      - networkFabric
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkFabric:
          actionKind: READ
          permissions: [ "NET_FABRIC_READ" ]
          serviceResourceName: NetworkFabric
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkFabric'].grantedPermissions.contains('NET_FABRIC_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: GetListOfFabrics
    summary: Get list of fabrics
    description: Get list of fabrics
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/RequestIdHeader'
    x-related-resource: '#/definitions/NetworkFabricResponseCollection'
    responses:
      200:
        description: A list of fabrics  object.
        schema:
          $ref: '#/definitions/NetworkFabricResponseCollection'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/list-responses.cond.yaml

/regions/{region}/network/model/fabrics/{type}:
  get:
    tags:
      - networkFabric
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkFabric:
          actionKind: READ
          permissions: [ "NET_FABRIC_READ" ]
          serviceResourceName: NetworkFabric
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkFabric'].grantedPermissions.contains('NET_FABRIC_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: GetNetworkFabricByType
    summary: Get  NetworkFabric by type
    description: Get NetworkFabric by type
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/NetworkFabricTypePathParam'
      - $ref: '#/parameters/RequestIdHeader'
    x-related-resource: '#/definitions/NetworkFabricResponseCollection'
    responses:
      200:
        description: A list of Networkfabrics.
        schema:
          $ref: '#/definitions/NetworkFabricResponseCollection'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/list-responses.cond.yaml

/regions/{region}/network/model/reset:
  delete:
    tags:
      - networkModelReset
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkModel:
          actionKind: DELETE
          permissions: [ "NET_MODEL_DELETE" ]
          serviceResourceName: NetworkModel
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkModel'].grantedPermissions.contains('NET_MODEL_DELETE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: ResetNetworkModel
    summary: Delete all entities inside the NMC tables
    description: Delete all entities inside the NMC tables
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/IfMatchHeader'
    responses:
      204:
        description: All entities deleted
        headers:
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/delete-responses.cond.yaml

# Deployment Entity Paths
# =============================================================================
/regions/{region}/network/deployments:
  post:
    operationId: CreateDeployment
    summary: Creates a new Deployment
    description: |
      Creates a new Deployment
    tags:
      - deployment
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkDeployment:
          actionKind: CREATE
          permissions: [ "NET_DEPLOYMENT_CREATE" ]
          serviceResourceName: NetworkDeployment
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkDeployment'].grantedPermissions.contains('NET_DEPLOYMENT_CREATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RetryTokenHeader'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/RegionPathParam'
      - name: CreateDeploymentDetails
        description: Details for creating new deployment.
        required: true
        in: body
        schema:
          $ref: '#/definitions/CreateDeploymentDetails'
    x-related-resource: '#/definitions/DeploymentResponse'
    responses:
      201:
        description: The deployment has been created.
        schema:
          $ref: '#/definitions/DeploymentResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/create-with-retry-token-responses.cond.yaml
  get:
    operationId: ListDeployments
    summary: Retrieves all deployments for the specified region
    description: |
      Returns a list of all deployments within the specified region.
    tags:
      - deployment
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkDeployment:
          actionKind: LIST
          permissions: [ "NET_DEPLOYMENT_READ" ]
          serviceResourceName: NetworkDeployment
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkDeployment'].grantedPermissions.contains('NET_DEPLOYMENT_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/PaginationLimitQueryParam'
      - $ref: '#/parameters/PaginationTokenQueryParam'
      - $ref: '#/parameters/SortOrderQueryParam'
      - $ref: '#/parameters/SortByQueryParam'
    x-related-resource: "#/definitions/DeploymentResponse"
    responses:
      200:
        description: A list of deployments in the specified region.
        schema:
          $ref: '#/definitions/DeploymentCollection'
        headers:
          opc-request-id: *ref-opc-request-id
          opc-next-page: *ref-opc-next-page
      @include_indented /responses-api-spec-include/source/list-responses.cond.yaml

/regions/{region}/network/deployments/{deploymentId}/start:
  post:
    tags:
      - deployment
      - nmc
    x-obmcs-terraform:
      actionType: START
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkDeployment:
          actionKind: UPDATE
          permissions: [ "NET_DEPLOYMENT_UPDATE" ]
          serviceResourceName: NetworkDeployment
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkDeployment'].grantedPermissions.contains('NET_DEPLOYMENT_UPDATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: StartDeployment
    summary: Start a paused deployment
    description: Start a paused deployment
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/DeploymentPathParam'
      - $ref: '#/parameters/RequestIdHeader'
    x-related-resource: '#/definitions/DeploymentResponse'
    responses:
      200:
        description: A Deployment object.
        schema:
          $ref: '#/definitions/DeploymentResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/update-with-if-match-responses.cond.yaml

/regions/{region}/network/deployments/{deploymentId}/pause:
  post:
    tags:
      - deployment
      - nmc
    x-obmcs-terraform:
      actionType: START
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkDeployment:
          actionKind: UPDATE
          permissions: [ "NET_DEPLOYMENT_UPDATE" ]
          serviceResourceName: NetworkDeployment
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkDeployment'].grantedPermissions.contains('NET_DEPLOYMENT_UPDATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: PauseDeployment
    summary: Pause a running deployment
    description: Pause a running deployment
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/DeploymentPathParam'
      - $ref: '#/parameters/RequestIdHeader'
    x-related-resource: '#/definitions/DeploymentResponse'
    responses:
      200:
        description: A Deployment object.
        schema:
          $ref: '#/definitions/DeploymentResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/update-with-if-match-responses.cond.yaml

/regions/{region}/network/deployments/{deploymentId}/terminate:
  post:
    tags:
      - deployment
      - nmc
    x-obmcs-terraform:
      actionType: START
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkDeployment:
          actionKind: UPDATE
          permissions: [ "NET_DEPLOYMENT_UPDATE" ]
          serviceResourceName: NetworkDeployment
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkDeployment'].grantedPermissions.contains('NET_DEPLOYMENT_UPDATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: TerminateDeployment
    summary: Terminate a deployment
    description: Terminate a deployment
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/DeploymentPathParam'
      - $ref: '#/parameters/RequestIdHeader'
    x-related-resource: '#/definitions/DeploymentResponse'
    responses:
      200:
        description: A Deployment object.
        schema:
          $ref: '#/definitions/DeploymentResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/update-with-if-match-responses.cond.yaml

/regions/{region}/network/deployments/{deploymentId}:
  get:
    tags:
      - deployment
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkDeployment:
          actionKind: READ
          permissions: [ "NET_DEPLOYMENT_READ" ]
          serviceResourceName: NetworkDeployment
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkDeployment'].grantedPermissions.contains('NET_DEPLOYMENT_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: GetDeployment
    summary: Get deployment by Id
    description: Get deployment by Id
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/DeploymentPathParam'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/IfMatchHeader'
    responses:
      200:
        description: A Deployment object.
        schema:
          $ref: '#/definitions/DeploymentResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

/regions/{region}/network/deployments/{deploymentId}/deploymentTasks:
  get:
    tags:
      - deployment
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkDeploymentTask:
          actionKind: READ
          permissions: [ "NET_DEPLOYMENT_TASK_READ" ]
          serviceResourceName: NetworkDeploymentTask
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkDeploymentTask'].grantedPermissions.contains('NET_DEPLOYMENT_TASK_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: ListDeploymentTasks
    summary: Get deployment tasks by deployment Id
    description: Get deployment tasks by deployment Id
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/DeploymentPathParam'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/PaginationLimitQueryParam'
      - $ref: '#/parameters/PaginationTokenQueryParam'
      - $ref: '#/parameters/SortOrderQueryParam'
      - $ref: '#/parameters/SortByQueryParam'
    x-related-resource: '#/definitions/DeploymentTaskSummary'
    responses:
      200:
        description: A page of deployment tasks.
        schema:
          $ref: '#/definitions/DeploymentTaskCollection'
        headers:
          opc-request-id: *ref-opc-request-id
          opc-next-page: *ref-opc-next-page
      @include_indented /responses-api-spec-include/source/list-responses.cond.yaml

# Deployment Task Definitions
# =============================================================================
/regions/{region}/network/deploymentTasks/{deploymentTaskId}/actions/execute:
  post:
    operationId: ExecuteDeploymentTask
    summary: Manually execute a deployment task
    description: |
      Manually execute a deployment task instead of using DeploymentTaskExecutor
    tags:
      - deploymentTask
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkDeploymentTask:
          actionKind: CREATE
          permissions: [ "NET_DEPLOYMENT_TASK_CREATE" ]
          serviceResourceName: NetworkDeploymentTask
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkDeploymentTask'].grantedPermissions.contains('NET_DEPLOYMENT_TASK_CREATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/DeploymentTaskPathParam'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/RetryTokenHeader'
      - $ref: '#/parameters/IfMatchHeader'
    responses:
      202:
        description: Accepted the request. The deployment task will be executed.
        headers:
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/create-with-retry-token-responses.cond.yaml

# Deployment firmwareUpgrade Definitions
# =============================================================================
/regions/{region}/network/devices/name/{deviceName}/firmwareUpgrade:
  post:
    operationId: CreateFirmwareUpgrade
    summary: Manually execute firmware upgrade deployment
    description: |
      Manually execute firmware upgrade deployment
    tags:
      - deployment
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkDeployment:
          actionKind: CREATE
          permissions: [ "NET_DEPLOYMENT_CREATE" ]
          serviceResourceName: NetworkDeployment
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkDeployment'].grantedPermissions.contains('NET_DEPLOYMENT_CREATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    x-related-resource: '#/definitions/FirmwareUpgradeResponse'
    parameters:
      - $ref: '#/parameters/DeviceNamePathParam'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/RetryTokenHeader'
      - $ref: '#/parameters/IfMatchHeader'
      - name: CreateFirmwareUpgradeDetails
        description: Details for creating new fabric manifest.
        required: true
        in: body
        schema:
          $ref: '#/definitions/CreateFirmwareUpgradeDetails'
    responses:
      200:
        description: The Firmware Upgrade deployment has been created.
        schema:
          $ref: '#/definitions/FirmwareUpgradeResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/create-with-retry-token-responses.cond.yaml


# Config Metadata API's
# =============================================================================
/regions/{region}/network/config/{configType}:
  get:
    tags:
      - config
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkConfigs:
          actionKind: LIST
          permissions: [ "NET_CONFIGS_READ" ]
          serviceResourceName: NetworkDeploymentTask
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkConfigs'].grantedPermissions.contains('NET_CONFIGS_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: ListConfigs
    summary: Get all latest configs metadata by config type.
    description: Get all latest configs metadata by config type.
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/ConfigTypePathParam'
      - $ref: '#/parameters/ConfigStatusQueryParam'
      - $ref: '#/parameters/DeviceNameQueryParam'
      - $ref: '#/parameters/RoleQueryParam'
      - $ref: '#/parameters/RequestIdHeader'
    responses:
      200:
        description: A List of latest Configs metadata.
        schema:
          $ref: '#/definitions/ConfigsMetadataResponseCollection'
        headers:
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

/regions/{region}/network/config/metadata/{configMetadataId}:
  get:
    operationId: GetNetworkConfigMetadata
    summary: Retrieves the config metadata by id
    description: Retrieves the config metadata by id
    tags:
      - config
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkConfigs:
          actionKind: READ
          permissions: [ "NET_CONFIGS_READ" ]
          serviceResourceName: NetworkConfigs
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkConfigs'].grantedPermissions.contains('NET_CONFIGS_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/ConfigMetadataIdPathParam'
      - $ref: '#/parameters/RequestIdHeader'
    x-related-resource: '#/definitions/ConfigsMetadataResponse'
    responses:
      200:
        description: The configs metadata.
        schema:
          $ref: '#/definitions/ConfigsMetadataResponse'
        headers:
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

/regions/{region}/network/config/{configsMetadataId}/actions/refreshPar:
  post:
    operationId: RefreshConfigsMetadataPar
    summary: Manually refresh a configs metadata PAR
    description: |
      Manually refresh a configs metadata PAR and update the URL
    tags:
      - config
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkConfigs:
          actionKind: UPDATE
          permissions: [ "NET_CONFIGS_UPDATE" ]
          serviceResourceName: NetworkConfigs
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkConfigs'].grantedPermissions.contains('NET_CONFIGS_UPDATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/ConfigsMetadataPathParam'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/RegionPathParam'
    x-related-resource: '#/definitions/ConfigsMetadataResponse'
    responses:
      200:
        description: Accepted the request. The PAR was updated.
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
        schema:
          $ref: '#/definitions/ConfigsMetadataResponse'
      @include_indented /responses-api-spec-include/source/update-with-if-match-responses.cond.yaml

/regions/{region}/network/manifests/default:
  post:
    operationId: CreateDefaultManifest
    summary: Creates a new Default Manifest
    description: |
      Creates a new Default Manifest
    tags:
      - defaultManifest
      - nmc
    x-obmcs-splat:
      apiType: Asynchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkDefaultManifest:
          actionKind: CREATE
          permissions: [ "NET_DEPLOYMENT_CREATE" ]
          serviceResourceName: NetworkDeployment
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkDefaultManifest'].grantedPermissions.contains('NET_DEPLOYMENT_CREATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/RetryTokenHeader'
      - $ref: '#/parameters/RequestIdHeader'
      - name: CreateDefaultManifestDetails
        description: Details for creating new default manifest.
        required: true
        in: body
        schema:
          $ref: '#/definitions/CreateDefaultManifestDetails'
    x-related-resource: '#/definitions/DefaultManifest'
    responses:
      200:
        description: The default manifest is created.
        schema:
          $ref: '#/definitions/DefaultManifest'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/create-with-retry-token-responses.cond.yaml


/regions/{region}/network/manifests/deployment:
  post:
    operationId: CreateDeploymentManifest
    summary: Creates a new Deployment Manifest
    description: |
      Creates a new Deployment Manifest
    tags:
      - deploymentManifest
      - nmc
    x-obmcs-splat:
      apiType: Asynchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkDeploymentManifest:
          actionKind: CREATE
          permissions: [ "NET_DEPLOYMENT_CREATE" ]
          serviceResourceName: NetworkDeployment
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkDeploymentManifest'].grantedPermissions.contains('NET_DEPLOYMENT_CREATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/RetryTokenHeader'
      - $ref: '#/parameters/RequestIdHeader'
      - name: CreateDeploymentManifestDetails
        description: Details for creating new deployment manifest.
        required: true
        in: body
        schema:
          $ref: '#/definitions/CreateDeploymentManifestDetails'
    x-related-resource: '#/definitions/DeploymentManifest'
    responses:
      201:
        description: The deployment manifest is being created.
        schema:
          $ref: '#/definitions/DeploymentManifest'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
          opc-work-request-id: *ref-opc-work-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/create-with-retry-token-responses.cond.yaml
  get:
    operationId: ListDeploymentManifests
    summary: List deployment manifest
    description: |
      List deployment manifests
    tags:
      - deploymentManifest
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkDeploymentManifest:
          actionKind: CREATE
          permissions: [ "NET_DEPLOYMENT_READ" ]
          serviceResourceName: NetworkDeployment
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkDeploymentManifest'].grantedPermissions.contains('NET_DEPLOYMENT_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/RetryTokenHeader'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/IsActiveQueryParam'
      - $ref: '#/parameters/PaginationLimitQueryParam'
      - $ref: '#/parameters/PaginationTokenQueryParam'
      - $ref: '#/parameters/SortOrderQueryParam'
      - $ref: '#/parameters/SortByQueryParam'
    x-related-resource: '#/definitions/DeploymentManifest'
    responses:
      200:
        description: The deployment manifest has been created.
        schema:
          $ref: '#/definitions/DeploymentManifestCollection'
        headers:
          opc-next-page: *ref-opc-next-page
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/create-with-retry-token-responses.cond.yaml

/regions/{region}/network/manifests/deployment/{deploymentManifestId}:
  get:
    tags:
      - deploymentManifest
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkDeploymentManifest:
          actionKind: READ
          permissions: [ "NET_DEPLOYMENT_READ" ]
          serviceResourceName: NetworkDeployment
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkDeploymentManifest'].grantedPermissions.contains('NET_DEPLOYMENT_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: GetDeploymentManifest
    summary: Get deployment manifest by Id
    description: Get deployment manifest by Id
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/DeploymentManifestPathParam'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/IfMatchHeader'
    responses:
      200:
        description: A DeploymentManifest object.
        schema:
          $ref: '#/definitions/DeploymentManifest'
        headers:
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

# Fabric Manifest Entity Paths
# =============================================================================
/regions/{region}/network/fabricManifests:
  post:
    operationId: CreateFabricManifest
    summary: Creates a new Fabric Manifest
    description: |
      Creates a new Fabric Manifest
    tags:
      - fabricManifest
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkFabricManifest:
          actionKind: CREATE
          permissions: [ "NET_FABRIC_MANIFEST_CREATE" ]
          serviceResourceName: NetworkFabricManifest
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkFabricManifest'].grantedPermissions.contains('NET_FABRIC_MANIFEST_CREATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/RetryTokenHeader'
      - $ref: '#/parameters/RequestIdHeader'
      - name: CreateFabricManifestDetails
        description: Details for creating new fabric manifest.
        required: true
        in: body
        schema:
          $ref: '#/definitions/CreateFabricManifestDetails'
    x-related-resource: '#/definitions/FabricManifestResponse'
    responses:
      200:
        description: The fabric manifest has been created.
        schema:
          $ref: '#/definitions/FabricManifestResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/create-with-retry-token-responses.cond.yaml

/regions/{region}/network/fabricManifests/{fabricManifestId}:
  get:
    tags:
      - fabricManifest
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkFabricManifest:
          actionKind: READ
          permissions: [ "NET_FABRIC_MANIFEST_READ" ]
          serviceResourceName: NetworkFabricManifest
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkFabricManifest'].grantedPermissions.contains('NET_FABRIC_MANIFEST_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: GetFabricManifest
    summary: Get fabric manifest by Id
    description: Get fabric manifest by Id
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/FabricManifestPathParam'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/IfMatchHeader'
    responses:
      200:
        description: A FabricManifest object.
        schema:
          $ref: '#/definitions/FabricManifestResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

/regions/{region}/network/fabricManifests/{fabricManifestId}/cancel:
  post:
    tags:
      - fabricManifest
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkFabricManifest:
          actionKind: CREATE
          permissions: [ "NET_FABRIC_MANIFEST_CREATE" ]
          serviceResourceName: NetworkFabricManifest
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkFabricManifest'].grantedPermissions.contains('NET_FABRIC_MANIFEST_CREATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: CancelFabricManifest
    summary: Cancel a fabric manifest by Id, terminating associated deployments
    description: Cancel a fabric manifest by Id, terminating associated deployments
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/FabricManifestPathParam'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/IfMatchHeader'
    responses:
      200:
        description: A FabricManifest object.
        schema:
          $ref: '#/definitions/FabricManifestResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

# =============================================================================
# Devices API's
# =============================================================================
/regions/{region}/network/model/devices/ztp/serial/{serialNumber}:
  get:
    tags:
      - networkDevice
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkDevice:
          actionKind: READ
          permissions: [ "NET_DEVICE_ZTP_READ" ]
          serviceResourceName: NetworkDevice
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkDevice'].grantedPermissions.contains('NET_DEVICE_ZTP_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 1
    operationId: GetSlimDeviceBySerialAndVendor
    summary: Get device by serialId and vendor name.
    description: Get device by serialId and vendor name.
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/DevicesSerialNumberPathParam'
      - $ref: '#/parameters/DevicesVendorQueryParam'
      - $ref: '#/parameters/SkipNvswitchStateUpdateQueryParam'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/IfMatchHeader'
    x-related-resource: '#/definitions/NetworkDeviceSlimResponse'
    responses:
      200:
        description: A Device Instance matching serial id and vendor name.
        schema:
          $ref: '#/definitions/NetworkDeviceSlimResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

/regions/{region}/network/model/devices/ztp/name/{deviceName}:
  get:
    tags:
      - networkDevice
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkDevice:
          actionKind: READ
          permissions: [ "NET_DEVICE_ZTP_READ" ]
          serviceResourceName: NetworkDevice
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkDevice'].grantedPermissions.contains('NET_DEVICE_ZTP_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 1
    operationId: GetSlimDeviceByDeviceName
    summary: Get device by device name or by serialId and vendor name.
    description: Get device by device name or by serialId and vendor name.
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/DeviceNamePathParam'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/IfMatchHeader'
    x-related-resource: '#/definitions/NetworkDeviceSlimResponse'
    responses:
      200:
        description: A Device Instance matching device name.
        schema:
          $ref: '#/definitions/NetworkDeviceSlimResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml


# New endpoint for getting a device by dcmsId
/regions/{region}/network/model/devices/id/{dcmsId}:
  get:
    tags:
      - networkDevice
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkDevice:
          actionKind: READ
          permissions: [ "NET_DEVICE_READ" ]
          serviceResourceName: NetworkDevice
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkDevice'].grantedPermissions.contains('NET_DEVICE_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: GetDeviceByDcmsId
    summary: Retrieve device by DcmsId
    description: Retrieve device by DcmsId
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/DeviceDcmsIdPathParam'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/HasChildrenQueryParam'
      - $ref: '#/parameters/IsIncludeSerialNumberQueryParam'
      - $ref: '#/parameters/IfMatchHeader'
    x-related-resource: '#/definitions/NetworkDeviceResponse'
    responses:
      200:
        description: Retrieve device by dcmsId.
        schema:
          $ref: '#/definitions/NetworkDeviceResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

# New endpoint for getting a device by dcmsId
/regions/{region}/network/model/devices/name/{deviceName}:
  get:
    tags:
      - networkDevice
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkDevice:
          actionKind: READ
          permissions: [ "NET_DEVICE_READ" ]
          serviceResourceName: NetworkDevice
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkDevice'].grantedPermissions.contains('NET_DEVICE_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: GetNetworkDeviceByName
    summary: Retrieve device by device name
    description: Retrieve device by device name
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/DeviceNamePathParam'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/HasChildrenQueryParam'
      - $ref: '#/parameters/IsIncludeSerialNumberQueryParam'
      - $ref: '#/parameters/IfMatchHeader'
    x-related-resource: '#/definitions/NetworkDeviceResponse'
    responses:
      200:
        description: Retrieve device by device name.
        schema:
          $ref: '#/definitions/NetworkDeviceResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

# New endpoint for getting a device by dcmsId
/regions/{region}/network/model/devices/serial/{serialNumber}:
  get:
    tags:
      - networkDevice
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkDevice:
          actionKind: READ
          permissions: [ "NET_DEVICE_READ" ]
          serviceResourceName: NetworkDevice
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkDevice'].grantedPermissions.contains('NET_DEVICE_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: GetNetworkDeviceBySerialAndVendor
    summary: Retrieve device by serial number and vendor
    description: Retrieve device by serial number and vendor
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/DevicesSerialNumberPathParam'
      - $ref: '#/parameters/DevicesVendorQueryParam'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/HasChildrenQueryParam'
      - $ref: '#/parameters/IsIncludeSerialNumberQueryParam'
      - $ref: '#/parameters/IfMatchHeader'
    x-related-resource: '#/definitions/NetworkDeviceResponse'
    responses:
      200:
        description: Retrieve device by serial number and vendor.
        schema:
          $ref: '#/definitions/NetworkDeviceResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml


# =============================================================================
# Network Device State Entity
# =============================================================================
/regions/{region}/network/model/devices/state:
  get:
    tags:
      - networkDeviceState
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkDeviceState:
          actionKind: READ
          permissions: [ "NET_DEVICE_STATE_READ" ]
          serviceResourceName: NetworkDeviceState
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkDeviceState'].grantedPermissions.contains('NET_DEVICE_STATE_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: ListAllDeviceStates
    summary: Retrieve all network device states
    description: Retrieve Network device states
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/isMonitoredQueryParam'
      - $ref: '#/parameters/RoleQueryParam'
      - $ref: '#/parameters/DeviceCurrentStateValue'
      - $ref: '#/parameters/DeviceIntendedStateValue'
      - $ref: '#/parameters/AutomationStateValue'
      - $ref: '#/parameters/TimestampQueryParam'
      - $ref: '#/parameters/PaginationLimitQueryParam'
      - $ref: '#/parameters/PaginationTokenQueryParam'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/WithProcessDetailsQueryParam'
    responses:
      200:
        description: A page of device states with all attributes.
        schema:
          $ref: '#/definitions/NetworkDeviceStateResponseCollection'
        headers:
          opc-request-id: *ref-opc-request-id
          opc-next-page: *ref-opc-next-page
      @include_indented /responses-api-spec-include/source/list-responses.cond.yaml

# New endpoint for getting a specific interface state with all attribute
/regions/{region}/network/model/devices/{device-name}/state:
  get:
    tags:
      - networkDeviceState
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkDeviceState:
          actionKind: READ
          permissions: [ "NET_DEVICE_STATE_READ" ]
          serviceResourceName: NetworkDeviceState
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkDeviceState'].grantedPermissions.contains('NET_DEVICE_STATE_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: GetDeviceState
    summary: Get Network Device State by device-name
    description: Get the state of a specific device state using device-name.
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/DeviceStatePathParam'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/IfMatchHeader'
    responses:
      204:
        description: Get device state by device-name
        schema:
          $ref: '#/definitions/NetworkDeviceStateResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

# New endpoint for getting a specific device state attribute
/regions/{region}/network/model/devices/{device-name}/state/attributes/{attribute}:
  get:
    tags:
      - networkDeviceState
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkDeviceStateAttribute:
          actionKind: READ
          permissions: [ "NET_DEVICE_STATE_ATTRIBUTE_READ" ]
          serviceResourceName: NetworkDeviceStateAttribute
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkDeviceStateAttribute'].grantedPermissions.contains('NET_DEVICE_STATE_ATTRIBUTE_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: GetDeviceStateAttribute
    summary: Retrieve a specific attribute of a device state using device-name
    description: Retrieve the value of a specified attribute for a given device state identified by its device-name.
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/DeviceStatePathParam'
      - $ref: '#/parameters/StateAttributePathParam'
      - $ref: '#/parameters/RequestIdHeader'
    responses:
      200:
        description: The value of the specified attribute.
        schema:
          type: string  # the attribute value
        headers:
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml
  put:
    tags:
      - networkDeviceState
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkDeviceStateAttribute:
          actionKind: UPDATE
          permissions: [ "NET_DEVICE_STATE_ATTRIBUTE_UPDATE" ]
          serviceResourceName: NetworkDeviceStateAttribute
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkDeviceStateAttribute'].grantedPermissions.contains('NET_DEVICE_STATE_ATTRIBUTE_UPDATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: UpdateDeviceStateAttribute
    summary: Update a specific attribute of a device state using device-name
    description: Update a specific attribute of a device state using device-name
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/DeviceStatePathParam'
      - $ref: '#/parameters/StateAttributePathParam'
      - $ref: '#/parameters/IfMatchHeader'
      - name: attributeDetails
        description: Details for update state attribute
        required: true
        in: body
        schema:
          $ref: '#/definitions/StateAttributePair'
      - $ref: '#/parameters/RequestIdHeader'
    responses:
      200:
        description: Updated device state by device-name
        schema:
          $ref: '#/definitions/NetworkDeviceStateResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

/regions/{region}/network/model/devices/id/{dcmsId}/validationStatus:
  get:
    operationId: GetValidationStatus
    summary: Get the validation status for a specific dcmsDeviceModelId
    description: Validation status for the device
    tags:
      - networkDevice
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkDevice:
          actionKind: READ
          permissions: [ "NET_DEVICE_READ" ]
          serviceResourceName: NetworkDevice
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkDevice'].grantedPermissions.contains('NET_DEVICE_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/DeviceDcmsIdPathParam'
      - $ref: '#/parameters/DeviceValidationTypeQueryParam'
    x-related-resource: '#/definitions/NetworkDeviceValidationStatus'
    responses:
      200:
        description: Validation status
        schema:
          $ref: '#/definitions/NetworkDeviceValidationStatus'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

# new api to get firmware data with device details
/regions/{region}/network/model/devices/firmware:
  get:
    tags:
      - firmwareImage
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkDevice:
          actionKind: LIST
          permissions: [ "NET_DEVICE_READ" ]
          serviceResourceName: NetworkDevice
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkDevice'].grantedPermissions.contains('NET_DEVICE_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: GetAllDevicesWithFirmwareMapping
    summary: Get all devices with the firmware mapping details.
    description: Get all devices with the firmware mapping details.
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/NetworkDevicePaginationLimitQueryParam'
      - $ref: '#/parameters/RoleQueryParam'
      - $ref: '#/parameters/DeviceModelQueryParam'
      - $ref: '#/parameters/PaginationTokenQueryParam'
      - $ref: '#/parameters/SortOrderQueryParam'
      - $ref: '#/parameters/SortByQueryParam'
    x-related-resource: '#/definitions/NetworkDeviceFirmwareResponseCollection'
    responses:
      200:
        description: A page of network devices with firmware details
        schema:
          $ref: '#/definitions/NetworkDeviceFirmwareResponseCollection'
        headers:
          opc-next-page: *ref-opc-next-page
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

# Network Devices Location Definitions
# =============================================================================
/regions/{region}/network/model/devicesInRack:
  get:
    tags:
      - networkDeviceLocation
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkDevice:
          actionKind: LIST
          permissions: [ "NET_DEVICE_READ" ]
          serviceResourceName: NetworkDevice
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkDevice'].grantedPermissions.contains('NET_DEVICE_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: GetAllDevicesInRack
    summary: Retrieve all devices by rack number at particular site with optional filters on rack position and role
    description: Retrieve all devices by rack number at particular site. Use query parameters to filter results by role and position
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/SiteQueryParam'
      - $ref: '#/parameters/RackNumberQueryParam'
      - $ref: '#/parameters/RoleQueryParam'
      - $ref: '#/parameters/ElevationQueryParam'
      - $ref: '#/parameters/IsIncludeSerialNumberQueryParam'
      - $ref: '#/parameters/RequestIdHeader'
    responses:
      200:
        description: A list of network devices with ports and interfaces.
        schema:
          $ref: '#/definitions/NetworkDeviceResponseCollection'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

# Endpoint for updating the status (up/down) of host facing link.
/regions/{region}/network/model/devices/{deviceName}/interfaces/{interfaceName}/state/{stateValue}/host:
  put:
    operationId: SetInterfaceHostState
    summary: Set interface host state
    description: |
      Set Interface State
    tags:
      - netInterface
      - nmc
    x-obmcs-terraform:
      actionType: START
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkInterfaceStateInternal:
          actionKind: UPDATE
          permissions: [ "NET_INTERFACE_STATE_UPDATE" ]
          serviceResourceName: NetworkInterfaceState
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
        networkInterfaceStateExternal:
          actionKind: UPDATE
          permissions: [ "NET_INTERFACE_STATE_UPDATE" ]
          serviceResourceName: NetworkInterfaceState
          targetCompartmentId: "'${EXTERNAL_TARGET_COMPARTMENT_ID}'"
      restriction:
        allowedTenancies: ${ALLOWED_TENANCIES_FOR_EXTERNAL_API}
        allowedPublicCidrBlocks: ${ALLOWED_PUBLIC_CIDRS_FOR_EXTERNAL_API}
      authorization:
        mode: automated
        check: resources['networkInterfaceStateInternal'].grantedPermissions.contains('NET_INTERFACE_STATE_UPDATE') || resources['networkInterfaceStateExternal'].grantedPermissions.contains('NET_INTERFACE_STATE_UPDATE')
        allowCrossTenancy: true
        sameTenancyResources: [ ]
        crossTenancyAssociations: [ ]
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    x-related-resource: '#/definitions/CustomerFacingInterfaceResponse'
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/DeviceNamePathParam'
      - $ref: '#/parameters/InterfaceNamePathParam'
      - $ref: '#/parameters/InterfaceHostStateValue'
      - $ref: '#/parameters/ForceUnsafeParam'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/RetryTokenHeader'
      - name: if-match
        in: header
        description: For optimistic concurrency control. Set this to the value of the etag from a previous GET or POST response for the resource.
        required: false
        type: string
    responses:
      200:
        description: The link value of the Interface has been set.
        schema:
          $ref: '#/definitions/CustomerFacingInterfaceResponse'
        headers:
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/create-with-retry-token-responses.cond.yaml

# Endpoint for updating the status of interior link.
/regions/{region}/network/model/devices/{deviceName}/interfaces/{interfaceName}/state/{stateValue}/interior:
  put:
    operationId: SetInterfaceInteriorState
    summary: Set interface interior state
    description: |
      Set Interface State
    tags:
      - netInterface
      - nmc
    x-obmcs-terraform:
      actionType: START
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkInterfaceStateInternal:
          actionKind: UPDATE
          permissions: [ "NET_INTERFACE_STATE_UPDATE" ]
          serviceResourceName: NetworkInterfaceState
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
        networkInterfaceStateExternal:
          actionKind: UPDATE
          permissions: [ "NET_INTERFACE_STATE_UPDATE" ]
          serviceResourceName: NetworkInterfaceState
          targetCompartmentId: "'${EXTERNAL_TARGET_COMPARTMENT_ID}'"
      restriction:
        allowedTenancies: ${ALLOWED_TENANCIES_FOR_EXTERNAL_API}
        allowedPublicCidrBlocks: ${ALLOWED_PUBLIC_CIDRS_FOR_EXTERNAL_API}
      authorization:
        mode: automated
        check: resources['networkInterfaceStateInternal'].grantedPermissions.contains('NET_INTERFACE_STATE_UPDATE') || resources['networkInterfaceStateExternal'].grantedPermissions.contains('NET_INTERFACE_STATE_UPDATE')
        allowCrossTenancy: true
        sameTenancyResources: [ ]
        crossTenancyAssociations: [ ]
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    x-related-resource: '#/definitions/CustomerFacingInterfaceResponse'
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/DeviceNamePathParam'
      - $ref: '#/parameters/InterfaceNamePathParam'
      - $ref: '#/parameters/InterfaceInteriorStateValue'
      - $ref: '#/parameters/ForceUnsafeParam'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/RetryTokenHeader'
      - name: if-match
        in: header
        description: For optimistic concurrency control. Set this to the value of the etag from a previous GET or POST response for the resource.
        required: false
        type: string
    responses:
      200:
        description: The link value of the Interface has been set.
        schema:
          $ref: '#/definitions/CustomerFacingInterfaceResponse'
        headers:
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/create-with-retry-token-responses.cond.yaml

/regions/{region}/network/model/devices/{deviceName}/interfaces/{interfaceName}/state:
  get:
    tags:
      - netInterface
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkInterfaceStateInternal:
          actionKind: READ
          permissions: [ "NET_INTERFACE_STATE_READ" ]
          serviceResourceName: NetworkInterfaceState
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
        networkInterfaceStateExternal:
          actionKind: READ
          permissions: [ "NET_INTERFACE_STATE_READ" ]
          serviceResourceName: NetworkInterfaceState
          targetCompartmentId: "'${EXTERNAL_TARGET_COMPARTMENT_ID}'"
      restriction:
        allowedTenancies: ${ALLOWED_TENANCIES_FOR_EXTERNAL_API}
        allowedPublicCidrBlocks: ${ALLOWED_PUBLIC_CIDRS_FOR_EXTERNAL_API}
      authorization:
        mode: automated
        check: resources['networkInterfaceStateInternal'].grantedPermissions.contains('NET_INTERFACE_STATE_READ') || resources['networkInterfaceStateExternal'].grantedPermissions.contains('NET_INTERFACE_STATE_READ')
        allowCrossTenancy: true
        sameTenancyResources: [ ]
        crossTenancyAssociations: [ ]
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: GetInterfaceState
    summary: Get Interface State
    description: Get Interface State
    x-related-resource: '#/definitions/NetworkInterfaceResponse'
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/DeviceNamePathParam'
      - $ref: '#/parameters/InterfaceNamePathParam'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/IfMatchHeader'
    responses:
      200:
        description: Get device state by device-name
        schema:
          $ref: '#/definitions/CustomerFacingInterfaceResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

# New endpoint for creating the profile with the given parameters
/regions/{region}/network/config/rules/cos:
  post:
    tags:
      - cosProfile
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkConfigCosInternal:
          actionKind: CREATE
          permissions: [ "NET_CONFIG_COS_CREATE" ]
          serviceResourceName: NetworkConfigCos
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
        networkConfigCosExternal:
          actionKind: CREATE
          permissions: [ "NET_CONFIG_COS_CREATE" ]
          serviceResourceName: NetworkConfigCos
          targetCompartmentId: "'${EXTERNAL_TARGET_COMPARTMENT_ID}'"
      restriction:
        allowedTenancies: ${ALLOWED_TENANCIES_FOR_EXTERNAL_API}
        allowedPublicCidrBlocks: ${ALLOWED_PUBLIC_CIDRS_FOR_EXTERNAL_API}
      authorization:
        mode: automated
        check: resources['networkConfigCosInternal'].grantedPermissions.contains('NET_CONFIG_COS_CREATE') || resources['networkConfigCosExternal'].grantedPermissions.contains('NET_CONFIG_COS_CREATE')
        allowCrossTenancy: true
        sameTenancyResources: [ ]
        crossTenancyAssociations: [ ]
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: CreateCosProfile
    summary: Create a specific Cos Profile
    description: Create a specific Cos Profile
    x-related-resource: '#/definitions/CreateCosProfileDetails'
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - name: CreateCosProfileDetails
        description: Details for creating cos profile
        required: true
        in: body
        schema:
          $ref: '#/definitions/CreateCosProfileDetails'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/RetryTokenHeader'
    responses:
      200:
        description: Created device state by device-name
        schema:
          $ref: '#/definitions/CreateCosProfileDetails'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/create-with-retry-token-responses.cond.yaml

/regions/{region}/network/model/devices/{deviceName}/interfaces/cos/{profileName}:
  put:
    operationId: UpdateInterfaceCosProfile
    summary: Associates a profile with an interface on a device
    description: |
      Update Cos Profile
    tags:
      - cosProfile
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkConfigCosInternal:
          actionKind: UPDATE
          permissions: [ "NET_CONFIG_COS_UPDATE" ]
          serviceResourceName: NetworkConfigCos
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
        networkConfigCosExternal:
          actionKind: UPDATE
          permissions: [ "NET_CONFIG_COS_UPDATE" ]
          serviceResourceName: NetworkConfigCos
          targetCompartmentId: "'${EXTERNAL_TARGET_COMPARTMENT_ID}'"
      restriction:
        allowedTenancies: ${ALLOWED_TENANCIES_FOR_EXTERNAL_API}
        allowedPublicCidrBlocks: ${ALLOWED_PUBLIC_CIDRS_FOR_EXTERNAL_API}
      authorization:
        mode: automated
        check: resources['networkConfigCosInternal'].grantedPermissions.contains('NET_CONFIG_COS_UPDATE') || resources['networkConfigCosExternal'].grantedPermissions.contains('NET_CONFIG_COS_UPDATE')
        allowCrossTenancy: true
        sameTenancyResources: [ ]
        crossTenancyAssociations: [ ]
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    x-related-resource: '#/definitions/InterfaceCosDetails'
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/DeviceNamePathParam'
      - name: UpdateInterfaceCosProfileDetails
        description: Interface names to attach the cos profile to
        required: true
        in: body
        schema:
          $ref: '#/definitions/UpdateInterfaceCosProfileDetails'
      - $ref: '#/parameters/ProfileNamePathParam'
      - $ref: '#/parameters/RemoveCosProfileQueryParam'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/IfMatchHeader'
    responses:
      200:
        description: Updated Cos Profile
        schema:
          $ref: '#/definitions/InterfaceCosDetails'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/update-with-if-match-responses.cond.yaml
  get:
    operationId: GetInterfaceCosProfile
    summary: Gets the interfaces on a device that are associated with the given cos profile
    description: |
      Get Interfaces Cos Profile
    tags:
      - cosProfile
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkConfigCosInternal:
          actionKind: UPDATE
          permissions: [ "NET_CONFIG_COS_UPDATE" ]
          serviceResourceName: NetworkConfigCos
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
        networkConfigCosExternal:
          actionKind: UPDATE
          permissions: [ "NET_CONFIG_COS_UPDATE" ]
          serviceResourceName: NetworkConfigCos
          targetCompartmentId: "'${EXTERNAL_TARGET_COMPARTMENT_ID}'"
      restriction:
        allowedTenancies: ${ALLOWED_TENANCIES_FOR_EXTERNAL_API}
        allowedPublicCidrBlocks: ${ALLOWED_PUBLIC_CIDRS_FOR_EXTERNAL_API}
      authorization:
        mode: automated
        check: resources['networkConfigCosInternal'].grantedPermissions.contains('NET_CONFIG_COS_UPDATE') || resources['networkConfigCosExternal'].grantedPermissions.contains('NET_CONFIG_COS_UPDATE')
        allowCrossTenancy: true
        sameTenancyResources: [ ]
        crossTenancyAssociations: [ ]
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    x-related-resource: '#/definitions/InterfaceCosDetails'
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/DeviceNamePathParam'
      - $ref: '#/parameters/ProfileNamePathParam'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/IfMatchHeader'
    responses:
      200:
        description: Get Cos Profile for interfaces on a device
        schema:
          $ref: '#/definitions/InterfaceCosDetails'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/update-with-if-match-responses.cond.yaml

/regions/{region}/network/model/devices/{deviceName}/cos/{profileName}:
  put:
    operationId: UpdateCosProfile
    summary: Associates a profile with a device
    description: |
      Update Cos Profile
    tags:
      - cosProfile
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkConfigCosInternal:
          actionKind: UPDATE
          permissions: [ "NET_CONFIG_COS_UPDATE" ]
          serviceResourceName: NetworkConfigCos
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
        networkConfigCosExternal:
          actionKind: UPDATE
          permissions: [ "NET_CONFIG_COS_UPDATE" ]
          serviceResourceName: NetworkConfigCos
          targetCompartmentId: "'${EXTERNAL_TARGET_COMPARTMENT_ID}'"
      restriction:
        allowedTenancies: ${ALLOWED_TENANCIES_FOR_EXTERNAL_API}
        allowedPublicCidrBlocks: ${ALLOWED_PUBLIC_CIDRS_FOR_EXTERNAL_API}
      authorization:
        mode: automated
        check: resources['networkConfigCosInternal'].grantedPermissions.contains('NET_CONFIG_COS_UPDATE') || resources['networkConfigCosExternal'].grantedPermissions.contains('NET_CONFIG_COS_UPDATE')
        allowCrossTenancy: true
        sameTenancyResources: [ ]
        crossTenancyAssociations: [ ]
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    x-related-resource: '#/definitions/NetworkModelDeviceCosDetails'
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/DeviceNamePathParam'
      - $ref: '#/parameters/ProfileNamePathParam'
      - $ref: '#/parameters/ForceUnsafeParam'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/IfMatchHeader'
    responses:
      200:
        description: Updated Cos Profile
        schema:
          $ref: '#/definitions/NetworkModelDeviceCosDetails'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/update-with-if-match-responses.cond.yaml
  get:
    tags:
      - cosProfile
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkConfigCosInternal:
          actionKind: READ
          permissions: [ "NET_CONFIG_COS_READ" ]
          serviceResourceName: NetworkConfigCos
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
        networkConfigCosExternal:
          actionKind: READ
          permissions: [ "NET_CONFIG_COS_READ" ]
          serviceResourceName: NetworkConfigCos
          targetCompartmentId: "'${EXTERNAL_TARGET_COMPARTMENT_ID}'"
      restriction:
        allowedTenancies: ${ALLOWED_TENANCIES_FOR_EXTERNAL_API}
        allowedPublicCidrBlocks: ${ALLOWED_PUBLIC_CIDRS_FOR_EXTERNAL_API}
      authorization:
        mode: automated
        check: resources['networkConfigCosInternal'].grantedPermissions.contains('NET_CONFIG_COS_READ') || resources['networkConfigCosExternal'].grantedPermissions.contains('NET_CONFIG_COS_READ')
        allowCrossTenancy: true
        sameTenancyResources: [ ]
        crossTenancyAssociations: [ ]
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: GetCosProfile
    summary: Get Cos Profile
    description: Get Cos Profile
    x-related-resource: '#/definitions/NetworkModelDeviceCosDetails'
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/DeviceNamePathParam'
      - $ref: '#/parameters/ProfileNamePathParam'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/IfMatchHeader'
    responses:
      200:
        description: Get Cos Profile
        schema:
          $ref: '#/definitions/NetworkModelDeviceCosDetails'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml


# Endpoints for external peering
/regions/{region}/network/rules/external-peerings/{peerName}/interfaces:
  post:
    operationId: CreateExternalPeerInterface
    summary: create interface for external peering
    description: create external peering interfaces and create routing contexts
    tags:
      - externalPeering
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkExternalPeerInterface:
          actionKind: CREATE
          permissions: [ "NET_EXTERNAL_PEER_INTERFACE_CREATE" ]
          serviceResourceName: NetworkExternalPeerInterface
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkExternalPeerInterface'].grantedPermissions.contains('NET_EXTERNAL_PEER_INTERFACE_CREATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/ExternalPeerPathParam'
      - $ref: '#/parameters/RetryTokenHeader'
      - $ref: '#/parameters/RequestIdHeader'
      - name: CreateExternalPeerInterfaceDetails
        description: Details for creating and configuring a external peering interfaces and routing contexts
        required: true
        in: body
        schema:
          $ref: '#/definitions/CreateExternalPeerInterfaceDetails'
    x-related-resource: '#/definitions/NetworkInterfaceResponse'
    responses:
      201:
        description: The network interface created on external peer
        schema:
          $ref: '#/definitions/NetworkInterfaceResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/create-with-retry-token-responses.cond.yaml
  put:
    operationId: UpdateExternalPeeringInterface
    summary: Update interfaces in external peering
    description: |
      Update external peering interfaces
    tags:
      - externalPeering
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkExternalPeerInterface:
          actionKind: UPDATE
          permissions: [ "NET_EXTERNAL_PEER_INTERFACE_UPDATE" ]
          serviceResourceName: NetworkExternalPeerInterface
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkExternalPeerInterface'].grantedPermissions.contains('NET_EXTERNAL_PEER_INTERFACE_UPDATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/ExternalPeerPathParam'
      - $ref: '#/parameters/RetryTokenHeader'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/IfMatchHeader'
      - name: UpdateExternalPeerInterfaceDetails
        description: Details for updating external peering interfaces
        required: true
        in: body
        schema:
          $ref: '#/definitions/UpdateExternalPeerInterfaceDetails'
    x-related-resource: '#/definitions/NetworkInterfaceResponse'
    responses:
      200:
        description: Accepted the request. updated network interface
        headers:
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
        schema:
          $ref: '#/definitions/NetworkInterfaceResponse'
      409:
        description: Invalidated Retry Token
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id:
            description: Unique identifier for the request
            type: string
      @include_indented /responses-api-spec-include/source/update-with-if-match-responses.cond.yaml
  delete:
    operationId: DeleteExternalPeeringInterface
    summary: Delete interfaces in external peering
    description: |
      Delete external peering interfaces
    tags:
      - externalPeering
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkExternalPeerInterface:
          actionKind: DELETE
          permissions: [ "NET_EXTERNAL_PEER_INTERFACE_DELETE" ]
          serviceResourceName: NetworkExternalPeerInterface
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkExternalPeerInterface'].grantedPermissions.contains('NET_EXTERNAL_PEER_INTERFACE_DELETE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/ExternalPeerPathParam'
      - $ref: '#/parameters/IfMatchHeader'
      - $ref: '#/parameters/RequestIdHeader'
      - name: sourceDeviceName
        description: The source device to be linked to external peer
        type: string
        required: true
        in: query
      - name: sourceInterfaceName
        description: The name of the interface on the source device that is part of the peering relationship
        type: string
        required: true
        in: query
    x-related-resource: '#/definitions/NetworkInterfaceResponseCollection'
    responses:
      204:
        description: All peering interfaces were deleted
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/update-with-if-match-responses.cond.yaml

# Endpoints for Ingestion
/regions/{region}/network/model/ingestion/complete:
  post:
    operationId: CreateNetworkModelIngestionComplete
    summary: creates an ingestion-complete signal for the logical connection ingestion into the network model
    description: creates an ingestion-complete signal for the logical connection ingestion into the network model
    tags:
      - networkModelIngestion
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkModelIngestion:
          actionKind: CREATE
          permissions: [ "NET_MODEL_INGESTION_COMPLETE_CREATE" ]
          serviceResourceName: NetworkModelIngestion
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkModelIngestion'].grantedPermissions.contains('NET_MODEL_INGESTION_COMPLETE_CREATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/RetryTokenHeader'
      - $ref: '#/parameters/RequestIdHeader'
      - name: CreateNetworkModelIngestionCompleteDetails
        description: Details for creating and configuring a external peering interfaces and routing contexts
        required: true
        in: body
        schema:
          $ref: '#/definitions/CreateNetworkModelIngestionCompleteDetails'
    x-related-resource: '#/definitions/CreateNetworkModelIngestionCompleteDetails'
    responses:
      200:
        description: The response containing details of the post-ingestion processing
        schema:
          $ref: '#/definitions/CreateNetworkModelIngestionCompleteDetails'
        headers:
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/create-with-retry-token-responses.cond.yaml

# =============================================================================
# Net_VIPS paths
# =============================================================================
/regions/{region}/network/model/vips:
  post:
    operationId: CreateVips
    summary: create vips
    description: Create all the vips in the list, return created Vips and also failed to create items.
    tags:
      - networkVip
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkVips:
          actionKind: CREATE
          permissions: [ "NET_VIPS_CREATE" ]
          serviceResourceName: NetworkVips
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkVips'].grantedPermissions.contains('NET_VIPS_CREATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/RetryTokenHeader'
      - $ref: '#/parameters/RequestIdHeader'
      - name: CreateVipDetails
        description: Details for creating vip
        required: true
        in: body
        schema:
          $ref: '#/definitions/CreateVipDetails'
    x-related-resource: '#/definitions/CreateUpdateNetworkModelVipResponse'
    responses:
      201:
        description: The network vip created
        schema:
          $ref: '#/definitions/CreateUpdateNetworkModelVipResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/create-with-retry-token-responses.cond.yaml
  get:
    operationId: ListNetworkModelVips
    summary: lists all network vips
    description: lists all network vips
    tags:
      - networkVip
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkVips:
          actionKind: LIST
          permissions: [ "NET_VIPS_READ" ]
          serviceResourceName: NetworkVips
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkVips'].grantedPermissions.contains('NET_VIPS_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/RequestIdHeader'
    x-related-resource: '#/definitions/RegionNetworkModelVipSummary'
    responses:
      200:
        description: List of network vips
        schema:
          $ref: '#/definitions/RegionNetworkModelVipSummary'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml
  put:
    operationId: UpdateVips
    summary: Update vips.
    description: Update all the vips in the list, return updated Vips and also failed to update items.
    tags:
      - networkVip
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkVips:
          actionKind: UPDATE
          permissions: [ "NET_VIPS_UPDATE" ]
          serviceResourceName: NetworkVips
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkVips'].grantedPermissions.contains('NET_VIPS_UPDATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/RetryTokenHeader'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/IfMatchHeader'
      - name: UpdateVipDetails
        description: Details for updating vip
        required: true
        in: body
        schema:
          $ref: '#/definitions/UpdateVipDetails'
    x-related-resource: '#/definitions/CreateUpdateNetworkModelVipResponse'
    responses:
      200:
        description: Updated network vips matching the name.
        schema:
          $ref: '#/definitions/CreateUpdateNetworkModelVipResponse'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/create-with-retry-token-responses.cond.yaml

/regions/{region}/network/model/vips/{vipName}:
  get:
    operationId: GetVip
    summary: Fetch vip by name.
    description: Fetch network vip matching the name.
    tags:
      - networkVip
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkVips:
          actionKind: READ
          permissions: [ "NET_VIPS_READ" ]
          serviceResourceName: NetworkVips
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkVips'].grantedPermissions.contains('NET_VIPS_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/RetryTokenHeader'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/VipNamePathParam'
      - $ref: '#/parameters/IfMatchHeader'
    x-related-resource: '#/definitions/NetworkModelVip'
    responses:
      200:
        description: network vips matching the name.
        schema:
          $ref: '#/definitions/NetworkModelVip'
        headers:
          location:
            description: URL of resource
            type: string
            format: uri-reference
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/create-with-retry-token-responses.cond.yaml

/regions/{region}/network/model/devices/name/{deviceName}/configDiff/{baseConfig}:
  get:
    operationId: GetNetworkDeviceConfigDiff
    summary: Compare the network devices configs of the device with an option to compare latest, running and deployed.
    description: Diff of configs of network devices
    tags:
      - networkDevice
      - nmc
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkDevice:
          actionKind: READ
          permissions: [ "NET_DEVICE_READ" ]
          serviceResourceName: NetworkDevice
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkDevice'].grantedPermissions.contains('NET_DEVICE_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/RetryTokenHeader'
      - $ref: '#/parameters/RequestIdHeader'
      - $ref: '#/parameters/DeviceNamePathParam'
      - $ref: '#/parameters/CompareConfigBaseConfigPathParam'
      - $ref: '#/parameters/CompareConfigTargetConfigQueryParam'
      - $ref: '#/parameters/ConfigsMetadataIdQueryParam'
    x-related-resource: '#/definitions/NetworkModelConfigDiff'
    responses:
      200:
        description: Config diff
        schema:
          $ref: '#/definitions/NetworkModelConfigDiff'
        headers:
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

/regions/{region}/network/model/devices/state/intended:
  post:
    operationId: TransitionDeviceIntoTargetState
    summary: Transition devices into target state.
    description: Create workrequests to transition devices into target state.
    tags:
      - deviceStateChange
      - nmc
    x-obmcs-terraform:
      actionType: START
    x-obmcs-splat:
      apiType: Synchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkDeviceStateAttribute:
          actionKind: UPDATE
          permissions: [ "NET_DEVICE_STATE_ATTRIBUTE_UPDATE" ]
          serviceResourceName: NetworkDeviceStateAttribute
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkDeviceStateAttribute'].grantedPermissions.contains('NET_DEVICE_STATE_ATTRIBUTE_UPDATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/RetryTokenHeader'
      - $ref: '#/parameters/RequestIdHeader'
      - name: DeviceStateChangeDetails
        description: Details for transitioning devices to the target state.
        required: true
        in: body
        schema:
          $ref: '#/definitions/DeviceStateChangeDetails'
    x-related-resource: '#/definitions/WorkRequest'
    responses:
      200:
        description: Device state change response
        schema:
          $ref: '#/definitions/WorkRequest'
        headers:
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      409:
        description: Invalidated Retry Token
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id:
            description: Unique identifier for the request
            type: string
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

/regions/{region}/network/device/name/{deviceName}/runningConfig:
  get:
    operationId: GetRunningDeviceConfig
    summary: Get configs metadata for a device.
    description: Get configs metadata for a device.
    tags:
      - config
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkConfigs:
          actionKind: LIST
          permissions: [ "NET_CONFIGS_READ" ]
          serviceResourceName: NetworkDeploymentTask
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkConfigs'].grantedPermissions.contains('NET_CONFIGS_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/DeviceNamePathParam'
      - $ref: '#/parameters/RequestIdHeader'
    x-related-resource: '#/definitions/DeviceConfigResponse'
    responses:
      200:
        description: device config as string.
        schema:
          $ref: '#/definitions/DeviceConfigResponse'
        headers:
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

# New endpoint to get devices count in different states
/regions/{region}/network/analytics/deviceState:
  get:
    tags:
      - networkAnalytics
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkAnalytics:
          actionKind: READ
          permissions: [ "NET_ANALYTICS_READ" ]
          serviceResourceName: NetworkAnalytics
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkAnalytics'].grantedPermissions.contains('NET_ANALYTICS_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: GetDevicesStateAnalytics
    summary: Get Network Device counts in different states
    description: et Network Device counts in different states
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/RequestIdHeader'
    responses:
      200:
        description: Get All device states analytics
        schema:
          $ref: '#/definitions/NetworkDevicesStateStatsResponse'
        headers:
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/get-responses.cond.yaml

# New endpoint to get devices states with last updated time stamp
/regions/{region}/network/analytics/devices/state:
  get:
    tags:
      - networkAnalytics
      - nmc
    x-obmcs-splat:
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        networkAnalytics:
          actionKind: READ
          permissions: [ "NET_ANALYTICS_READ" ]
          serviceResourceName: NetworkAnalytics
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['networkAnalytics'].grantedPermissions.contains('NET_ANALYTICS_READ')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    operationId: ListAllDeviceStatesAnalytics
    summary: Retrieve all network device states
    description: Retrieve Network device states
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/isMonitoredQueryParam'
      - $ref: '#/parameters/RoleQueryParam'
      - $ref: '#/parameters/PaginationLimitQueryParam'
      - $ref: '#/parameters/PaginationTokenQueryParam'
      - $ref: '#/parameters/RequestIdHeader'
    responses:
      200:
        description: A page of device states with all attributes.
        schema:
          $ref: '#/definitions/NetworkDevicesStateAnalyticsResponseCollection'
        headers:
          opc-request-id: *ref-opc-request-id
          opc-next-page: *ref-opc-next-page
      @include_indented /responses-api-spec-include/source/list-responses.cond.yaml

  # Interface config attributes endpoints
/regions/{region}/network/model/devices/{deviceName}/interfaces/configAttribute:
  post:
    operationId: CreateNetInterfaceListConfigAttribute
    summary: Set config attributes on a list of interfaces for device
    description: |
      Sets interface config attribute on list of interfaces on a device given list of interface names
    tags:
      - netInterfaceConfigAttribute
      - nmc
    x-obmcs-splat:
      apiType: Asynchronous
      routing:
        strategy: route-to-any-ad
      serviceList: [ '${SERVICE_NAME}' ]
      resources:
        netInterfaceConfigAttributes:
          actionKind: CREATE
          permissions: [ "NET_INTERFACE_CONFIG_ATTRIBUTE_CREATE" ]
          serviceResourceName: NetInterfaceConfigAttributes
          targetCompartmentId: "'${INTERNAL_TARGET_COMPARTMENT_ID}'"
      authorization:
        mode: automated
        check: resources['netInterfaceConfigAttributes'].grantedPermissions.contains('NET_INTERFACE_CONFIG_ATTRIBUTE_CREATE')
        allowCrossTenancy: true
      tagStore:
        mode: none
      audit:
        mode: none
      search:
        mode: none
      quotas:
        mode: none
      maximumAttemptCount: 3
    parameters:
      - $ref: '#/parameters/RegionPathParam'
      - $ref: '#/parameters/DeviceNamePathParam'
      - $ref: '#/parameters/RetryTokenHeader'
      - $ref: '#/parameters/RequestIdHeader'
      - name: CreateNetInterfaceListConfigAttributeDetails
        description: interfaces and config attributes that will be applied to them
        required: true
        in: body
        schema:
          $ref: '#/definitions/CreateNetInterfaceListConfigAttributeDetails'
    x-related-resource: '#/definitions/NetInterfaceListConfigAttributeResponse'
    responses:
      200:
        description: Accepted the request. The configs will be rendered to object storage
        schema:
          $ref: '#/definitions/NetInterfaceListConfigAttributeResponse'
        headers:
          etag: *ref-etag
          opc-request-id: *ref-opc-request-id
      412:
        description: The provided ETag in the 'if-match' header does not match the current resource ETag.
        schema:
          $ref: '#/definitions/Error'
        headers:
          opc-request-id: *ref-opc-request-id
      @include_indented /responses-api-spec-include/source/create-with-retry-token-responses.cond.yaml
