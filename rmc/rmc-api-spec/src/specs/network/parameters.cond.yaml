# Namespace Entity Parameters
# =============================================================================
NamespacePathParam:
  name: namespace
  type: string
  in: path
  required: true
  description: The name of the namespace.

# PrefixGroup Entity Parameters
# =============================================================================
PrefixGroupQueryParam:
  name: prefixGroup
  type: string
  in: query
  required: false
  description: The name of the PrefixGroup.

# IpVersion Entity Parameters
# =============================================================================
IpVersionQueryParam:
  name: ipVersion
  type: string
  enum: ["4", "6"]
  in: query
  required: false
  description: The IpVersion 4 for ipv4 and 6 for ipv6.

# Fetch by Id Query Parameters
# =============================================================================
FetchByIdQueryParam:
  name: id
  type: string
  in: query
  required: true
  description: The Rmc Id of the entity.

# Prefix Entity Parameters
# =============================================================================
PrefixIdQueryParam:
  name: id
  type: string
  in: query
  required: false
  description: The Rmc Id of the prefix.

# ASNPOOL Entity Parameters
# =============================================================================
AsnpoolNamePathParam:
  name: asnPoolName
  type: string
  in: path
  required: true
  description: The name of the asnpool.

# Deployment Parameters
# =============================================================================
DeploymentPathParam:
  name: deploymentId
  type: string
  in: path
  required: true
  description: The Id of the deployment.

# Deployment Task Parameters
# =============================================================================
DeploymentTaskPathParam:
  name: deploymentTaskId
  type: string
  in: path
  required: true
  description: The Id of the deployment task.

# ASNTEMPLATE Entity Parameters
# =============================================================================
AsnTemplateIdPathParam:
  name: id
  type: string
  in: path
  required: true
  description: The Rmc Id of the asntemplate

AsnTemplatePoolNameQueryParam:
  name: poolName
  type: string
  in: query
  required: false
  description: The  poolName of the asntemplate

AsnTemplateRoleQueryParam:
  name: role
  type: string
  in: query
  required: false
  description: The role  of the asntemplate

NetworkFabricTypePathParam:
  name: type
  type: string
  in: path
  required: true
  description: The type of fabric

# Configs Metadata Parameters
# =============================================================================
ConfigTypePathParam:
  name: configType
  type: string
  enum:
    - device
    - dhcp
  in: path
  required: true
  description: Type of Config.

ConfigsMetadataPathParam:
  name: configsMetadataId
  type: string
  in: path
  required: true
  description: The Id of the configs metadata object.

ConfigMetadataIdPathParam:
  name: configMetadataId
  type: string
  in: path
  required: true
  description: The id of the configs metadata

ConfigStatusQueryParam:
  name: configStatus
  type: string
  enum:
    - latest
    - deployed
  in: query
  required: false
  description: Status of Config.


# Device config type diff Parameters
# =============================================================================
CompareConfigBaseConfigPathParam:
  name: baseConfig
  type: string
  required: true
  in: path
  description: config to compare
  enum:
    - running

CompareConfigTargetConfigQueryParam:
  name: targetConfig
  type: string
  required: true
  in: query
  description: config to compare
  enum:
    - latest
    - deployed
    - config_metadata_id

ConfigsMetadataIdQueryParam:
  name: configsMetadataId
  type: string
  required: false
  in: query
  description: Config metadata id of a rendered config to compare with running.

# Device Validation Status Parameters
# =============================================================================
DeviceValidationTypeQueryParam:
  name: type
  type: string
  required: true
  in: query
  description: Type of validation to check
  enum:
    - VALIDATED
    - REACHABLE

# Deployment Manifest Parameters
# =============================================================================
DeploymentManifestPathParam:
  name: deploymentManifestId
  type: string
  in: path
  required: true
  description: The Id of the deployment manifest.

# Fabric Manifest Parameters
# =============================================================================
FabricManifestPathParam:
  name: fabricManifestId
  type: string
  in: path
  required: true
  description: The Id of the fabric manifest.

# Get Device Parameters
# =============================================================================
DevicesSerialNumberPathParam:
  name: serialNumber
  type: string
  in: path
  required: true
  description: The serialNumber of the device.

DeviceNameQueryParam:
  name: deviceName
  type: string
  in: query
  required: false
  description: The name of the device.

DevicesSerialNumberQueryParam:
  name: serialNumber
  type: string
  in: query
  required: false
  description: The serialNumber of the device.

DeviceVendorQueryParam:
  name: vendor
  type: string
  in: query
  required: false
  description: The vendor/manufacturer name of the device.

DevicesVendorQueryParam:
  name: vendor
  type: string
  in: query
  required: false
  description: The vendor/manufacturer name of the device.

SkipNvswitchStateUpdateQueryParam:
  name: skipNvswitchStateUpdate
  type: boolean
  in: query
  required: false
  description: Default false. If true, then nvswitch state will be updated.

DeviceDcmsIdPathParam:
  name: dcmsId
  type: string
  in: path
  required: true
  description: DeviceModelId on DCMS side.

WithProcessDetailsQueryParam:
  name: withProcessDetails
  in: query
  required: false
  type: boolean
  description: get device states with process details

isMonitoredQueryParam:
  name: isMonitored
  in: query
  required: false
  type: boolean
  description: Filter for if device is monitored.

IsIncludeSerialNumberQueryParam:
  name: isIncludeSerialNumber
  in: query
  required: false
  type: boolean
  description: Filter if the serial number needs to be queried

DeviceIntendedStateValue:
  name: intendedState
  type: string
  enum: ["NEW",
        "READY",
        "IN_SERVICE",
        "MAINTENANCE",
        "DECOMMISSIONED"]
  in: query
  required: false
  description: |
    The state value for the interface:
    * `READY` - The device is getting ready
    * `IN_SERVICE` - The device has traffic

DeviceCurrentStateValue:
  name: currentState
  type: string
  enum: ["NEW",
         "READY",
         "IN_SERVICE",
         "MAINTENANCE",
         "DECOMMISSIONED"]
  in: query
  required: false
  description: |
    The state value for the interface:
    * `READY` - The device is getting ready
    * `IN_SERVICE` - The device has traffic

AutomationStateValue:
  name: automationState
  type: string
  enum: ["ENABLED",
        "DISABLED"]
  in: query
  required: false
  description: |
    The state value for the interface:
    * `ENABLED` - The device is controlled by automation flows like NHRS and LSM
    * `DISABLED` - The device is not controlled by automation flows

InterfaceIntendedStateValue:
  name: intendedState
  type: string
  enum: ["IN_SERVICE",
         "DRAINED",
         "UN_USED",
         "DISABLED"]
  in: query
  required: false
  description: |
    The state value for the interface:
    * `DISABLED` - The interface is administratively down
    * `IN_SERVICE` - The interface has peer information

InterfaceCurrentStateValue:
  name: currentState
  type: string
  enum: ["IN_SERVICE",
         "DRAINED",
         "UN_USED",
         "DISABLED"]
  in: query
  required: false
  description: |
    The state value for the interface:
    * `DISABLED` - The interface is administratively down
    * `IN_SERVICE` - The interface has peer information

IsWithStateQueryParam:
  name: isWithState
  in: query
  required: false
  type: boolean
  default: true
  description: Filter for device state.

RoleQueryParam:
  name: role
  in: query
  required: false
  type: string
  description: Filter for device role.

DeviceModelQueryParam:
  name: deviceModel
  in: query
  required: false
  type: string
  description: Filter for device model.

ConfigPlatformQueryParam:
  name: configPlatform
  in: query
  required: false
  type: string
  description: Filter for config platform.

IpAddressQueryParam:
  name: ipAddress
  in: query
  required: false
  type: string
  description: Filter for ip address.

ForceApplyQueryParam:
  name: isForceApply
  in: query
  required: false
  type: boolean
  description: Force a certain interface rule on the network model.

TimestampQueryParam:
  name: timestamp
  in: query
  required: false
  format: date-time
  type: string
  description: Filter for device updated time.

ResponseFields:
  name: fields
  in: query
  required: false
  type: string
  description: Comma-separated list of fields to include in the response. Example - `id,networkModelId,deviceModel`

NetworkDevicePaginationLimitQueryParam:
  name: limit
  in: query
  type: integer
  minimum: 1
  maximum: 2000
  default: 1000
  description: |
    For list pagination. The maximum number of results per page, or items to return in a
    paginated "List" call. For important details about how pagination works, see
    [List Pagination](/iaas/Content/API/Concepts/usingapi.htm#nine).

HasChildrenQueryParam:
  name: hasChildren
  in: query
  required: false
  type: boolean
  description: Filter for if device is monitored.

# Device State Parameters
# =============================================================================
DeviceStatePathParam:
  name: device-name
  type: string
  in: path
  required: true
  description: The identifier of the state.

StateAttributePathParam:
  name: attribute
  type: string
  in: path
  required: true
  description: The attribute of the state.

# Network Link Parameters
# =============================================================================
DeviceNamePathParam:
  name: deviceName
  type: string
  in: path
  required: true
  description: The name of the device.

InterfaceNamePathParam:
  name: interfaceName
  type: string
  in: path
  required: true
  description: The interface name on the the device.

InterfaceHostStateValue:
  name: stateValue
  type: string
  enum: ["DISABLED", "IN_SERVICE"]
  in: path
  required: true
  description: |
    The state value for the interface:
    * `DISABLED` - The interface is administratively down
    * `IN_SERVICE` - The interface has peer information

InterfaceInteriorStateValue:
  name: stateValue
  type: string
  enum: ["DRAINED", "IN_SERVICE"]
  in: path
  required: true
  description: |
    The state value for the interface:
    * `DRAINED` - Administratively up with strong de-preference as a preferred path. Ping and LLDP operate as expected, but no production traffic is expected to traverse the interface
    * `IN_SERVICE` - The interface has peer information

# Cos Profile Details
# =============================================================================
ProfileNamePathParam:
  name: profileName
  type: string
  in: path
  required: true
  description: The cos profile to associate the device with.

ForceUnsafeParam:
  name: forceUnsafe
  type: boolean
  in: query
  required: false
  description: Apply this profile without change window and immediately to the device

RemoveCosProfileQueryParam:
  name: removeCosProfile
  type: boolean
  in: query
  required: false
  description: If a COS profile should be attached or removed from the interface or device.


# External Peer Parameters
# =============================================================================
ExternalPeerPathParam:
  name: peerName
  type: string
  in: path
  required: true
  description: The identifier of the external peer.


# Vip Parameters
# =============================================================================
VipNamePathParam:
  name: vipName
  type: string
  in: path
  required: true
  description: The name of the network vip.

# Location API Param
# ================================================
ElevationQueryParam:
  name: elevation
  type: string
  in:  query
  required: false
  description: Elevation of rack.

SiteQueryParam:
  name: site
  type: string
  in:  query
  required: true
  description: site of rack.

# Location API Param
# ================================================
NetworkDevicePortsPaginationLimitQueryParam:
  name: limit
  in: query
  type: integer
  minimum: 1
  maximum: 2000
  default: 1000
  description: |
    For list pagination. The maximum number of results per page, or items to return in a
    paginated "List" call. For important details about how pagination works, see
    [List Pagination](/iaas/Content/API/Concepts/usingapi.htm#nine).

# Firmware Image Parameters
# =============================================================================
FirmwareImagePathParam:
  name: firmwareImageId
  type: string
  in: path
  required: true
  description: The Id of the firmware image.

# Region Parameters
# =============================================================================
IsActiveQueryParam:
  name: isActive
  in: query
  required: false
  x-default-description: 'null'
  type: boolean
  description: A filter to return only the active region(s)

# NetInterfaceRule related parameters
# =============================================================================
NetInterfaceRuleId:
  name: netInterfaceRuleId
  type: string
  in: path
  required: true
  description: The Id of the network interface rule
