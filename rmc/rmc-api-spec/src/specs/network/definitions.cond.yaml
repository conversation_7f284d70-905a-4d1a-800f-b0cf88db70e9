# Network definitions.
# This is being included in the 'definitions:' section.
# =============================================================================
# DHCP Configs Rendering Definitions
# =============================================================================
UpdateDhcpConfigResponse:
  description: The result of DHCP config rendering.
  type: object
  required:
    - configs
  properties:
    configs:
      type: object
      description: DHCP Config type to generated config PAR in Object Storage.
      additionalProperties:
        type: string

# Template Development Definitions
# =============================================================================
RenderNetworkDevicesConfigDetails:
  description: The data needed to render a network device config.
  type: object
  required:
    - deviceIds
  properties:
    deviceIds:
      description: List of devices IDs for which to render configs.
      type: array
      minItems: 1
      items:
        type: string
        minLength: 1
        maxLength: 255
    templatesArtifact:
      description: Templates artifact name in object storage. If not provided, the active templates artifact will be used.
      type: string
      minLength: 1
      maxLength: 255
    isUpdateLatestConfig:
      description: Flag to update device latest config during render device Process.
      type: boolean
      default: false

RenderNetworkDevicesConfigDetailsInSync:
  description: The data needed to render a network device config during sync operation.
  type: object
  required:
    - templatesArtifact
  properties:
    templatesArtifact:
      description: Templates artifact name in object storage.
      type: string
      minLength: 1
      maxLength: 255
    isUpdateLatestConfig:
      description: Flag to update device latest config during render device Process.
      type: boolean
      default: false


# Namespace Entity Definitions
# =============================================================================
CreateNamespaceDetails:
  description: |
    A Namespace  information for creation.
  type: object
  required:
    - name
  properties:
    name:
      description: The name of the namespace
      type: string
      minLength: 1
      maxLength: 255

NamespaceResponse:
  description: |
    A Namespace
  type: object
  required:
    - id
    - name
  properties:
    id:
      description: The [UUID](/iaas/Content/General/Concepts/identifiers.htm) of the Namespace.
      type: string
      minLength: 1
      maxLength: 255
    name:
      description: The name of the namespace.
      type: string
      minLength: 1
      maxLength: 255
    createdBy:
      type: string
      minLength: 1
      maxLength: 255
    modifiedBy:
      type: string
      minLength: 1
      maxLength: 255
    timeCreated:
      description: |
        The date and time the resource was created, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).

        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time
    timeUpdated:
      description: |
        The date and time the resource was updated, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).

        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time

NamespaceResponseCollection:
  description: |
    List of Namespaces.
  type: object
  required:
    - items
  properties:
    items:
      description: List of Namespaces .
      type: array
      items:
        $ref: "#/definitions/NamespaceResponse"

# Prefix Entity Definitions
# =============================================================================
CreatePrefixDetails:
  description: |
    A Prefix information for creation.
  type: object
  required:
    - prefixGroup
    - prefixStr
    - description
  properties:
    prefixGroup:
      description: The prefixGroup.
      type: string
      minLength: 1
      maxLength: 512
    prefixStr:
      description: prefixStr.
      type: string
      minLength: 1
      maxLength: 255
    description:
      type: string
      minLength: 1
      maxLength: 255

PrefixResponse:
  description: |
    A Prefix Record.
  type: object
  properties:
    id:
      description: The [UUID](/iaas/Content/General/Concepts/identifiers.htm) of the Namespace.
      type: string
      minLength: 1
      maxLength: 255
    prefixGroup:
      description: The prefixGroup.
      type: string
      minLength: 1
      maxLength: 512
    namespace:
      description: The namespace.
      type: string
      minLength: 1
      maxLength: 255
    ipVersion:
      description: v4 or v6.
      type: string
      minLength: 1
      maxLength: 255
    prefixStr:
      description: prefixStr.
      type: string
      minLength: 1
      maxLength: 255
    prefixNumber:
      description: Prefix Number.
      type: integer
      format: int64
      minLength: 0
    parentPrefixStr:
      description: the parent for this prefix.
      type: string
    description:
      type: string
      minLength: 1
      maxLength: 255
    createdBy:
      type: string
      minLength: 1
      maxLength: 255
    modifiedBy:
      type: string
      minLength: 1
      maxLength: 255
    timeCreated:
      description: |
        The date and time the resource was created, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).

        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time
    timeUpdated:
      description: |
        The date and time the resource was updated, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).

        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time

PrefixResponseCollection:
  description: |
    List of Prefix.
  type: object
  required:
    - items
  properties:
    items:
      description: List of Prefix .
      type: array
      items:
        $ref: "#/definitions/PrefixResponse"

# IpamPoolTemplate Entity Definitions
# =============================================================================
CreatePoolTemplateDetails:
  description: |
    A pool template information for creation.
  type: object
  required:
    - prefixSelector
    - description
  properties:
    description:
      type: string
      minLength: 1
      maxLength: 255
    prefixGroup:
      description: prefix group.
      type: string
      minLength: 1
      maxLength: 512

PoolTemplateResponse:
  description: |
    A Pool template Record.
  type: object
  properties:
    id:
      description: The [UUID](/iaas/Content/General/Concepts/identifiers.htm) of the Pool.
      type: string
      minLength: 1
      maxLength: 255
    prefixGroup:
      description: prefix group.
      type: string
      minLength: 1
      maxLength: 512
    description:
      type: string
      minLength: 1
      maxLength: 255
    sourcePrefixes:
      description: list of prefix sources.
      type: array
      minItems: 1
      items:
        type: string
        minLength: 1
        maxLength: 255
    createdBy:
      type: string
      minLength: 1
      maxLength: 255
    modifiedBy:
      type: string
      minLength: 1
      maxLength: 255
    timeCreated:
      description: |
        The date and time the resource was created, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).

        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time
    timeUpdated:
      description: |
        The date and time the resource was updated, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).

        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time

# AllocationRule Entity Definitions
# =============================================================================
AllocationSubnets:
  description: |
    Details for declaring a fabric subnet
  type: object
  required:
    - groupBy
    - allocationLength
  properties:
    groupBy:
      description: subnet group
      type: string
      minLength: 1
      maxLength: 255
    allocationLength:
      description: allocation length
      type: integer

CreateAllocationRuleDetails:
  description: |
    Details for creating an IP allocation rule.
  type: object
  required:
    - name
    - prefixGroup
    - description
    - fabricSelector
    - subnets
  properties:
    name:
      type: string
      minLength: 1
      maxLength: 255
    description:
      type: string
      minLength: 1
      maxLength: 255
    prefixGroup:
      description: prefix group.
      type: string
      minLength: 1
      maxLength: 255
    fabricSelector:
      description: The templateSelector for fabrics.
      type: object
      properties:
        fabricType:
          description: The fabric type.
          type: string
    subnets:
      description: A list of network interface rules
      type: array
      items:
        $ref: '#/definitions/AllocationSubnets'

UpdateAllocationRuleDetails:
  description: |
    Details for updating an IP allocation rule.
  type: object
  required:
    - prefixGroup
  properties:
    name:
      description: name of allocation rule
      type: string
      minLength: 1
      maxLength: 255
    description:
      description: meaningful description of allocation rule
      type: string
      minLength: 1
      maxLength: 255
    prefixGroup:
      description: prefix group.
      type: string
      minLength: 1
      maxLength: 255
    subnets:
      description: A list of allocation subnets
      type: array
      items:
        $ref: '#/definitions/AllocationSubnets'


AllocationRuleResponse:
  description: |
    An allocation rule record.
  type: object
  properties:
    id:
      description: The [UUID](/iaas/Content/General/Concepts/identifiers.htm) of the Pool.
      type: string
      minLength: 1
      maxLength: 255
    prefixGroup:
      description: prefix group.
      type: string
      minLength: 1
      maxLength: 255
    description:
      type: string
      minLength: 1
      maxLength: 255
    name:
      description: allocation rule name
      type: string
      minLength: 1
      maxLength: 255
    fabricSelector:
      description: the fabric selection criteria
      type: string
      minLength: 1
    subnets:
      description: A list of network interface rules
      type: string
      minLength: 1
    createdBy:
      type: string
      minLength: 1
      maxLength: 255
    modifiedBy:
      type: string
      minLength: 1
      maxLength: 255
    timeCreated:
      description: |
        The date and time the resource was created, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).

        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time
    timeUpdated:
      description: |
        The date and time the resource was updated, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).

        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time

AllocationRuleUpdateResponse:
  description: Allocation rule update reseponse
  type: object
  properties:
    allocationRule:
      $ref: '#/definitions/AllocationRuleResponse'

# Declare asn pool Entity Definitions
# =============================================================================
CreateAsnPoolDetails:
  description: |
    An AsnPool information for creation.
  type: object
  required:
    - name
  properties:
    name:
      description: The name of the asnpool
      type: string
      minLength: 1
      maxLength: 255
    description:
      description: The description of the asnpool
      type: string
      minLength: 1
      maxLength: 255
    ranges:
      description: The range of the asnpool
      type: array
      items:
        type: array
        items:
          type: integer
          format: int64

AsnPool:
  description: |
    An AsnPool information for creation.
  type: object
  required:
    - id
    - name
    - ranges
    - createdBy
    - modifiedBy
    - timeCreated
    - timeUpdated
  properties:
    id:
      description: The [UUID](/iaas/Content/General/Concepts/identifiers.htm) of the Asnpool.
      type: string
      minLength: 1
      maxLength: 255
    name:
      description: The name of the asnpool
      type: string
      minLength: 1
      maxLength: 255
    description:
      description: The description of the asnpool
      type: string
      minLength: 1
      maxLength: 255
    ranges:
      description: The range of the asnpool
      type: array
      items:
        type: array
        items:
          type: integer
          format: int64
    createdBy:
      type: string
      minLength: 1
      maxLength: 255
    modifiedBy:
      type: string
      minLength: 1
      maxLength: 255
    timeCreated:
      description: |
        The date and time the resource was created, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).

        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time
    timeUpdated:
      description: |
        The date and time the resource was updated, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).

        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time

# Declare firmware Entity Definitions
# =============================================================================
CreateFirmwareImageDetails:
  description: |
    An FirmwareImage information for creation.
  type: object
  required:
    - fileName
    - firmwareVersion
    - fileSize
    - md5Checksum
  properties:
    fileName:
      description: The name of the firmware file
      type: string
      minLength: 1
      maxLength: 255
    firmwareVersion:
      description: The firmware version
      type: string
      minLength: 1
      maxLength: 255
    fileSize:
      description: The size of the firmware file
      type: integer
      minimum: 1
    md5Checksum:
      description: The checksum of the firmware file
      type: string
      minLength: 1
      maxLength: 255

FirmwareImageCollection:
  type: object
  description: The response of the FirmwareImageResponse.
  required:
    - items
  properties:
    items:
      description: will return all FirmwareImageResponse.
      type: array
      items:
        $ref: '#/definitions/FirmwareImageResponse'

FirmwareImageResponse:
  description: |
    An FirmwareImage information.
  type: object
  required:
    - id
    - fileName
    - firmwareVersion
    - fileSize
    - md5Checksum
    - firmwareUrl
    - createdBy
    - modifiedBy
    - timeCreated
    - timeUpdated
  properties:
    id:
      description: The [UUID](/iaas/Content/General/Concepts/identifiers.htm) of the FirmwareImage.
      type: string
      minLength: 1
      maxLength: 255
    fileName:
      description: The name of the firmware file
      type: string
      minLength: 1
      maxLength: 255
    firmwareVersion:
      description: The firmware version
      type: string
      minLength: 1
      maxLength: 255
    fileSize:
      description: The size of the firmware file
      type: integer
      minimum: 1
    md5Checksum:
      description: The checksum of the firmware file
      type: string
      minLength: 1
      maxLength: 255
    firmwareUrl:
      description: The PAR url of the firmware file in object storage
      type: string
      minLength: 1
      maxLength: 1024
    createdBy:
      type: string
      minLength: 1
      maxLength: 255
    modifiedBy:
      type: string
      minLength: 1
      maxLength: 255
    timeCreated:
      description: |
        The date and time the resource was created, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).

        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time
    timeUpdated:
      description: |
        The date and time the resource was updated, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).

        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time

CreateFirmwareMappingDetails:
  description: |
    An FirmwareMapping information for creation.
  type: object
  required:
    - firmwareImageId
    - deviceModel
    - role
    - configPlatform
  properties:
    firmwareImageId:
      description: The name of the firmware file
      type: string
      minLength: 1
      maxLength: 255
    deviceModel:
      description: The firmware version
      type: string
      minLength: 1
      maxLength: 255
    role:
      description: The checksum of the firmware file
      type: string
      minLength: 1
      maxLength: 255
    configPlatform:
      description: The supported config platform for the device
      type: string
      minLength: 0
      maxLength: 255

FirmwareMappingCollection:
  type: object
  description: The response of the FirmwareMapping.
  required:
    - items
  properties:
    items:
      description: will return all FirmwareMappingResponse.
      type: array
      items:
        $ref: '#/definitions/FirmwareMappingResponse'

FirmwareMappingResponse:
  description: |
    An FirmwareMapping information.
  type: object
  required:
    - id
    - firmwareImageId
    - deviceModel
    - role
    - configPlatform
  properties:
    id:
      description: The [UUID](/iaas/Content/General/Concepts/identifiers.htm) of the FirmwareMapping.
      type: string
      minLength: 1
      maxLength: 255
    firmwareImageId:
      description: The id of the firmware image
      type: string
      minLength: 1
      maxLength: 255
    deviceModel:
      description: The device model
      type: string
      minLength: 1
      maxLength: 255
    role:
      description: The role of device
      type: string
      minLength: 1
      maxLength: 255
    configPlatform:
      description: The supported config platform for the device
      type: string
      minLength: 0
      maxLength: 255
    fileName:
      description: The name of the firmware file
      type: string
      minLength: 1
      maxLength: 255
    firmwareVersion:
      description: The firmware version
      type: string
      minLength: 1
      maxLength: 255
    fileSize:
      description: The size of the firmware file
      type: integer
      minimum: 1
    md5Checksum:
      description: The checksum of the firmware file
      type: string
      minLength: 1
      maxLength: 255
    firmwareUrl:
      description: The PAR url of the firmware file in object storage
      type: string
      minLength: 1
      maxLength: 1024
    createdBy:
      type: string
      minLength: 1
      maxLength: 255
    modifiedBy:
      type: string
      minLength: 1
      maxLength: 255
    timeCreated:
      description: |
        The date and time the resource was created, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).

        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time
    timeUpdated:
      description: |
        The date and time the resource was updated, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).

        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time

# NetInterfaceRule Entity Definitions
# =============================================================================
RuleTemplateSelector:
  description: JSON object with the selection criteria parameters for this rule
  required:
    - sourceRole
  properties:
    fabricOffering:
      description: fabric of device selected
      type: string
      minLength: 1
      maxLength: 255
    sourceRole:
      description: role of device selected
      type: string
      minLength: 1
      maxLength: 255
    platform:
      description: platform of device selected
      type: string
      minLength: 1
      maxLength: 255
    portGroup:
      description: group id of port selected
      type: string
      minLength: 1
      maxLength: 255
    destRole:
      description: role of device destination device
      type: string
      minLength: 1
      maxLength: 255

CreateVirtualNetInterfaceRuleDetails:
  description: |
    The data needed to create a Network Interface Rule
  type: object
  required:
    - name
    - description
    - ruleSelector
    - sourcePool
  properties:
    name:
      description: The name of the net-interface config rule
      type: string
      minLength: 1
      maxLength: 255
    description:
      description: Short summary of the configuration applied by this rule
      type: string
      minLength: 1
      maxLength: 255
    ruleSelector:
      description: JSON object with the selection criteria parameters for this rule
      $ref: '#/definitions/RuleTemplateSelector'
    sourcePool:
      description: The [UUID](/iaas/Content/General/Concepts/identifiers.htm) of the address pool associated with this rule, if assigning IP addresses.
      type: string
      minLength: 1
      maxLength: 255
    monitoredInterface:
      description: set to true if interface is monitored
      type: boolean



CreateLayer2NetInterfaceRuleDetails:
  description: The data needed to create a Layer2 Network Interface Rule
  type: object
  required:
    - name
    - description
    - ruleSelector
    - mtu
    - startVlan
  properties:
    name:
      description: The name of the net-interface config rule
      type: string
      minLength: 1
      maxLength: 255
    description:
      description: Short summary of the configuration applied by this rule
      type: string
      minLength: 1
      maxLength: 255
    ruleSelector:
      description: JSON object with the selection criteria parameters for this rule
      $ref: '#/definitions/RuleTemplateSelector'
    sourcePool:
      description: The [UUID](/iaas/Content/General/Concepts/identifiers.htm) of the address pool associated with this rule, if assigning IP addresses.
      type: string
      minLength: 1
      maxLength: 255
    mtu:
      description: Maximum transmission unit (MTU) value to configure for L2 interface
      type: integer
    startVlan:
      description: Vlan group id for L2 interface
      type: integer
    monitoredInterface:
      description: set to true if interface is monitored
      type: boolean
    parentRuleName:
      description: The parent rule name of the net-interface config rule
      type: string
      minLength: 1
      maxLength: 255
    overflowAllowed:
      description: Is IP over flow allowed
      type: boolean

CreateLayer3NetInterfaceRuleDetails:
  description: The data needed to create a Layer3 Network Interface Rule
  type: object
  required:
    - name
    - description
    - ruleSelector
    - sourcePool
    - mtu
  properties:
    name:
      description: The name of the net-interface config rule
      type: string
      minLength: 1
      maxLength: 255
    description:
      description: Short summary of the configuration applied by this rule
      type: string
      minLength: 1
      maxLength: 255
    ruleSelector:
      description: JSON object with the selection criteria parameters for this rule
      $ref: '#/definitions/RuleTemplateSelector'
    sourcePool:
      description: The [UUID](/iaas/Content/General/Concepts/identifiers.htm) of the address pool associated with this rule, if assigning IP addresses.
      type: string
      minLength: 1
      maxLength: 255
    interfaceType:
      description: Type of Interface for layer 3
      type: string
      minLength: 1
      maxLength: 255
    mtu:
      description: Maximum transmission unit (MTU) value to configure for L3 interface
      type: integer
    monitoredInterface:
      description: set to true if interface is monitored
      type: boolean
    parentRuleName:
      description: The parent rule name of the net-interface config rule
      type: string
      minLength: 1
      maxLength: 255
    overflowAllowed:
      description: Is IP overflow allowed
      type: boolean

NetInterfaceRuleResponse:
  description: |
    A Network Interface Rule Record
  type: object
  required:
    - id
    - name
    - description
    - interfaceType
    - ruleSelector
  properties:
    id:
      description: The [UUID](/iaas/Content/General/Concepts/identifiers.htm) of the Network Interface rule.
      type: string
      minLength: 1
      maxLength: 255
    name:
      description: The name of the net-interface config rule
      type: string
      minLength: 1
      maxLength: 255
    parentRuleId:
      description: The name of the net-interface config rule
      type: string
      minLength: 1
      maxLength: 255
    description:
      description: Short summary of the configuration applied by this rule
      type: string
      minLength: 1
      maxLength: 255
    interfaceType:
      description: Network interface type that this rule applies to
      type: string
      minLength: 1
      maxLength: 255
    ruleSelector:
      description: JSON object with the selection criteria parameters for this rule
      type: string
      minLength: 1
      maxLength: 1024
    sourcePool:
      description: The [UUID] of the address pool associated with this rule, if assigning IP addresses.
      type: string
      minLength: 1
      maxLength: 255
    role:
      description: selector fro device role type
      type: string
      minLength: 1
      maxLength: 255
    mtu:
      description: Maximum transmission unit (MTU) value to configure for L2 interface
      type: integer
    startVlan:
      description: VLAN id for L2 interface
      type: integer
    monitoredInterface:
      description: set to true if interface is monitored
      type: boolean
    overflowAllowed:
      description: set to true if interface is monitored
      type: boolean
    createdBy:
      type: string
      minLength: 1
      maxLength: 255
    modifiedBy:
      type: string
      minLength: 1
      maxLength: 255
    timeCreated:
      description: |
        The date and time the resource was created, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).

        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time
    timeUpdated:
      description: |
        The date and time the resource was updated, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).

        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time

NetInterfaceRuleCollection:
  description: Response containing a list of network interface rules
  type: object
  required:
    - items
  properties:
    items:
      description: A list of network interface rules
      type: array
      items:
        $ref: '#/definitions/NetInterfaceRuleResponse'

# ASNTemplate Entity Definitions
# =============================================================================
CreateAsnTemplateDetails:
  description: |
    An AsnTemplate information for creation.
  type: object
  required:
    - asn
    - deviceSelector
  properties:
    name:
      description: Name of asn pool
      type: string
    groupBy:
      description: Name of asn template as groupBy
      type: string
    deviceSelector:
      $ref: "#/definitions/CreateAsnSelectorDetails"
    asn:
      $ref: "#/definitions/CreateAsnTypeDetails"

CreateAsnSelectorDetails:
  description: The selector for ASN
  type: object
  properties:
    selectorType:
      description: The type of the selector, Current supported is "device" and "fabric"
      type: string
    fabricType:
      description: The fabric type.
      type: string
    version:
      description: The fabric version.
      type: integer
    role:
      description: The role of the fabric.
      type: string

CreateAsnTypeDetails:
  description: The type of ASN
  type: object
  properties:
    type:
      description: The type of asn.
      type: string
    value:
      description: The value of asn.
      type: string
    range:
      type: object
      properties:
        startRange:
          type: integer
          format: int64
        endRange:
          type: integer
          format: int64

AsnTemplateResponse:
  description: |
    An AsnTemplate information for creation.
  type: object
  required:
    - asn
    - deviceSelector
    - id
  properties:
    name:
      description: Name of asn pool
      type: string
    groupBy:
      description: Name of asn template as groupBy
      type: string
    deviceSelector:
      $ref: "#/definitions/CreateAsnSelectorDetails"
    asn:
      $ref: "#/definitions/CreateAsnTypeDetails"
    id:
      description: The [UUID](/iaas/Content/General/Concepts/identifiers.htm) of the Asntemplate.
      type: string
      minLength: 1
      maxLength: 255

AsnTemplateResponseCollection:
  description: |
    Results of a asntemplate by search.
  type: object
  required:
    - items
  properties:
    items:
      description: List of asntemplate .
      type: array
      items:
        $ref: "#/definitions/AsnTemplateResponse"

# Device Entity Definitions
# =============================================================================
NetworkDeviceResponseCollection:
  description: |
    Collection of devices.
  type: object
  required:
    - items
  properties:
    items:
      description: List of devices .
      type: array
      items:
        $ref: "#/definitions/NetworkDeviceResponse"

NetworkDeviceResponse:
  description: |
    Network device information.
  type: object
  required:
    - id
    - dcmsDeviceId
    - deviceName
    - role
    - deviceIndex
  properties:
    id:
      description: The unique device id in the database.
      type: string
      minLength: 1
      maxLength: 255
    serialNumber:
      description: The serial number of the data in database
      type: string
      minLength: 1
      maxLength: 255
    dcmsDeviceId:
      description: The unique device id of the device in the dcms database
      type: string
      minLength: 1
      maxLength: 255
    parentDeviceId:
      description: The parent unique Id
      type: string
      minLength: 1
      maxLength: 255
    deviceName:
      description: The name of the device
      type: string
      minLength: 1
      maxLength: 128
    configPlatform:
      description: The supported config platform for the device
      type: string
      minLength: 0
      maxLength: 255
    deviceModel:
      description: The device model
      type: string
      minLength: 1
      maxLength: 128
    role:
      description: The device role
      type: string
      minLength: 1
      maxLength: 128
    isMonitored:
      description: Is device monitored
      type: boolean
    ipAddress:
      description: The device ip address
      type: string
      minLength: 0
      maxLength: 255
    deviceIndex:
      description: The device index
      type: integer
      minimum: 0
    ports:
      description: List of ports for this device
      type: array
      items:
        $ref: "#/definitions/NetworkPortResponse"
    interfaces:
      description: List of interfaces for this device
      type: array
      items:
        $ref: "#/definitions/NetworkInterfaceResponse"
    routingContexts:
      description: List of routing contexts for this device
      type: array
      items:
        $ref: "#/definitions/DeviceRoutingContextResponse"
    state:
      $ref: "#/definitions/DeviceLifeCycleStateDetails"
    locationMetadata:
      $ref: "#/definitions/LocationMetadata"
    fabricMetadata:
      $ref: "#/definitions/FabricMetadata"
    childrenDevices:
      description: List of children devices for this device
      type: array
      items:
        $ref: "#/definitions/NetworkDeviceResponse"
    createdBy:
      type: string
      minLength: 1
      maxLength: 255
    modifiedBy:
      type: string
      minLength: 1
      maxLength: 255
    timeCreated:
      description: |
        The date and time the resource was created, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).
        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time
    timeUpdated:
      description: |
        The date and time the resource was updated, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).
        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time

## Device Location Metadata Definitions
## =============================================================================
LocationMetadata:
  description: |
    Device location metadata information
  type: object
  required:
    - site
    - room
    - rackPosition
    - rackElevation
    - building
    - dataHall
  properties:
    site:
      description: site of the device
      type: string
      minLength: 1
      maxLength: 128
    room:
      description: room of the device
      type: string
      minLength: 1
      maxLength: 128
    rackPosition:
      description: rack position of the device
      type: string
      minLength: 1
      maxLength: 128
    rackElevation:
      description: rack elevation of the device
      type: string
      minLength: 1
      maxLength: 128
    building:
      description: building of the device
      type: string
      minLength: 1
      maxLength: 128
    dataHall:
      description: data hall of the device
      type: string
      minLength: 1
      maxLength: 128

## Device Fabric Metadata Definitions
## =============================================================================
FabricMetadata:
  description: |
    Device fabric metadata information
  type: object
  required:
    - id
    - displayName
    - fabricType
    - instance
    - fabricVersion
  properties:
    id:
      description: id of the fabric
      type: string
      minLength: 1
      maxLength: 128
    displayName:
      description: display name of the fabric
      type: string
      minLength: 1
      maxLength: 128
    fabricType:
      description: fabric type of the device
      type: string
      minLength: 1
      maxLength: 128
    instance:
      description: fabric instance
      type: string
      minLength: 1
      maxLength: 128
    fabricVersion:
      description: fabric plane
      type: string
      minLength: 1
      maxLength: 128

# Port Entity Definitions
# =============================================================================
NetworkPortResponse:
  description: |
    Port information.
  type: object
  required:
    - id
    - dcmsId
    - portName
    - portType
    - speed
  properties:
    id:
      description: The unique port id in the database.
      type: string
      minLength: 1
      maxLength: 255
    dcmsId:
      description: The unique port id of the port in the dcms database
      type: string
      minLength: 1
      maxLength: 255
    portName:
      description: The name of the port
      type: string
      minLength: 1
      maxLength: 128
    portGroup:
      description: The port group
      type: string
      minLength: 0
      maxLength: 255
    portType:
      description: The port type
      type: string
      minLength: 1
      maxLength: 128
    peerPortId:
      description: The peer port id
      type: string
      minLength: 0
      maxLength: 255
    speed:
      description: The speed of the port
      type: integer
      format: int64
      minLength: 0
    createdBy:
      type: string
      minLength: 1
      maxLength: 255
    modifiedBy:
      type: string
      minLength: 1
      maxLength: 255
    timeCreated:
      description: |
        The date and time the resource was created, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).
        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time
    timeUpdated:
      description: |
        The date and time the resource was updated, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).
        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time

# Interface Entity Definitions
# =============================================================================
InterfaceLifeCycleState:
  description: Life Cycle State of an interface
  type: string
  enum: &INTERFACE_LIFE_CYCLE_STATE
    - IN_SERVICE
    - DRAINED
    - UN_USED
    - DISABLED

InterfaceHealthStatus:
  description: The health status of interface.
  type: string
  enum: &INTERFACE_HEALTH_STATUS
    - UN_REACHABLE
    - UN_HEALTHY
    - HEALTHY

NetworkInterfaceState:
  description: A resource used by SPLAT only to fulfill permissions check contract.
  x-obmcs-splat:
    ocidEntityType: service
    adLocality: regional
    resourceKind: networkInterfaceState

CustomerFacingInterfaceResponse:
  description: Interface information.
  type: object
  required:
    - id
  properties:
    id:
      description: The unique interface id in the database.
      type: string
      minLength: 1
      maxLength: 255
    interfaceName:
      description: The name of the interface
      type: string
      minLength: 1
      maxLength: 128
    workRequestId:
      type: string
      description: The [OCID](/iaas/Content/General/Concepts/identifiers.htm) of the work request.
    intendedState:
      description: The state the interface should be in.
      type: string
      enum: *INTERFACE_LIFE_CYCLE_STATE
      x-obmcs-top-level-enum: '#/definitions/InterfaceLifeCycleState'
    currentState:
      description: The current operational state of the interface.
      type: string
      enum: *INTERFACE_LIFE_CYCLE_STATE
      x-obmcs-top-level-enum: '#/definitions/InterfaceLifeCycleState'
    description:
      description: The interface description
      type: string
      minLength: 0
      maxLength: 255
    interfaceGroup:
      description: The interface group
      type: string
      minLength: 1
      maxLength: 128
    type:
      description: The type of interface
      type: string
      minLength: 0
      maxLength: 255
    mtu:
      description: The mtu of the interface
      type: integer
      format: int64
      minLength: 0
    hostRecords:
      description: List of host records for this interface
      type: array
      items:
        $ref: "#/definitions/HostRecordResponse"
    peerInterfaces:
      description: List of peerInterface records for this interface
      type: array
      items:
        $ref: "#/definitions/PeerInterfaceResponse"
    createdBy:
      description: the user who created the resource
      type: string
      minLength: 1
      maxLength: 255
    modifiedBy:
      description: the user who last modified the resource
      type: string
      minLength: 1
      maxLength: 255
    timeCreated:
      description: |
        The date and time the resource was created, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).
        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time
    timeUpdated:
      description: |
        The date and time the resource was updated, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).
        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time

NetworkInterfaceResponse:
  description: Interface information.
  type: object
  required:
    - id
    - dcmsId
    - portName
    - portType
    - speed
  properties:
    id:
      description: The unique interface id in the database.
      type: string
      minLength: 1
      maxLength: 255
    interfaceName:
      description: The name of the interface
      type: string
      minLength: 1
      maxLength: 128
    description:
      description: The interface description
      type: string
      minLength: 0
      maxLength: 255
    interfaceGroup:
      description: The interface group
      type: string
      minLength: 1
      maxLength: 128
    type:
      description: The type of interface
      type: string
      minLength: 0
      maxLength: 255
    monitoredInterface:
      description: indicator stating if this interface is monitored
      type: boolean
      default: false
    mtu:
      description: The mtu of the interface
      type: integer
      format: int64
      minLength: 0
    intendedState:
      description: The state the interface should be in.
      type: string
      enum: *INTERFACE_LIFE_CYCLE_STATE
      x-obmcs-top-level-enum: '#/definitions/InterfaceLifeCycleState'
    currentState:
      description: The current operational state of the interface.
      type: string
      enum: *INTERFACE_LIFE_CYCLE_STATE
      x-obmcs-top-level-enum: '#/definitions/InterfaceLifeCycleState'
    processState:
      description: The state of current NMC process being performed on the interface.
      type: string
      minLength: 1
      maxLength: 128
    healthStatus:
      description: The health status of interface.
      type: string
      enum: *INTERFACE_HEALTH_STATUS
      x-obmcs-top-level-enum: '#/definitions/InterfaceHealthStatus'
    hostRecords:
      description: List of host records for this interface
      type: array
      items:
        $ref: "#/definitions/HostRecordResponse"
    peerInterfaces:
      description: List of peerInterface records for this interface
      type: array
      items:
        $ref: "#/definitions/PeerInterfaceResponse"
    createdBy:
      type: string
      minLength: 1
      maxLength: 255
    modifiedBy:
      type: string
      minLength: 1
      maxLength: 255
    timeCreated:
      description: |
        The date and time the resource was created, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).
        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time
    timeUpdated:
      description: |
        The date and time the resource was updated, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).
        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time
    healthStatusUpdated:
      description: |
        The date and time the resource health was updated, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).
        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time

NetworkInterfaceResponseCollection:
  description: Collection of NetworkInterfaceResponse.
  type: object
  required:
    - items
  properties:
    items:
      description: List of NetworkInterfaceResponse.
      type: array
      items:
        $ref: '#/definitions/NetworkInterfaceResponse'

NetworkInterfaceStateResponse:
  description: Interface information.
  type: object
  required:
    - id
    - interfaceName
  properties:
    id:
      description: The unique interface id in the database.
      type: string
      minLength: 1
      maxLength: 255
    interfaceName:
      description: The name of the interface
      type: string
      minLength: 1
      maxLength: 128
    deviceName:
      description: The name of the device
      type: string
      minLength: 1
      maxLength: 128
    role:
      description: The role of the device
      type: string
      minLength: 1
      maxLength: 128
    isMonitored:
      description: If the device is monitored state of the remote device
      type: boolean
      default: false
    deviceId:
      description: The Id of the device
      type: string
      minLength: 1
      maxLength: 128
    isMonitoredInterface:
      description: indicator stating if this interface is monitored
      type: boolean
      default: false
    intendedState:
      description: The state the interface should be in.
      type: string
      enum: *INTERFACE_LIFE_CYCLE_STATE
      x-obmcs-top-level-enum: '#/definitions/InterfaceLifeCycleState'
    currentState:
      description: The current operational state of the interface.
      type: string
      enum: *INTERFACE_LIFE_CYCLE_STATE
      x-obmcs-top-level-enum: '#/definitions/InterfaceLifeCycleState'
    processState:
      description: The state of current NMC process being performed on the interface.
      type: string
      minLength: 1
      maxLength: 128
    timeUpdated:
      description: |
        The date and time the resource was updated, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).
        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time

NetworkInterfaceStateResponseCollection:
  description: Collection of NetworkInterfaceStateResponse.
  type: object
  required:
    - items
  properties:
    responseTimestamp:
      description: |
        The date and time the resource was created, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).
        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time
    items:
      description: List of NetworkInterfaceStateResponse.
      type: array
      items:
        $ref: '#/definitions/NetworkInterfaceStateResponse'

NetInterfaceUpdateDetails:
  description: Netinterface update details.
  type: object
  properties:
    currentState:
      description: The current operational state of the interface.
      type: string
      enum: *INTERFACE_LIFE_CYCLE_STATE
      x-obmcs-top-level-enum: '#/definitions/InterfaceLifeCycleState'
    intendedState:
      description: The state the interface should be in.
      type: string
      enum: *INTERFACE_LIFE_CYCLE_STATE
      x-obmcs-top-level-enum: '#/definitions/InterfaceLifeCycleState'
    processState:
      description: The state of current NMC process being performed on the interface.
      type: string
      minLength: 1
      maxLength: 128

# Declare HostRecord Entity Definitions
# =============================================================================
HostRecordResponse:
  description: |
    HostRecord information.
  type: object
  required:
    - id
    - ipAddress
    - prefixLength
  properties:
    id:
      description: The unique host id in the database.
      type: string
      minLength: 1
      maxLength: 255
    ipAddress:
      description: The ip address of the host
      type: string
      minLength: 1
      maxLength: 128
    prefixLength:
      description: The prefix length for the IP
      type: integer
      minimum: 0
    createdBy:
      description: Created by
      type: string
      minLength: 1
      maxLength: 255
    modifiedBy:
      description: Modified by
      type: string
      minLength: 1
      maxLength: 255
    timeCreated:
      description: |
        The date and time the resource was created, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).
        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time
    timeUpdated:
      description: |
        The date and time the resource was updated, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).
        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time

# Declare Device Routing Context Entity Definitions
# =============================================================================
DeviceRoutingContextResponse:
  description: |
    Context information.
  type: object
  required:
    - id
    - name
    - type
    - asn
  properties:
    id:
      description: The unique interface id in the database.
      type: string
      minLength: 1
      maxLength: 255
    name:
      description: The name of the routing context
      type: string
      minLength: 1
      maxLength: 128
    type:
      description: The type of context
      type: string
      minLength: 0
      maxLength: 255
    asn:
      description: The asn for the device
      type: integer
      format: int64
      minLength: 0
    createdBy:
      type: string
      minLength: 1
      maxLength: 255
    modifiedBy:
      type: string
      minLength: 1
      maxLength: 255
    timeCreated:
      description: |
        The date and time the resource was created, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).
        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time
    timeUpdated:
      description: |
        The date and time the resource was updated, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).
        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time

NetworkFabricResponse:
  description: |
    Results of a Fabric by search.
  type: object
  required:
    - id
    - type
    - displayName
    - fabricVersion
    - fabricIndex
    - dcmsId
    - prefix
  properties:
    id:
      description: id of fabric.
      type: string
    type:
      description: type of fabric .
      type: string
    displayName:
      description: displayname of fabric .
      type: string
    fabricVersion:
      description: version of fabric.
      type: string
    fabricIndex:
      description: index of fabric.
      type: integer
    dcmsId:
      description: dcmsid of fabric.
      type: string
    createdBy:
      type: string
      minLength: 1
      maxLength: 255
    modifiedBy:
      type: string
      minLength: 1
      maxLength: 255
    timeCreated:
      description: |
        The date and time the resource was created, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).

        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time
    timeUpdated:
      description: |
        The date and time the resource was updated, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).

        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time
    prefix:
      description: prefix for given fabric
      type: array
      items:
        $ref: "#/definitions/PrefixResponse"

NetworkFabricResponseCollection:
  description: |
    Results of a fabrics.
  type: object
  required:
    - items
  properties:
    items:
      description: List of fabrics .
      type: array
      items:
        $ref: "#/definitions/NetworkFabricResponse"


# Deployment Entity Definitions
# =============================================================================
DeploymentState:
  description: State of deployment.
  type: string
  enum: &DEPLOYMENT_STATE
    - CREATED
    - RUNNING
    - PAUSED
    - COMPLETED
    - CANCELED
    - TERMINATED
    - FAILED_EVALUATION
    - FAILED

DeploymentType:
  description: Type of deployment.
  type: string
  enum: &DEPLOYMENT_TYPE
    - COMMAND
    - CONFIG
    - FIRMWARE
    - CERTIFICATE
    - BYO_CERTIFICATE
    - RSA_KEY
    - FIRMWARE_UPGRADE_INITIATE
    - FILE_COPY

CreateDeploymentDetails:
  discriminator: type
  description: |
    Deployment information for creation.
  type: object
  required:
    - platform
    - role
    - deviceModel
    - type
    - maxConcurrency
    - maxFailures
  properties:
    devices:
      description: The devices this deployment targets
      type: array
      items:
        type: string
    platform:
      description: The platform this deployment targets
      type: string
      minLength: 1
      maxLength: 255
    role:
      description: The role this deployment targets
      type: string
      minLength: 1
      maxLength: 255
    deviceModel:
      description: The device model this deployment targets
      type: string
      minLength: 1
      maxLength: 255
    fabricType:
      description: The fabric type this deployment targets
      type: string
      minLength: 1
      maxLength: 255
    type:
      description: The type of deployment
      type: string
      enum: *DEPLOYMENT_TYPE
      x-obmcs-top-level-enum: '#/definitions/DeploymentType'
    version:
      description: The version of the artifact being deployed
      type: string
      minLength: 1
      maxLength: 255
    maxFailures:
      description: The number of maximum allowable failures in the deployment. Deployment automatically pauses once it reaches this number.
      type: integer
      minimum: 1
    maxTaskRetries:
      description: The maximum number of times a deployment task should be retried
      type: integer
      minimum: 1
    maxConcurrency:
      description: The maximum number of devices to be deployed to concurrently
      type: integer
      minimum: 0
      maximum: 1000
    isChangeManagementIgnored:
      description: Ignore change management windows for the deployment
      type: boolean

CreateCommandDeploymentDetails:
  description: deployment parameters for command deployment type
  allOf:
    - $ref: '#/definitions/CreateDeploymentDetails'
    - type: object
    - discriminator: COMMAND
      properties:
        executionCommand:
          description: For Command type deployments, the command to execute remotely on the devices
          type: array
          items:
            type: string
      required:
        - executionCommand

CreateFirmwareDeploymentDetails:
  description: deployment parameters for firmware deployment type
  allOf:
    - $ref: '#/definitions/CreateDeploymentDetails'
    - type: object
    - discriminator: FIRMWARE

CreateFirmwareUpgradeInitiateDeploymentDetails:
  description: deployment parameters for starting firmware upgrade deployment type
  allOf:
    - $ref: '#/definitions/CreateDeploymentDetails'
    - type: object
    - discriminator: FIRMWARE_UPGRADE_INITIATE

CreateConfigDeploymentDetails:
  description: deployment parameters for config deployment type
  allOf:
    - $ref: '#/definitions/CreateDeploymentDetails'
    - type: object
    - discriminator: CONFIG

CreateCertificateDeploymentDetails:
  description: deployment parameters for certificate deployment type
  allOf:
    - $ref: '#/definitions/CreateDeploymentDetails'
    - type: object
    - discriminator: CERTIFICATE

CreateByoCertificateDeploymentDetails:
  description: deployment parameters for bring your own certificate deployment type
  allOf:
    - $ref: '#/definitions/CreateDeploymentDetails'
    - type: object
    - discriminator: BYO_CERTIFICATE
      properties:
        certificatePem:
          description: certificate in PEM format
          type: string
        certificateChainPem:
          description: certificate authority in PEM format
          type: string
        rsaKeyName:
          description: name of the key on device used to request the certificates
          type: string
      required:
        - certificatePem
        - certificateChainPem
        - rsaKeyName

CreateRsaKeyDeploymentDetails:
  description: deployment parameters for rsa key deployment type
  allOf:
    - $ref: '#/definitions/CreateDeploymentDetails'
    - type: object
    - discriminator: RSA_KEY
      properties:
        rsaKeyName:
          description: name of the key on device used to request the certificates
          type: string
      required:
        - rsaKeyName

CreateFileCopyDeploymentDetails:
  description: deployment parameters for firmware file copy deployment type
  allOf:
    - $ref: '#/definitions/CreateDeploymentDetails'
    - type: object
    - discriminator: FILE_COPY
      properties:
        firmwareUrl:
          description: name of the key on device used to request the certificates
          type: string
      required:
        - firmwareUrl

DeploymentResponse:
  discriminator: type
  description: |
    A Deployment
  type: object
  properties:
    id:
      description: The [UUID](/iaas/Content/General/Concepts/identifiers.htm) of the Deployment.
      type: string
    devices:
      description: The devices this deployment targets
      type: array
      items:
        type: string
    role:
      description: The role this deployment targets
      type: string
    platform:
      description: The platform this deployment targets
      type: string
    deviceModel:
      description: The device model this deployment targets
      type: string
    manifestId:
      description: The fabric manifest associated with this deployment
      type: string
    fabricType:
      description: The fabric type associated with this deployment
      type: string
    type:
      description: The type of deployment
      type: string
      enum: *DEPLOYMENT_TYPE
      x-obmcs-top-level-enum: '#/definitions/DeploymentType'
    version:
      description: The version of the artifact being deployed
      type: string
    maxConcurrency:
      description: The maximum number of devices to be deployed to concurrently
      type: integer
    maxFailures:
      description: The maximum number of failures before automatically pausing the deployment
      type: integer
    maxTaskRetries:
      description: The maximum number of times a deployment task should be retried
      type: integer
    failures:
      description: The number of times this deployment has failed.
      type: integer
    failuresSinceLastStart:
      description: The number of times this deployment has failed since the last time it was started
      type: integer
    state:
      description: The state of deployment
      type: string
      enum: *DEPLOYMENT_STATE
      x-obmcs-top-level-enum: '#/definitions/DeploymentState'
    isChangeManagementIgnored:
      description: Ignore change management windows for the deployment
      type: boolean
    createdBy:
      type: string
    modifiedBy:
      type: string
    timeCreated:
      description: |
        The date and time the resource was created, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).

        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time
    timeUpdated:
      description: |
        The date and time the resource was updated, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).

        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time

CommandDeployment:
  description: deployment response for command deployment type
  allOf:
    - $ref: '#/definitions/DeploymentResponse'
    - type: object
    - discriminator: COMMAND
      properties:
        executionCommand:
          description: For Command type deployments, the command to execute remotely on the devices
          type: array
          items:
            type: string
      required:
        - executionCommand

FirmwareDeployment:
  description: deployment response for firmware deployment type
  allOf:
    - $ref: '#/definitions/DeploymentResponse'
    - type: object
    - discriminator: FIRMWARE

FirmwareUpgradeInitiateDeployment:
  description: deployment response for firmware upgrade initiate deployment type
  allOf:
    - $ref: '#/definitions/DeploymentResponse'
    - type: object
    - discriminator: FIRMWARE_UPGRADE_INITIATE

ConfigDeployment:
  description: deployment response for config deployment type
  allOf:
    - $ref: '#/definitions/DeploymentResponse'
    - type: object
    - discriminator: CONFIG

CertificateDeployment:
  description: deployment response for certificate deployment type
  allOf:
    - $ref: '#/definitions/DeploymentResponse'
    - type: object
    - discriminator: CERTIFICATE

ByoCertificateDeployment:
  description: deployment response for bring your own certificate deployment type
  allOf:
    - $ref: '#/definitions/DeploymentResponse'
    - type: object
    - discriminator: BYO_CERTIFICATE
      properties:
        certificatePem:
          description: certificate in PEM format
          type: string
        certificateChainPem:
          description: certificate authority in PEM format
          type: string
        rsaKeyName:
          description: name of the key on device used to request the certificates
          type: string

RsaKeyDeployment:
  description: deployment response for certificate deployment type
  allOf:
    - $ref: '#/definitions/DeploymentResponse'
    - type: object
    - discriminator: RSA_KEY
      properties:
        rsaKeyName:
          description: name of the key on device used to request the certificates
          type: string

FileCopyDeployment:
  description: deployment response for certificate deployment type
  allOf:
    - $ref: '#/definitions/DeploymentResponse'
    - type: object
    - discriminator: FILE_COPY
      properties:
        firmwareUrl:
          description: name of the key on device used to request the certificates
          type: string

DeploymentCollection:
  description: A list of deployments
  type: object
  required:
    - items
  properties:
    items:
      description: List of deployments
      type: array
      items:
        $ref: "#/definitions/DeploymentResponse"

# DeploymentTask Entity Definitions
# =============================================================================
DeploymentTaskState:
  description: State of deploymentTask.
  type: string
  enum: &DEPLOYMENT_TASK_STATE
    - PRE_VALIDATION
    - POST_VALIDATION
    - EXECUTION_COMPLETE
    - ON_HOLD
    - PENDING
    - RUNNING
    - COMPLETED
    - FAILED
    - FAILED_PENDING_RETRY
    - FAILED_CREATE
    - FAILED_PRE_VALIDATION
    - FAILED_POST_VALIDATION
    - CANCELED

DeploymentTaskSummary:
  description: DeploymentTask entity summary
  type: object
  properties:
    id:
      description: UUID of the deployment task entity.
      type: string
    comments:
      description: Comments related to the task
      type: string
      minLength: 1
      maxLength: 255
    deploymentId:
      description: The UUID of the Deployment this task belongs to
      type: string
      minLength: 1
      maxLength: 255
    deviceId:
      description: The id of the device (ocid1.device..)
      type: string
      minLength: 1
      maxLength: 128
    attempts:
      description: The number of deployment attempts for this task
      type: integer
      minimum: 0
    state:
      description: The state of the task, corresponding to DeploymentTaskState
      type: string
      enum: *DEPLOYMENT_TASK_STATE
      x-obmcs-top-level-enum: '#/definitions/DeploymentTaskState'

DeploymentTaskCollection:
  description: Collection of DeploymentTask entities.
  type: object
  required:
    - items
  properties:
    items:
      description: List of DeploymentTask entities.
      type: array
      items:
        $ref: '#/definitions/DeploymentTaskSummary'

# Config Metadata Definitions
# =============================================================================
ConfigsMetadataResponse:
  description: |
    A ConfigsMetadata
  type: object
  required:
    - id
    - name
  properties:
    id:
      description: The [UUID](/iaas/Content/General/Concepts/identifiers.htm) of the ConfigsMetadata.
      type: string
      minLength: 1
      maxLength: 128
    entityId:
      description: The [UUID](/iaas/Content/General/Concepts/identifiers.htm) of the Entity.
      type: string
      minLength: 1
      maxLength: 128
    manifestId:
      description: The [UUID](/iaas/Content/General/Concepts/identifiers.htm) of the manifest.
      type: string
      minLength: 1
      maxLength: 128
    configType:
      description: config type
      type: string
      minLength: 1
      maxLength: 128
    deviceName:
      description: device name associated with the given config
      type: string
      minLength: 1
      maxLength: 128
    deviceRole:
      description: device role associated with the given config
      type: string
      minLength: 1
      maxLength: 128
    configStatus:
      description: config status running or deployed
      type: string
      minLength: 1
      maxLength: 128
    fileName:
      description: file name
      type: string
      minLength: 1
      maxLength: 128
    sha256:
      description: sha256 of the file.
      type: string
      minLength: 1
      maxLength: 128
    objectUrl:
      description: objectUrl in the object store.
      type: string
      minLength: 1
      maxLength: 128
    contentMd5:
      description: md5 of the file content.
      type: string
      minLength: 1
      maxLength: 128
    createdBy:
      description: created by
      type: string
      minLength: 1
      maxLength: 128
    modifiedBy:
      description: modified by
      type: string
      minLength: 1
      maxLength: 128
    timeCreated:
      description: |
        The date and time the resource was created, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).

        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time
    timeUpdated:
      description: |
        The date and time the resource was updated, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).

        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time

ConfigsMetadataResponseCollection:
  description: |
    List of ConfigsMetadata.
  type: object
  required:
    - items
  properties:
    items:
      description: List of ConfigsMetadata .
      type: array
      items:
        $ref: "#/definitions/ConfigsMetadataResponse"


ManifestDeploymentType:
  description: Type of deployment supported by fabric manifests.
  type: string
  enum: &MANIFEST_DEPLOYMENT_TYPE
    - CONFIG
    - FIRMWARE

# Fabric Manifest Entity Definitions
# =============================================================================
DeploymentManifestState:
  description: State of the deployment manifest.
  type: string
  enum: &DEPLOYMENT_MANIFEST_STATE
    - CREATING
    - CREATED
    - FAILED
    - DEPLOYING
    - DEPLOYED

# Fabric Manifest Entity Definitions
# =============================================================================
FabricManifestState:
  description: State of the fabric manifest.
  type: string
  enum: &FABRIC_MANIFEST_STATE
    - CREATED
    - RENDERING
    - QUEUED
    - FAILED_RENDERING
    - APPLYING
    - PAUSING
    - PAUSED
    - DEPLOYED
    - CANCELING
    - CANCELED

DeviceManifest:
  description: Device manifest for a fabric
  type: object
  required:
    - platform
    - role
    - configTemplatesArtifact
    - deviceModel
  properties:
    platform:
      description: The platform of the deployment (i.e. EOS)
      type: string
      minLength: 1
      maxLength: 128
    role:
      description: The role this manifest targets
      type: string
      minLength: 1
      maxLength: 128
    configTemplatesArtifact:
      description: Templates artifact name in object storage to render the config template from
      type: string
      minLength: 1
      maxLength: 128
    deviceModel:
      description: Device model to be targeted by this manifest
      type: string
      minLength: 1
      maxLength: 128

ConfigDeviceManifest:
  description: Device manifest for a deployment
  type: object
  properties:
    platform:
      description: The platform of the deployment (i.e. EOS)
      type: string
      minLength: 1
      maxLength: 128
    role:
      description: The role this manifest targets
      type: string
      minLength: 1
      maxLength: 128
    configTemplatesArtifact:
      description: Templates artifact name in object storage to render the config template from
      type: string
      minLength: 1
      maxLength: 128
    deviceModel:
      description: Device model to be targeted by this manifest
      type: string
      minLength: 1
      maxLength: 128
    deviceNames:
      type: array
      items:
        type: string
      description: Names of the devices you want to include in this manifest
    site:
      description: site of the device
      type: string
      minLength: 1
      maxLength: 128
    rackPosition:
      description: rack number the devices are in
      type: string
      minLength: 1
      maxLength: 128

FirmwareDeviceManifest:
  description: Device manifest for a fabric
  type: object
  properties:
    deviceNames:
      type: array
      items:
        type: string
      description: Names of the devices you want to include in this manifest
    firmwareMappingId:
      description: Firmware mapping applicable to devices of the same role, platform, and model
      type: string
      minLength: 1
      maxLength: 128

CreateDefaultManifestDetails:
  description: |
    Information for setting the default config templates artifact version.
  type: object
  required:
    - defaultTemplatesArtifact
  properties:
    overridePreviousManifestValues:
      description: Default false, values from previous manifest will be copied to new manifest if user doesn't specify them. Otherwise, user's request will override values in previous manifest.
      type: boolean
      default: false
    defaultTemplatesArtifact:
      description: The default templates artifact to use for the manifest
      type: string
      minLength: 1
      maxLength: 255
    manifestOverrides:
      description: Manifests with config override values for select devices.
      type: array
      items:
        $ref: "#/definitions/ConfigDeviceManifest"

CreateDeploymentManifestDetails:
  description: |
    Deployment manifest information for creation.
  type: object
  discriminator: manifestDeploymentType
  required:
    - manifestDeploymentType
  properties:
    isChangeManagementIgnored:
      description: Ignore change management windows for the deployment
      type: boolean
      default: false
    manifestDeploymentType:
      description: If not specified, no deployment will be created
      type: string
      enum: *MANIFEST_DEPLOYMENT_TYPE
      x-obmcs-top-level-enum: '#/definitions/ManifestDeploymentType'
    fabricInstanceId:
      description: The fabric type for devices this manifest (optional)
      type: string
      minLength: 1
      maxLength: 255

CreateConfigDeploymentManifestDetails:
  description: |
    Manifest for config deployments
  allOf:
    - $ref: '#/definitions/CreateDeploymentManifestDetails'
    - type: object
    - discriminator: CONFIG
      required:
        - manifests
      properties:
        manifests:
          description: Device manifests for this deployment manifest
          type: array
          items:
            $ref: "#/definitions/ConfigDeviceManifest"

CreateFirmwareDeploymentManifestDetails:
  description: |
    Manifest for firmware deployments
  allOf:
    - $ref: '#/definitions/CreateDeploymentManifestDetails'
    - type: object
    - discriminator: FIRMWARE
      required:
        - manifests
      properties:
        manifests:
          description: Device manifests for this deployment manifest
          type: array
          items:
            $ref: "#/definitions/FirmwareDeviceManifest"

DefaultManifest:
  description: |
    Information for setting the default config templates artifact version.
  type: object
  required:
    - defaultTemplatesArtifact
  properties:
    overridePreviousManifestValues:
      description: Default false, values from previous manifest will be copied to new manifest if user doesn't specify them. Otherwise, user's request will override values in previous manifest.
      type: boolean
      default: false
    defaultTemplatesArtifact:
      description: The default templates artifact to use for the manifest
      type: string
      minLength: 1
      maxLength: 255
    manifestOverrides:
      description: Manifests with config override values for select devices.
      type: array
      items:
        $ref: "#/definitions/ConfigDeviceManifest"

DeploymentManifest:
  description: |
    A Deployment Manifest
  discriminator: manifestDeploymentType
  type: object
  required:
    - id
    - manifestDeploymentType
    - state
  properties:
    id:
      description: The [UUID](/iaas/Content/General/Concepts/identifiers.htm) of the Deployment.
      type: string
    manifestDeploymentType:
      description: If not specified, no deployment will be created
      type: string
      enum: *MANIFEST_DEPLOYMENT_TYPE
      x-obmcs-top-level-enum: '#/definitions/ManifestDeploymentType'
    manifestVersion:
      description: The version for this fabric manifest
      type: string
      minLength: 1
      maxLength: 255
    state:
      description: The state of the fabric manifest
      type: string
      enum: *DEPLOYMENT_MANIFEST_STATE
      x-obmcs-top-level-enum: '#/definitions/DeploymentManifestState'
    isChangeManagementIgnored:
      description: Ignore change management windows for the deployment
      type: boolean
    fabricInstanceId:
      description: The fabric instance for this manifest (optional)
      type: string
      minLength: 1
      maxLength: 255
    createdBy:
      description: The user who created the fabric manifest
      type: string
      minLength: 1
      maxLength: 255

FirmwareDeploymentManifest:
  description: deployment response for firmware deployment type
  allOf:
    - $ref: '#/definitions/DeploymentManifest'
    - type: object
    - discriminator: FIRMWARE
      properties:
        manifests:
          description: Device manifests for this deployment manifest
          type: array
          items:
            $ref: "#/definitions/FirmwareDeviceManifest"
      required:
        - manifests

ConfigDeploymentManifest:
  description: deployment response for config deployment type
  allOf:
    - $ref: '#/definitions/DeploymentManifest'
    - type: object
    - discriminator: CONFIG
      properties:
        manifests:
          description: Device manifests for this deployment manifest
          type: array
          items:
            $ref: "#/definitions/ConfigDeviceManifest"
      required:
        - manifests

DeploymentManifestSummary:
  description: |
    A Deployment Manifest
  type: object
  discriminator: manifestDeploymentType
  required:
    - id
    - state
  properties:
    manifestDeploymentType:
      description: If not specified, no deployment will be created
      type: string
      enum: *MANIFEST_DEPLOYMENT_TYPE
      x-obmcs-top-level-enum: '#/definitions/ManifestDeploymentType'
    id:
      description: The [UUID](/iaas/Content/General/Concepts/identifiers.htm) of the Deployment.
      type: string
    isActive:
      description: Whether or not this manifest is active for ztp bootstrapping
      type: boolean
    manifestVersion:
      description: The version for this fabric manifest
      type: string
      minLength: 1
      maxLength: 255
    state:
      description: The state of the fabric manifest
      type: string
      enum: *DEPLOYMENT_MANIFEST_STATE
      x-obmcs-top-level-enum: '#/definitions/DeploymentManifestState'
    isChangeManagementIgnored:
      description: Ignore change management windows for the deployment
      type: boolean
    fabricInstanceId:
      description: The fabric instance for this manifest (optional)
      type: string
      minLength: 1
      maxLength: 255
    createdBy:
      description: The user who created the fabric manifest
      type: string
      minLength: 1
      maxLength: 255

ConfigDeploymentManifestSummary:
  description: deployment response for config deployment type
  allOf:
    - $ref: '#/definitions/DeploymentManifestSummary'
    - type: object
    - discriminator: CONFIG
      properties:
        manifests:
          description: Device manifests for this deployment manifest
          type: array
          items:
            $ref: "#/definitions/ConfigDeviceManifest"
      required:
        - manifests

FirmwareDeploymentManifestSummary:
  description: deployment response for config deployment type
  allOf:
    - $ref: '#/definitions/DeploymentManifestSummary'
    - type: object
    - discriminator: FIRMWARE
      properties:
        manifests:
          description: Device manifests for this deployment manifest
          type: array
          items:
            $ref: "#/definitions/FirmwareDeviceManifest"
      required:
        - manifests

CreateFabricManifestDetails:
  description: |
    Fabric manifest information for creation.
  type: object
  required:
    - manifests
    - isChangeManagementIgnored
    - defaultTemplatesArtifact
  properties:
    manifests:
      description: Device manifests for this fabric manifest
      type: array
      items:
        $ref: "#/definitions/DeviceManifest"
    manifestVersion:
      description: The version for this fabric manifest
      type: string
      minLength: 1
      maxLength: 255
    isChangeManagementIgnored:
      description: Ignore change management windows for the deployment
      type: boolean
    isActive:
      description: If 'active', this manifest will be used by devices during Ztp bootstrap
      type: boolean
      default: false
    defaultTemplatesArtifact:
      description: The default templates artifact to use for the manifest
      type: string
      minLength: 1
      maxLength: 255
    fabricType:
      description: The fabric type for devices this manifest (optional)
      type: string
      minLength: 1
      maxLength: 255

CreateFirmwareUpgradeDetails:
  description: |
    Firmware upgrade information for creation.
  type: object
  required:
    - isForceUpgrade
  properties:
    isForceUpgrade:
      description: If true, the firmware upgrade will be forced even if the device is not in the expected state
      type: boolean
      default: false

FirmwareUpgradeResponse:
  description: Interface information.
  type: object
  required:
    - id
  properties:
    id:
      description: The unique device id in the database.
      type: string
      minLength: 1
      maxLength: 255
    deviceName:
      description: The name of the interface
      type: string
      minLength: 1
      maxLength: 128
    workRequestId:
      type: string
      description: The [OCID](/iaas/Content/General/Concepts/identifiers.htm) of the work request.
    isForceUpgrade:
      description: If true, the firmware upgrade will be forced even if the device is not in the expected state
      type: boolean
    createdBy:
      description: the user who created the resource
      type: string
      minLength: 1
      maxLength: 255
    modifiedBy:
      description: the user who last modified the resource
      type: string
      minLength: 1
      maxLength: 255
    timeCreated:
      description: |
        The date and time the resource was created, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).
        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time
    timeUpdated:
      description: |
        The date and time the resource was updated, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).
        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time

FabricManifestResponse:
  description: |
    A Fabric Manifest
  type: object
  required:
    - id
    - manifests
    - state
    - isActive
  properties:
    id:
      description: The [UUID](/iaas/Content/General/Concepts/identifiers.htm) of the Deployment.
      type: string
    manifests:
      description: Device manifests for this fabric manifest
      type: array
      items:
        $ref: "#/definitions/DeviceManifest"
    isActive:
      description: Whether or not this manifest is active for ztp bootstrapping
      type: boolean
    manifestVersion:
      description: The version for this fabric manifest
      type: string
      minLength: 1
      maxLength: 255
    state:
      description: The state of the fabric manifest
      type: string
      enum: *FABRIC_MANIFEST_STATE
      x-obmcs-top-level-enum: '#/definitions/FabricManifestState'
    defaultTemplatesArtifact:
      description: The default templates artifact to use for the manifest
      type: string
      minLength: 1
      maxLength: 255
    isChangeManagementIgnored:
      description: Ignore change management windows for the deployment
      type: boolean
    fabricRenderingWorkRequestId:
      type: string
      description: The RmcId of the work request for fabric config rendering
    fabricType:
      description: The fabric type for this manifest (optional)
      type: string
      minLength: 1
      maxLength: 255
    createdBy:
      description: The user who created the fabric manifest
      type: string
      minLength: 1
      maxLength: 255

# Network Device State Entity
# =====================================================================================
DeviceStateLifeCycleState:
  description: The actual operational state of the device.
  type: string
  enum: &LIFE_CYCLE_STATE
    - NEW
    - BOOTSTRAPPED
    - VALIDATION_READY
    - READY
    - IN_SERVICE
    - MAINTENANCE
    - DECOMMISSIONED

DeviceStateHealthStatus:
  description: The health status of device.
  type: string
  enum: &HEALTH_STATUS
    - UN_REACHABLE
    - UN_HEALTHY
    - HEALTHY

DeviceStateAutomationState:
  description: The automation state of device.
  type: string
  enum: &AUTOMATION_STATE
    - ENABLED
    - DISABLED

NetworkDeviceIntendedStateResponse:
  description: |
    device state information.
  type: object
  required:
    - id
    - deviceName
  properties:
    id:
      description: The device state id in the database.
      type: string
      minLength: 1
      maxLength: 255
    deviceName:
      description: The name of the device
      type: string
      minLength: 1
      maxLength: 128
    role:
      description: The role of the device
      type: string
      minLength: 1
      maxLength: 128
    isMonitored:
      description: If the device is monitored state of the remote device
      type: boolean
      minLength: 1
      maxLength: 128
    dcmsDeviceId:
      description: The unique device id of the device in the dcms database
      type: string
      minLength: 1
      maxLength: 255
    deviceId:
      description: The Id of the device
      type: string
      minLength: 1
      maxLength: 128
    currentState:
      description: The actual operational state of the device.
      type: string
      enum: *LIFE_CYCLE_STATE
      x-obmcs-top-level-enum: '#/definitions/DeviceStateLifeCycleState'
    intendedState:
      description: The state the device should be in.
      type: string
      enum: *LIFE_CYCLE_STATE
      x-obmcs-top-level-enum: '#/definitions/DeviceStateLifeCycleState'
    processState:
      description: The state of current NMC process being performed on the device.
      type: string
      minLength: 1
      maxLength: 128
    processDetails:
      $ref: "#/definitions/ProcessDetails"
    automationState:
      description: The automation state of device.
      type: string
      enum: *AUTOMATION_STATE
      x-obmcs-top-level-enum: '#/definitions/DeviceStateAutomationState'
    timeUpdated:
      description: |
        The date and time the resource was updated, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).
        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time

NetworkDeviceStateResponse:
  description: |
    device state information.
  type: object
  required:
    - id
    - deviceName
  properties:
    id:
      description: The device state id in the database.
      type: string
      minLength: 1
      maxLength: 255
    deviceName:
      description: The name of the device
      type: string
      minLength: 1
      maxLength: 128
    deviceId:
      description: The Id of the device
      type: string
      minLength: 1
      maxLength: 128
    currentState:
      description: The actual operational state of the device.
      type: string
      enum: *LIFE_CYCLE_STATE
      x-obmcs-top-level-enum: '#/definitions/DeviceStateLifeCycleState'
    intendedState:
      description: The state the device should be in.
      type: string
      enum: *LIFE_CYCLE_STATE
      x-obmcs-top-level-enum: '#/definitions/DeviceStateLifeCycleState'
    processState:
      description: The state of current NMC process being performed on the device.
      type: string
      minLength: 1
      maxLength: 128
    processDetails:
      $ref: "#/definitions/ProcessDetails"
    healthStatus:
      description: The health status of device.
      type: string
      enum: *HEALTH_STATUS
      x-obmcs-top-level-enum: '#/definitions/DeviceStateHealthStatus'
    automationState:
      description: The automation state of device.
      type: string
      enum: *AUTOMATION_STATE
      x-obmcs-top-level-enum: '#/definitions/DeviceStateAutomationState'
    errorMessage:
      description: Error message if there was any during LSM process.
      type: string
    configLatest:
      description: The latest config information in RmcID
      type: string
      minLength: 0
      maxLength: 255
    configDeployed:
      description: The deployed config information in RmcID
      type: string
      minLength: 0
      maxLength: 255
    firmwareLatest:
      description: The latest firmware information in RmcID
      type: string
      minLength: 0
      maxLength: 255
    firmwareDeployed:
      description: The deployed firmware information in RmcID
      type: string
      minLength: 0
      maxLength: 255
    createdBy:
      type: string
      minLength: 1
      maxLength: 255
    modifiedBy:
      type: string
      minLength: 1
      maxLength: 255
    timeCreated:
      description: |
        The date and time the resource was created, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).
        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time
    timeUpdated:
      description: |
        The date and time the resource was updated, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).
        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time
    healthStatusUpdated:
      description: |
        The date and time the resource health was updated, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).
        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time

NetworkDeviceStateResponseCollection:
  description: Collection of NetworkDeviceStateResponses.
  type: object
  required:
    - items
  properties:
    responseTimestamp:
      description: |
        The date and time the resource was created, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).
        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time
    items:
      description: List of NetworkDeviceIntendedStateResponse.
      type: array
      items:
        $ref: '#/definitions/NetworkDeviceIntendedStateResponse'

DeploymentManifestCollection:
  description: Collection of NetworkDeploymentManifest.
  type: object
  required:
    - items
  properties:
    items:
      description: List of NetworkDeploymentManifest.
      type: array
      items:
        $ref: '#/definitions/DeploymentManifest'

StateAttributePair:
  description: |
    attribute values for state attribute update.
  type: object
  required:
    - oldValue
    - newValue
  properties:
    oldValue:
      type: string
    newValue:
      type: string

ztpAction:
  description: An object with name and par link which ZTP reads and performs custom actions.
  type: object
  required:
    - action
    - alwaysExecute
    - attributes
  properties:
    action:
      description: Name of the action.
      type: string
      minLength: 1
      maxLength: 255
    alwaysExecute:
      description: ensures we always run the script
      type: boolean
      default: true
    attributes:
      description: Details of the action
      type: object
      required:
        - name
        - url
        - version
      properties:
        name:
          description: Name of the action.
          type: string
          minLength: 1
          maxLength: 255
        url:
          description: Name of the action.
          type: string
          minLength: 1
          maxLength: 255
        version:
          description: Name of the action.
          type: string
          minLength: 1
          maxLength: 255

LatestConfigStateUrls:
  description: The Firmware and config PAR files details.
  type: object
  required:
    - configLatestUrl
    - firmwareLatestUrl
    - firmwareVersion
  properties:
    configLatestUrl:
      description: The PAR url of the config file in object storage.
      type: string
      minLength: 1
      maxLength: 255
    firmwareLatestUrl:
      description: The PAR url of the firmware file in object storage.
      type: string
      minLength: 1
      maxLength: 255
    firmwareVersion:
      description: Firmware version
      type: string
      minLength: 1
      maxLength: 255
    actions:
      description: A list of custom action details for ZTP to consume.
      type: array
      items:
        $ref: "#/definitions/ztpAction"


NetworkDeviceSlimResponse:
  description: |
    A Fabric Manifest
  type: object
  required:
    - id
    - deviceName
    - configPlatform
    - role
    - ipAddress
    - state
  properties:
    id:
      description: The [UUID](/iaas/Content/General/Concepts/identifiers.htm) of the Deployment.
      type: string
    deviceName:
      description: The name of the device
      type: string
      minLength: 1
      maxLength: 128
    configPlatform:
      description: The supported config platform for the device
      type: string
      minLength: 0
      maxLength: 255
    role:
      description: The device role
      type: string
      minLength: 1
      maxLength: 128
    ipAddress:
      description: The device ip address
      type: string
      minLength: 0
      maxLength: 255
    state:
      $ref: "#/definitions/LatestConfigStateUrls"

# Device Lifecycle state Definitions for device response
# =============================================================================

DeviceLifeCycleStateDetails:
  description: Device lifecycle state information
  type: object
  properties:
    currentState:
      description: The actual operational state of the device.
      type: string
      enum: *LIFE_CYCLE_STATE
      x-obmcs-top-level-enum: '#/definitions/DeviceStateLifeCycleState'
    intendedState:
      description: The state the device should be in.
      type: string
      enum: *LIFE_CYCLE_STATE
      x-obmcs-top-level-enum: '#/definitions/DeviceStateLifeCycleState'
    processState:
      description: The state of current NMC process being performed on the device.
      type: string
      minLength: 1
      maxLength: 128
    timeUpdated:
      description: |
        The date and time the resource was updated, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).
        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time


ProcessDetails:
  description: Details of NMC process being performed on the device.
  type: object
  properties:
    processorName:
      description: name of processor working on device
      type: string
      minLength: 1
      maxLength: 255
    message:
      description: message from processor
      type: string
      minLength: 1
      maxLength: 255
    workRequestId:
      description: work request id from processor
      type: string
      minLength: 1
      maxLength: 255


NetworkRegionCollection:
  description: |
    List of Regions present in this NMC application.
  type: object
  required:
    - items
  properties:
    items:
      description: List of Regions.
      type: array
      items:
        $ref: "#/definitions/NetworkRegionResponse"

NetworkRegionResponse:
  description: |
    A Region
  type: object
  required:
    - id
    - airportCode
  properties:
    id:
      description: The networkModelId [UUID] of the region.
      type: string
      minLength: 1
      maxLength: 255
    airportCode:
      description: The airportCode of the region.
      type: string
      minLength: 1
      maxLength: 255
    realm:
      description: The realm of which the region is in
      type: string
      minLength: 1
      maxLength: 255
    market:
      description: The market of which the region is in
      type: string
      minLength: 1
      maxLength: 255
    dhcpConfigId:
      description: The RmcId to configure which dhcpConfig to use in the region
      type: string
      minLength: 1
      maxLength: 255
    createdBy:
      description: The entity who created this region
      type: string
      minLength: 1
      maxLength: 255
    modifiedBy:
      description: The last entity to modify this region
      type: string
      minLength: 1
      maxLength: 255
    timeCreated:
      description: |
        The date and time the resource was created, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).

        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time
    timeUpdated:
      description: |
        The date and time the resource was updated, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).

        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time
    isActive:
      type: boolean
      description: |
        Is this the active region

# Network Device firmware info
# =====================================================================================
NetworkDeviceFirmwareResponseCollection:
  description: |
    Collection of devices with firmware information.
  type: object
  required:
    - items
  properties:
    items:
      description: List of devices with firmware details .
      type: array
      items:
        $ref: "#/definitions/NetworkDeviceFirmwareResponse"

NetworkDeviceFirmwareResponse:
  description: |
    A NetworkDevice with firmware details
  type: object
  required:
    - id
    - deviceModel
    - firmwareFileName
    - firmwareVersion
    - firmwareFileSize
    - md5Checksum
    - firmwareUrl
  properties:
    id:
      description: The unique device id in the database.
      type: string
      minLength: 1
      maxLength: 255
    dcmsDeviceId:
      description: The unique device id of the device in the dcms database
      type: string
      minLength: 1
      maxLength: 255
    deviceName:
      description: The name of the device
      type: string
      minLength: 1
      maxLength: 128
    deviceModel:
      description: Device model to be targeted by this manifest
      type: string
      minLength: 1
      maxLength: 128
    configPlatform:
      description: The supported config platform for the device
      type: string
      minLength: 0
      maxLength: 255
    deviceRole:
      description: The device role
      type: string
      minLength: 1
      maxLength: 128
    firmwareFileName:
      description: The name of the firmware file
      type: string
      minLength: 1
      maxLength: 255
    firmwareVersion:
      description: The firmware version
      type: string
      minLength: 1
      maxLength: 255
    firmwareFileSize:
      description: The size of the firmware file
      type: integer
      minimum: 1
    md5Checksum:
      description: The checksum of the firmware file
      type: string
      minLength: 1
      maxLength: 255
    firmwareUrl:
      description: The PAR url of the firmware file in object storage
      type: string
      minLength: 1
      maxLength: 1024

PeerInterfaceResponse:
  type: object
  description: Peer Interface information about an interface of a device.
  properties:
    interfaceName:
      type: string
      description: Name of the interface.
    deviceName:
      description: The name of the device
      type: string
      minLength: 1
      maxLength: 128
    dcmsDeviceId:
      description: The unique device id of the device in the dcms database
      type: string
      minLength: 1
      maxLength: 255


# Cos Profile Details
# =============================================================================
CreateCosProfileDetails:
  description: Configuration details for the COS queues.
  type: object
  required:
    - profileName
    - queues
  properties:
    profileName:
      type: string
      description: Name of the profile.
    queues:
      $ref: "#/definitions/CosQueues"
    dscpClassifiers:
      type: array
      description: The DSCP Classifiers associated with the profile.
      items:
        $ref: "#/definitions/DscpClassifier"

NetworkModelDeviceCosDetails:
  description: Configuration details for the COS queues.
  type: object
  required:
    - profileName
    - queues
  properties:
    profileName:
      type: string
      description: Name of the profile.
    deviceNames:
      type: array
      items:
        type: string
      description: Names of the devices associated with the profile name.
    workRequestId:
      type: string
      description: The [OCID](/iaas/Content/General/Concepts/identifiers.htm) of the work request.
    queues:
      $ref: "#/definitions/CosQueues"
    associatedInterfaces:
      type: array
      items:
        $ref: '#/definitions/InterfaceCosDetail'
      description: COS profile associated with each interface

UpdateInterfaceCosProfileDetails:
  description: Interface named which the cos profile will apply to.
  type: object
  required:
    - associatedInterfaces
  properties:
    associatedInterfaces:
      type: array
      description: Name of the interface
      items:
        $ref: '#/definitions/UpdateInterfaceCosProfileDetail'

UpdateInterfaceCosProfileDetail:
  type: object
  description: DSCP Classifier (Differentiated Services Code Point) Mappings with the forwarding class.
  properties:
    interfaceName:
      type: string
      description: Name of the interface.

InterfaceCosDetails:
  description: Configuration details for the COS queues that apply to interfaces.
  type: object
  required:
    - deviceName
  properties:
    deviceProfileName:
      type: string
      description: Name of the profile.
    deviceName:
      type: string
      description: Names of the devices associated with the profile name.
    associatedInterfaces:
      type: array
      items:
        $ref: '#/definitions/InterfaceCosDetail'
      description: COS profile associated with each interface

CosQueues:
  type: object
  description: A mapping of forwarding queue names to their configuration details.
  properties:
    trafficClassDetails:
      type: array
      items:
        $ref: '#/definitions/TrafficClassDetail'
      description: A map where the key is the queue name and the value is the queue configuration details.

TrafficClassDetail:
  type: object
  description: Details about an individual forwarding class.
  required:
    - trafficClassName
    - trafficClassNumber
    - queueNumber
    - dscpValue
  properties:
    trafficClassName:
      type: string
      description: Name for the traffic class.
    trafficClassNumber:
      type: integer
      description: Traffic class number.
    queueNumber:
      type: integer
      description: Generally defaults to traffic class number if not provided (Also the priority for cumulus).
    dscpValue:
      type: integer
      description: The dscp value for this traffic class.
    config:
      $ref: '#/definitions/CongestionControl'

CongestionControl:
  type: object
  description: DSCP Classifier (Differentiated Services Code Point).
  properties:
    isPriority:
      type: boolean
      description: Whether the queue has priority.
    bandwidth:
      type: integer
      format: int32
      description: Bandwidth allocation (e.g., At this point we always use this as percentage).
    ecnEnabled:
      type: boolean
      description: If ecn mode is enabled
    minimumThreshold:
      type: integer
      format: int32
      minLength: 0
      description: minimum threshold
    maximumThreshold:
      type: integer
      format: int32
      minLength: 0
      description: maximum threshold
    probability:
      type: integer
      format: int32
      minLength: 0
      description: probability
    shapeRate:
      type: integer
      format: int32
      minLength: 0
      description: Shape rate (e.g., this is absolute value currently).

DscpClassifier:
  type: object
  description: DSCP Classifier (Differentiated Services Code Point).
  properties:
    classifierName:
      type: string
      description: Name of the classifier.
    dscpMappings:
      type: array
      items:
        $ref: '#/definitions/DscpMapping'
      description: A mapping of dscp values to the forwarding class.

DscpMapping:
  type: object
  description: DSCP Classifier (Differentiated Services Code Point) Mappings with the forwarding class.
  properties:
    dscpValues:
      type: array
      items:
        type: integer
      description: DSCP (Differentiated Services Code Point) values associated with the forwarding class.
    forwardingClassName:
      type: string
      description: Forwarding class name.

InterfaceCosDetail:
  type: object
  description: DSCP Classifier (Differentiated Services Code Point) Mappings with the forwarding class.
  properties:
    interfaceName:
      type: string
      description: Name of the interface.
    classOfServiceProfile:
      type: string
      description: Name of the class of service profile applied to the interface.
    trafficClassDetails:
      type: array
      items:
        $ref: '#/definitions/TrafficClassDetail'
      description: A map where the key is the queue name and the value is the queue configuration details.

ExternalInterface:
  description: Interface information.
  type: object
  required:
    - address
  properties:
    interfaceName:
      description: The name of the interface
      type: string
      minLength: 1
      maxLength: 128
    description:
      description: The interface description
      type: string
      minLength: 0
      maxLength: 255
    type:
      description: The type of interface
      type: string
      minLength: 0
      maxLength: 255
    mtu:
      description: The mtu of the interface
      type: integer
      minLength: 0
    startVlan:
      description: The startVlan of the interface
      type: integer
      minLength: 0
    address:
      description: address for the interface
      type: string
      minLength: 1
      maxLength: 255
    portMembers:
      description: ports on device
      type: array
      items:
        type: string
    deviceName:
      description: device name
      type: string
      minLength: 1
      maxLength: 255

CreateNetworkModelIngestionCompleteDetails:
  description: The data needed to mark the ingestion of the network model from DCMS logical connections as complete
  type: object
  required:
    - isCompletedSuccessfully
  properties:
    isCompletedSuccessfully:
      description: whether or not the ingestion completed successfully, default to true
      type: boolean
      default: true

CreateExternalPeerInterfaceDetails:
  description: |
    The data needed to configure an external peering connection with interfaces and routing contexts
  type: object
  required:
    - sourceInterface
    - destinationInterface
    - destinationDeviceRoutingContext
    - destinationDeviceAsn
  properties:
    sourceInterface:
      $ref: '#/definitions/ExternalInterface'
    destinationInterface:
      $ref: '#/definitions/ExternalInterface'
    destinationDeviceAsn:
      description: asn for external device
      type: integer
      format: int64
    destinationDeviceRoutingContext:
      description: routing context for external peer
      type: string
      minLength: 1
      maxLength: 100
# =============================================================================
# Net_VIPS definitions
# =============================================================================

NetworkModelVip:
  description: |
    Vip information.
  type: object
  required:
    - id
    - name
    - ipAddress
  properties:
    id:
      description: The unique vip id in the database.
      type: string
      minLength: 1
      maxLength: 255
    name:
      description: The name of the Vip
      type: string
      minLength: 1
      maxLength: 128
    ipAddress:
      description: The ipAddress for the vip
      type: string
      minLength: 0
      maxLength: 255
    createdBy:
      description: created by
      type: string
      minLength: 1
      maxLength: 255
    modifiedBy:
      description: updated by
      type: string
      minLength: 1
      maxLength: 255
    timeCreated:
      description: |
        The date and time the resource was created, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).
        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time
    timeUpdated:
      description: |
        The date and time the resource was updated, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).
        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time

RegionNetworkModelVipSummary:
  description: Collection of Network vips.
  type: object
  required:
    - items
  properties:
    items:
      description: List of network vips.
      type: array
      items:
        $ref: '#/definitions/NetworkModelVip'

CreateUpdateVipParams:
  description: |
    Params to create or update network vips.
  type: object
  properties:
    name:
      description: vip name
      type: string
      minLength: 1
      maxLength: 255
    ipAddress:
      description: Ip address for the vip
      type: string
      minLength: 1
      maxLength: 255

CreateVipDetails:
  description: |
    List of vips to create network vip.
  type: object
  required:
    - items
  properties:
    items:
      description: List of network vip params.
      type: array
      minItems: 1
      items:
        $ref: '#/definitions/CreateUpdateVipParams'

UpdateVipDetails:
  description: |
    List of vips to Update network vip.
  type: object
  required:
    - items
  properties:
    items:
      description: List of network vip params.
      type: array
      minItems: 1
      items:
        $ref: '#/definitions/CreateUpdateVipParams'

CreateUpdateNetworkModelVipResponse:
  description: Collection created or Updated Network vips and failed creates.
  type: object
  properties:
    success:
      description: List of network vips.
      type: array
      items:
        $ref: '#/definitions/NetworkModelVip'

# Splat resource for permission check
# =============================================================================
NetworkDeviceConfig:
  description: |
    A resource used by SPLAT only to fulfill permissions check for network device config.
  x-obmcs-splat:
    ocidEntityType: service
    adLocality: regional
    resourceKind: networkDeviceConfig

DeviceConfigResponse:
  description: |
    Device config response
  type: object
  required:
    - config
  properties:
    config:
      description: Device config as string.
      type: string

NetworkRegion:
  description: |
    A resource used by SPLAT for permissions check for network region.
  x-obmcs-splat:
    ocidEntityType: service
    adLocality: regional
    resourceKind: networkRegion

NetworkNamespace:
  description: |
    A resource used by SPLAT for permissions check for network namespace.
  x-obmcs-splat:
    ocidEntityType: service
    adLocality: regional
    resourceKind: networkNamespace

NetworkNamespaceAttribute:
  description: |
    A resource used by SPLAT for permissions check for network namespace attribute (prefix, pool, ip).
  x-obmcs-splat:
    ocidEntityType: service
    adLocality: regional
    resourceKind: networkNamespaceAttribute

NetworkAsnPool:
  description: |
    A resource used by SPLAT for permissions check for network ASN pool
  x-obmcs-splat:
    ocidEntityType: service
    adLocality: regional
    resourceKind: networkAsnPool

NetworkAsnTemplate:
  description: |
    A resource used by SPLAT for permissions check for network ASN template
  x-obmcs-splat:
    ocidEntityType: service
    adLocality: regional
    resourceKind: networkAsnTemplate

NetworkFirmwareImage:
  description: |
    A resource used by SPLAT for permissions check for network firmware image.
  x-obmcs-splat:
    ocidEntityType: service
    adLocality: regional
    resourceKind: networkFirmwareImage

NetworkFirmwareMapping:
  description: |
    A resource used by SPLAT for permissions check for network firmware mapping.
  x-obmcs-splat:
    ocidEntityType: service
    adLocality: regional
    resourceKind: networkFirmwareMapping

NetworkInterfaceTemplate:
  description: |
    A resource used by SPLAT for permissions check for network interface template.
  x-obmcs-splat:
    ocidEntityType: service
    adLocality: regional
    resourceKind: networkInterfaceTemplate

NetworkDevice:
  description: |
    A resource used by SPLAT for permissions check for network device.
  x-obmcs-splat:
    ocidEntityType: service
    adLocality: regional
    resourceKind: networkDevice

NetworkDeviceState:
  description: |
    A resource used by SPLAT for permissions check for network device state.
  x-obmcs-splat:
    ocidEntityType: service
    adLocality: regional
    resourceKind: networkDeviceState

NetworkDeviceStateAttribute:
  description: |
    A resource used by SPLAT for permissions check for network device state attributes.
  x-obmcs-splat:
    ocidEntityType: service
    adLocality: regional
    resourceKind: networkDeviceStateAttribute

NetworkFabric:
  description: |
    A resource used by SPLAT for permissions check for network fabric.
  x-obmcs-splat:
    ocidEntityType: service
    adLocality: regional
    resourceKind: networkFabric

NetworkModel:
  description: |
    A resource used by SPLAT for permissions check for network model.
  x-obmcs-splat:
    ocidEntityType: service
    adLocality: regional
    resourceKind: networkModel

NetworkModelIngestion:
  description: |
    A resource used by SPLAT for permissions check for network model ingestion.
  x-obmcs-splat:
    ocidEntityType: service
    adLocality: regional
    resourceKind: networkModelIngestion

NetworkDeployment:
  description: |
    A resource used by SPLAT for permissions check for network deployment.
  x-obmcs-splat:
    ocidEntityType: service
    adLocality: regional
    resourceKind: networkDeployment

NetworkDeploymentTask:
  description: |
    A resource used by SPLAT for permissions check for network deployment task.
  x-obmcs-splat:
    ocidEntityType: service
    adLocality: regional
    resourceKind: networkDeploymentTask

NetworkConfigs:
  description: |
    A resource used by SPLAT for permissions check for network configs.
  x-obmcs-splat:
    ocidEntityType: service
    adLocality: regional
    resourceKind: networkConfigs

NetworkFabricManifest:
  description: |
    A resource used by SPLAT for permissions check for network fabric manifest.
  x-obmcs-splat:
    ocidEntityType: service
    adLocality: regional
    resourceKind: networkFabricManifest

UpdateExternalPeerInterfaceDetails:
  description: |
    The data needed to update external peering interface
  type: object
  properties:
    sourceInterface:
      $ref: '#/definitions/ExternalInterface'
    destinationInterface:
      $ref: '#/definitions/ExternalInterface'
    destinationDeviceAsn:
      description: asn for external device
      type: integer
      format: int64
    destinationDeviceRoutingContext:
      description: routing context for external peer
      type: string
      minLength: 1
      maxLength: 100

NetworkExternalPeerInterface:
  description: |
    A resource used by SPLAT for permissions check for network external peer interface.
  x-obmcs-splat:
    ocidEntityType: service
    adLocality: regional
    resourceKind: networkExternalPeerInterface

NetworkConfigCos:
  description: |
    A resource used by SPLAT for permissions check for network config cos.
  x-obmcs-splat:
    ocidEntityType: service
    adLocality: regional
    resourceKind: networkConfigCos

NetworkVips:
  description: |
    A resource used by SPLAT for permissions check for network vips.
  x-obmcs-splat:
    ocidEntityType: service
    adLocality: regional
    resourceKind: networkVips

# Network Ports and peer ports
# ================================================
# used for paged response
NetworkDevicePortAndPeerCollection:
  description: |
    Collection of ports with peer information for a device.
  type: object
  required:
    - items
  properties:
    items:
      description: List of ports with peer information.
      type: array
      items:
        $ref: "#/definitions/NetworkDevicePortResponse"

# Used for List response
NetworkDevicePortAndPeerList:
  description: |
    Collection of ports with peer information for a device.
  type: object
  required:
    - items
  properties:
    items:
      description: List of ports with peer information.
      type: array
      items:
        $ref: "#/definitions/NetworkDevicePortResponse"
NetworkDevicePortResponse:
  description: |
    Port information with peer details .
  type: object
  required:
    - id
    - dcmsId
    - portName
    - deviceId
    - deviceDcmsId
    - deviceName
    - deviceRole
    - deviceCurrentState
    - deviceIntendedState
    - isMonitored
    - portType
    - speed
    - peerPort
  properties:
    id:
      description: The unique port id in the database.
      type: string
      minLength: 1
      maxLength: 255
    dcmsId:
      description: The unique port id of the port in the dcms database
      type: string
      minLength: 1
      maxLength: 255
    portName:
      description: The name of the port
      type: string
      minLength: 1
      maxLength: 128
    deviceId:
      description: The id of the device (ocid1.device..)
      type: string
      minLength: 1
      maxLength: 128
    deviceDcmsId:
      description: The unique device id of the device in the dcms database
      type: string
      minLength: 1
      maxLength: 255
    deviceName:
      description: The name of the device
      type: string
      minLength: 1
      maxLength: 128
    deviceRole:
      description: The role of the remote device
      type: string
      minLength: 1
      maxLength: 128
    deviceCurrentState:
      description: The current state of the remote device
      type: string
      minLength: 1
      maxLength: 128
    deviceIntendedState:
      description: The intended state of the remote device
      type: string
      minLength: 1
      maxLength: 128
    isMonitored:
      description: If the device is monitored state of the remote device
      type: boolean
      minLength: 1
      maxLength: 128
    parentInterfaceId:
      description: The unique RmcId of parent interface
      type: string
      minLength: 1
      maxLength: 128
    portGroup:
      description: The port group
      type: string
      minLength: 0
      maxLength: 255
    portType:
      description: The port type
      type: string
      minLength: 1
      maxLength: 128
    speed:
      description: The speed of the port
      type: integer
      format: int64
      minLength: 0
    peerPort:
      $ref: '#/definitions/NetworkDevicePeerPort'
    createdBy:
      description: |
        Entity who created the entry
      type: string
      minLength: 1
      maxLength: 255
    modifiedBy:
      description: |
        Entity who modified the entry
      type: string
      minLength: 1
      maxLength: 255
    timeCreated:
      description: |
        The date and time the resource was created, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).
        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time
    timeUpdated:
      description: |
        The date and time the resource was updated, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).
        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time

NetworkDevicePeerPort:
  description: |
    Port information with peer details .
  type: object
  required:
    - id
    - dcmsId
    - portName
    - deviceId
    - deviceDcmsId
    - deviceName
    - deviceRole
    - deviceCurrentState
    - deviceIntendedState
    - isMonitored
    - portType
    - speed
  properties:
    id:
      description: The unique port id in the database.
      type: string
      minLength: 1
      maxLength: 255
    dcmsId:
      description: The unique port id of the port in the dcms database
      type: string
      minLength: 1
      maxLength: 255
    portName:
      description: The name of the port
      type: string
      minLength: 1
      maxLength: 128
    deviceId:
      description: The id of the device (ocid1.device..)
      type: string
      minLength: 1
      maxLength: 128
    deviceDcmsId:
      description: The unique device id of the device in the dcms database
      type: string
      minLength: 1
      maxLength: 255
    deviceName:
      description: The name of the device
      type: string
      minLength: 1
      maxLength: 128
    deviceRole:
      description: The role of the remote device
      type: string
      minLength: 1
      maxLength: 128
    deviceCurrentState:
      description: The current state of the remote device
      type: string
      minLength: 1
      maxLength: 128
    deviceIntendedState:
      description: The intended state of the remote device
      type: string
      minLength: 1
      maxLength: 128
    isMonitored:
      description: If the device is monitored state of the remote device
      type: boolean
      minLength: 1
      maxLength: 128
    parentInterfaceId:
      description: The unique RmcId of parent interface
      type: string
      minLength: 1
      maxLength: 128
    portGroup:
      description: The port group
      type: string
      minLength: 0
      maxLength: 255
    portType:
      description: The port type
      type: string
      minLength: 1
      maxLength: 128
    speed:
      description: The speed of the port
      type: integer
      format: int64
      minLength: 0

NetworkModelConfigDiff:
  description: |
    network model config difference.
  type: object
  required:
    - difference
  properties:
    difference:
      description: Config difference as string.
      type: string
    isSame:
      description: true if both configs are identical.
      type: boolean
      default: false

NetworkDeviceValidationStatus:
  description: |
    Network device validation status.
  type: object
  required:
    - isValid
  properties:
    isValid:
      description: true if device passes specified validation type.
      type: boolean
      default: false

SecretsRotationAction:
  description: Actions of Secrets Rotation
  type: string
  enum: &SECRETS_ROTATION_ACTION
    - ROLLFORWARD
    - ROLLBACK

InstallCertificateDetails:
  description: |
    details to install certificate
  type: object
  required:
    - forceRotate
  properties:
    forceRotate:
      description: if not set, certificates will not be rotated if they are not expired
      type: boolean

RotateSecretDetails:
  description: |
    details for secret rotations.
  type: object
  required:
    - action
    - rotateAllSecrets
    - secretName
  properties:
    secretName:
      description: name of the secret to be rotated
      type: string
    rotateAllSecrets:
      description: whether to rotate all secrets
      type: boolean
      default: false
    action:
      description: action can be roll forward or roll back
      type: string
      enum: *SECRETS_ROTATION_ACTION
      x-obmcs-top-level-enum: '#/definitions/SecretsRotationAction'
    deviceNames:
      description: List of devices names for which to update secrets. if this is null, update secrets for all devices
      type: array
      minItems: 1
      items:
        type: string
        minLength: 1
        maxLength: 255

# Device state change Parameters
# =============================================================================

DeviceStateChangeDetails:
  description: |
    Devices which needs state change.
  type: object
  required:
    - deviceName
    - targetState
    - presentState
  properties:
    presentState:
      description: The intended state of the device right now
      type: string
    targetState:
      description: Target state intended to move the device to
      type: string
    deviceName:
      description: Device name
      type: string

# Network Devices State analytics
NetworkDevicesStateStatsResponse:
  description: |
    Count of devices in different states
  type: object
  required:
    - currentStateStats
    - intendedStateStats
    - processStateStats
    - region
  properties:
    currentStateStats:
      description: |
        Collection of count of devices in the different current states.
      type: object
      required:
        - items
      properties:
        items:
          description: List of count of devices in the different current states.
          type: array
          items:
            $ref: "#/definitions/NetworkDevicesStateStats"
    intendedStateStats:
      description: |
        Collection of count of devices in the different intended states.
      type: object
      required:
        - items
      properties:
        items:
          description: List of count of devices in the different intended states.
          type: array
          items:
            $ref: "#/definitions/NetworkDevicesStateStats"
    processStateStats:
      description: |
        Collection of count of devices in the different process states.
      type: object
      required:
        - items
      properties:
        items:
          description: List of count of devices in the different process states.
          type: array
          items:
            $ref: "#/definitions/NetworkDevicesStateStats"

NetworkDevicesStateStats:
  description: |
    count of devices and state name
  type: object
  required:
    - count
    - deviceStateName
  properties:
    count:
      description: count of the device in particular state
      type: integer
      format: int64
      minLength: 0
    deviceStateName:
      description: Name of the state of the Device in which it is currently
      type: string

NetworkDevicesStateAnalyticsResponseCollection:
  description: Collection of NetworkDevicesStateAnalyticsResponses.
  type: object
  required:
    - items
  properties:
    items:
      description: List of NetworkDevicesStateAnalyticsResponse.
      type: array
      items:
        $ref: '#/definitions/NetworkDevicesStateAnalyticsResponse'

NetworkDevicesStateAnalyticsResponse:
  description: |
    device state information.
  type: object
  required:
    - deviceId
    - deviceName
  properties:
    deviceId:
      description: The device id in the database.
      type: string
      minLength: 1
      maxLength: 255
    deviceName:
      description: The name of the device
      type: string
      minLength: 1
      maxLength: 128
    role:
      description: The role of the device
      type: string
      minLength: 1
      maxLength: 128
    isMonitored:
      description: If the device is monitored state of the remote device
      type: boolean
      minLength: 1
      maxLength: 128
    currentState:
      description: The actual operational state of the device.
      type: string
      enum: *LIFE_CYCLE_STATE
      x-obmcs-top-level-enum: '#/definitions/DeviceStateLifeCycleState'
    currentStateUpdateTime:
      description: |
        The date and time the current state was updated, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).
        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time
    intendedState:
      description: The state the device should be in.
      type: string
      enum: *LIFE_CYCLE_STATE
      x-obmcs-top-level-enum: '#/definitions/DeviceStateLifeCycleState'
    intendedStateUpdateTime:
      description: |
        The date and time the intended state was updated, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).
        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time
    processState:
      description: The state of current NMC process being performed on the device.
      type: string
      minLength: 1
      maxLength: 128
    processStateUpdateTime:
      description: |
        The date and time the process state was updated, in the format defined by [RFC 3339](https://tools.ietf.org/html/rfc3339).
        Example: `2016-08-25T21:10:29.600Z`
      type: string
      format: date-time

NetInterfaceConfigAttribute:
  description: Interface information.
  type: object
  properties:
    id:
      description: id of the generated net interface config attribute
      type: string
      minLength: 1
      maxLength: 128
    interfaceId:
      description: interface id
      type: string
      minLength: 0
      maxLength: 255
    isDefaultDhcp:
      description: config attribute denoting using of ROMA_DHCP vs DEFAULT_DHCP
      type: boolean
      default: true

CreateNetInterfaceListConfigAttributeDetails:
  description: The data needed to set config attributes on a list of device interfaces
  type: object
  required:
    - interfaceNames
    - isDefaultDhcp
  properties:
    interfaceNames:
      description: List of interface names
      type: array
      minItems: 1
      items:
        type: string
        minLength: 1
        maxLength: 255
    isDefaultDhcp:
      description: Flag to indicate whether these interfaces will use default dhcp or roma dhcp
      type: boolean
      default: true

NetInterfaceListConfigAttributeResponse:
  description: Response on creation of config attributes for list of interfaces
  type: object
  required:
    - workRequestId
    - items
  properties:
    workRequestId:
      description: Triggered work request to update model
      type: string
    items:
      description: will return created net interface config attributes
      type: array
      items:
        $ref: '#/definitions/NetInterfaceConfigAttribute'

NetInterfaceConfigAttributes:
  description: |
    A resource used by SPLAT for permissions check for network interface config attributes.
  x-obmcs-splat:
    ocidEntityType: service
    adLocality: regional
    resourceKind: netInterfaceConfigAttributes

NetworkAnalytics:
  description: |
    A resource used by SPLAT for permissions check for network analytics.
  x-obmcs-splat:
    ocidEntityType: service
    adLocality: regional
    resourceKind: networkAnalytics
