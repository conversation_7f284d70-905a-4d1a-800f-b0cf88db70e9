package com.oracle.oci.rmc.rma.helper;

import static com.oracle.oci.rmc.rma.config.RmaMetricConstants.CREATE_RMA_COLLECTION_EXCEED_MAX_RETRY;
import static com.oracle.oci.rmc.rma.config.RmaMetricConstants.CREATE_RMA_COLLECTION_FAILED_COUNT;
import static com.oracle.oci.rmc.rma.config.RmaMetricConstants.CREATE_RMA_COLLECTION_HTTP_CLIENT_EXCEPTION;
import static com.oracle.oci.rmc.rma.config.RmaMetricConstants.CREATE_RMA_COLLECTION_RETRY_COUNT;
import static com.oracle.oci.rmc.rma.config.RmaMetricConstants.CREATE_RMA_COLLECTION_SUCCEEDED_COUNT;
import static com.oracle.oci.rmc.rma.config.RmaMetricConstants.CREATE_RMA_COLLECTION_TIMEOUT_EXCEPTION;
import static com.oracle.oci.rmc.rma.config.RmaMetricConstants.CREATE_RMA_COLLECTION_UNHANDLED_EXCEPTION;

import com.oracle.oci.rmc.catalog.api.service.PartsCompatibilityService;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeOrder;
import com.oracle.oci.rmc.changemanagement.api.service.ChangeManagementService;
import com.oracle.oci.rmc.dcms.api.model.entity.PartCondition;
import com.oracle.oci.rmc.dcms.api.model.entity.Rma;
import com.oracle.oci.rmc.dcms.api.model.entity.RmaCollection;
import com.oracle.oci.rmc.dcms.api.model.entity.RmaCollectionStatus;
import com.oracle.oci.rmc.dcms.api.service.RegionService;
import com.oracle.oci.rmc.dcms.api.service.RmaCollectionService;
import com.oracle.oci.rmc.dcms.api.service.RmaService;
import com.oracle.oci.rmc.dcms.impl.service.CatalogService;
import com.oracle.oci.rmc.model.RmcId;
import com.oracle.oci.sfw.micronaut.http.pagination.PageQuery;
import com.oracle.oci.sfw.micronaut.http.pagination.PaginatedList;
import com.oracle.oci.sfw.micronaut.http.pagination.TokenPageQuery;
import com.oracle.pic.commons.controllercommons.controllers.NoRetryReconciliationException;
import com.oracle.pic.telemetry.commons.metrics.Metrics;
import io.micronaut.context.annotation.Context;
import io.micronaut.core.annotation.NonNull;
import io.micronaut.core.util.StringUtils;
import io.micronaut.http.client.exceptions.HttpClientResponseException;
import io.micronaut.http.client.exceptions.ReadTimeoutException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.openapitools.api.GsiApi;
import org.openapitools.model.RequestCreateBulkOciRMA;
import org.openapitools.model.RequestCreateBulkOciRMARmaHeadersInner;
import org.openapitools.model.RequestCreateBulkOciRMARmaHeadersInnerRmaLinesInner;
import org.openapitools.model.RequestCreateBulkOciRMARmaHeadersInnerRmaLinesInnerRmaSerialInner;
import org.openapitools.model.ResponseCreateBulkOciRMA;
import org.openapitools.model.ResponseCreateBulkOciRMAResultsInner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings("PMD")
@Context
public class CreateRMACollectionHelper extends RmaHelper {

  private static final Logger LOG = LoggerFactory.getLogger(CreateRMACollectionHelper.class);
  private final RmaCollectionService rmaCollectionService;
  private final GsiApi gsiClient;
  private final ChangeManagementService changeManagementService;

  private static final String BAD = "BAD";
  private static final String GOOD = "GOOD";
  private static final String NOT_SERIALIZED = "NOT_SERIALIZED";

  public CreateRMACollectionHelper(
      RmaCollectionService rmaCollectionService,
      RmaService rmaService,
      GsiApi gsiClient,
      RegionService regionService,
      ChangeManagementService changeManagementService,
      CatalogService catalogService,
      PartsCompatibilityService partsCompatibilityService) {
    super(rmaService, regionService, catalogService, partsCompatibilityService);
    this.rmaCollectionService = rmaCollectionService;
    this.gsiClient = gsiClient;
    this.changeManagementService = changeManagementService;
  }

  public void createRMACollections() {
    LOG.info("Creating RMA collections");
    TokenPageQuery pageQuery = PageQuery.fromEmptyToken(PAGE_QUERY_SIZE);
    boolean hasMoreData = true;

    long succeededCount = 0;
    long failedCount = 0;
    long retryCount = 0;

    while (hasMoreData) {
      PaginatedList<RmaCollection> rmaCollectionsPaginatedList =
          rmaCollectionService.listByStatusAndCreateCollectionGsiRetryCount(
              RmaCollectionStatus.CREATED, MAX_RETRIES, pageQuery);
      for (RmaCollection rmaCollection : rmaCollectionsPaginatedList.getItems()) {
        LOG.info("Processing RMA collection record {}", rmaCollection.id().id());
        try {
          if (rmaCollection.oemPackaging() != null
              && Boolean.FALSE.equals(rmaCollection.mosSupported())) {
            continue;
          }
          ResponseCreateBulkOciRMA responseCreateBulkOciRMA = createRMACollection(rmaCollection);
          validateCreateRMABulkResponse(responseCreateBulkOciRMA);
          markAsSucceeded(rmaCollection);
          succeededCount++;
        } catch (HttpClientResponseException clientResponseException) {

          Metrics.record(CREATE_RMA_COLLECTION_HTTP_CLIENT_EXCEPTION);

          if (RETRYABLE_STATUS.contains(clientResponseException.getStatus())) {
            LOG.warn(
                "Retryable error occurred while calling create rma collection",
                clientResponseException);
            retryCount++;
            if (!handleRetryableFailure(rmaCollection)) {
              failedCount++;
            }
          } else {
            LOG.error(
                "Non-retryable error occurred while calling create rma collection",
                clientResponseException);
            markAsFailed(rmaCollection, clientResponseException.getMessage());
            failedCount++;
          }
        } catch (ReadTimeoutException readTimeoutException) {

          Metrics.record(CREATE_RMA_COLLECTION_TIMEOUT_EXCEPTION);

          LOG.warn(
              "timeout exception occurred while calling create rma collection",
              readTimeoutException);
          retryCount++;
          if (!handleRetryableFailure(rmaCollection)) {
            failedCount++;
          }
        } catch (Exception e) {
          markAsFailed(rmaCollection, e.getMessage());
          LOG.error("Error processing item {}: {}", rmaCollection.id(), e.getMessage(), e);
          failedCount++;

          Metrics.record(CREATE_RMA_COLLECTION_UNHANDLED_EXCEPTION);
        }
      }
      if (rmaCollectionsPaginatedList.getNextPageToken() != null
          && !rmaCollectionsPaginatedList.getNextPageToken().toString().isEmpty()) {
        pageQuery =
            PageQuery.fromToken(rmaCollectionsPaginatedList.getNextPageToken(), PAGE_QUERY_SIZE);
      } else {
        hasMoreData = false;
      }
    }

    Metrics.emit(CREATE_RMA_COLLECTION_SUCCEEDED_COUNT, succeededCount);
    Metrics.emit(CREATE_RMA_COLLECTION_FAILED_COUNT, failedCount);
    Metrics.emit(CREATE_RMA_COLLECTION_RETRY_COUNT, retryCount);
  }

  private ResponseCreateBulkOciRMA createRMACollection(@NonNull RmaCollection rmaCollection)
      throws Exception {
    RequestCreateBulkOciRMARmaHeadersInner createRMACollectionRequest =
        new RequestCreateBulkOciRMARmaHeadersInner();
    String packaging =
        String.join(
            ",",
            Objects.requireNonNull(
                rmaCollection.parcelSerialNumber(), "parcelSerialNumber cannot be null"),
            String.valueOf(
                Objects.requireNonNull(
                    rmaCollection.parcelWeight(), "parcelWeight cannot be null")),
            String.valueOf(
                Objects.requireNonNull(rmaCollection.parcelWidth(), "parcelWidth cannot be null")),
            String.valueOf(
                Objects.requireNonNull(rmaCollection.parcelDepth(), "parcelDepth cannot be null")),
            String.valueOf(
                Objects.requireNonNull(
                    rmaCollection.parcelHeight(), "parcelHeight cannot be null")));
    // TODO: IDNA-6326 - We need to ensure RmaCollection populates site when created in Apex UI
    String gsiSite =
        Optional.ofNullable(rmaCollection.siteRegion())
            .orElseGet(
                () -> {
                  // TODO: IDNA-6326 - delete this fallback to ABL15
                  LOG.warn(
                      "No siteRegion supplied in RMA collection payload. Defaulting to ABL15 ["
                          + " RmaCollection = {} ]",
                      rmaCollection);
                  return "ABL15";
                })
            .toUpperCase(Locale.ROOT);
    createRMACollectionRequest.dciSite(gsiSite);
    createRMACollectionRequest.sourceRmaRef(
        String.format("%s-%d", gsiSite, rmaCollection.rmaCollectionNumber()));
    createRMACollectionRequest.sourceHeaderId(
        BigDecimal.valueOf(rmaCollection.rmaCollectionNumber()));
    createRMACollectionRequest.rmaPackaging(packaging);
    createRMACollectionRequest.rmaPallet(
        Objects.requireNonNull(rmaCollection.isPallet(), "isPallet cannot be null") ? "Y" : "N");
    createRMACollectionRequest.rmaStatus(RmaCollectionStatus.CREATED.getValue());
    createRMACollectionRequest.rmaDate(
        Objects.requireNonNull(rmaCollection.timeCreated(), "timeCreated cannot be null")
            .toString());
    createRMACollectionRequest.gsapReference(
        Boolean.TRUE.equals(rmaCollection.gsap()) ? rmaCollection.gsapTicketNumber() : null);
    createRMACollectionRequest.requestedBy(
        Objects.requireNonNull(rmaCollection.createdBy(), "createdBy cannot be null"));
    String returnType = Boolean.TRUE.equals(rmaCollection.oemPackaging()) ? GOOD : BAD;
    List<Rma> rmaList = getRmaList(rmaCollection);
    createRMACollectionRequest.rmaLines(
        getRequestCreateBulkOciRMARmaHeadersInnerRmaLinesInners(rmaList, returnType));

    RequestCreateBulkOciRMA requestCreateRMACollection = new RequestCreateBulkOciRMA();
    requestCreateRMACollection.addRmaHeadersItem(createRMACollectionRequest);

    LOG.info("Calling GSi Client createRMACollection with request {}", requestCreateRMACollection);
    ResponseCreateBulkOciRMA response =
        gsiClient.miscspDciCreateBulkOcirmaPost(requestCreateRMACollection);
    LOG.info(
        "Received create RMA Collection response for record {}: {}", rmaCollection.id(), response);
    return response;
  }

  private List<RequestCreateBulkOciRMARmaHeadersInnerRmaLinesInner>
      getRequestCreateBulkOciRMARmaHeadersInnerRmaLinesInners(
          List<Rma> rmaList, String returnType) {
    List<RequestCreateBulkOciRMARmaHeadersInnerRmaLinesInner> rmaLinesInners = new ArrayList<>();
    int sourceLineId = 0;
    for (Rma rma : rmaList) {
      sourceLineId++;
      RequestCreateBulkOciRMARmaHeadersInnerRmaLinesInner createRmaLinesInnerRequest =
          new RequestCreateBulkOciRMARmaHeadersInnerRmaLinesInner();
      createRmaLinesInnerRequest.sourceLineId(BigDecimal.valueOf(sourceLineId));
      String partNumber = getItemNum(rma);
      createRmaLinesInnerRequest.itemNum(
          Objects.requireNonNull(partNumber, "oraclePartNumber cannot be null"));
      createRmaLinesInnerRequest.returnType(
          PartCondition.OLD.equals(rma.partCondition()) ? BAD : returnType);
      createRmaLinesInnerRequest.rmaQty(
          BigDecimal.valueOf(Objects.requireNonNull(rma.quantity(), "quantity cannot be null")));
      createRmaLinesInnerRequest.lineStatus(RmaCollectionStatus.CREATED.getValue());
      createRmaLinesInnerRequest.lineComments(getLineComments(rma));
      List<RequestCreateBulkOciRMARmaHeadersInnerRmaLinesInnerRmaSerialInner> rmaArrayList =
          new ArrayList<>();
      RequestCreateBulkOciRMARmaHeadersInnerRmaLinesInnerRmaSerialInner rmaSerialInner =
          new RequestCreateBulkOciRMARmaHeadersInnerRmaLinesInnerRmaSerialInner();
      String serialToUse =
          (rma.assetId() != null && !NOT_SERIALIZED.equals(rma.failedSerialNumber()))
              ? rma.failedSerialNumber()
              : null;
      rmaSerialInner.serialNumber(serialToUse);
      rmaArrayList.add(rmaSerialInner);
      createRmaLinesInnerRequest.rmaSerial(rmaArrayList);
      rmaLinesInners.add(createRmaLinesInnerRequest);
    }
    return rmaLinesInners;
  }

  private boolean handleRetryableFailure(RmaCollection rmaCollection) {
    LOG.warn("Retryable error for item {}", rmaCollection.id());
    int retries =
        rmaCollection.gsiCreateRmaCollectionRetryCount() == null
            ? 0
            : rmaCollection.gsiCreateRmaCollectionRetryCount();
    if (retries < MAX_RETRIES) {
      rmaCollectionService.updateGsiCreateRmaCollection(
          rmaCollection.id(),
          rmaCollection.rmaCollectionStatus(),
          retries + 1,
          null,
          "CreateRMACollection");
      return true;
    } else {
      Metrics.record(CREATE_RMA_COLLECTION_EXCEED_MAX_RETRY);
      LOG.error(
          "Maximum retries reached for rmaCollection {}. Marking as failed. Retryable statuses"
              + " were: {}",
          rmaCollection.id(),
          RETRYABLE_STATUS);
      markAsFailed(rmaCollection, "Maximum retries reached for rmaCollection");
      return false;
    }
  }

  private String getLineComments(Rma rma) {
    if (Objects.requireNonNull(rma.partCondition()) == PartCondition.OLD) {
      String ticketUrl = rma.ticket();
      String ticketId = "NA";
      if (StringUtils.isNotEmpty(ticketUrl)) {
        Pattern pattern = Pattern.compile("(\\d{2}-\\d+)$");
        Matcher matcher = pattern.matcher(ticketUrl);
        if (matcher.find()) {
          ticketId = matcher.group(1);
        }
      }
      RmcId changeOrderId = rma.changeOrderId();
      String taskId = "NA";
      String sequence = "NA";
      if (changeOrderId != null) {
        String trimmedId = changeOrderId.toString().trim();
        taskId = trimmedId.substring(trimmedId.length() - 10);
        try {
          ChangeOrder changeOrder = changeManagementService.get(rma.changeOrderId());
          sequence = changeOrder.changeOrderGsiReference().toString();
        } catch (Exception e) {
          LOG.warn(
              "Failed to retrieve ChangeOrder for RMA ID {}: {}. Proceeding with GSI"
                  + " notification.",
              rma.id(),
              e.getMessage());
        }
      }
      return ticketId + "|" + taskId + "|" + sequence;
    }
    return rma.rmaNumber().toString();
  }

  private void markAsSucceeded(RmaCollection rmaCollection) {
    rmaCollectionService.updateGsiCreateRmaCollection(
        rmaCollection.id(),
        RmaCollectionStatus.PENDING_SHIPMENT,
        rmaCollection.gsiCreateRmaCollectionRetryCount(),
        LocalDateTime.now(ZoneId.of(UTC)),
        "CreateRMACollection");
    LOG.info("Marked item {} as succeeded", rmaCollection.id());
  }

  private void markAsFailed(RmaCollection rmaCollection, String errorMessage) {
    rmaCollectionService.updateGsiCreateRmaCollection(
        rmaCollection.id(),
        rmaCollection.rmaCollectionStatus(),
        MAX_RETRIES + 1,
        null,
        "CreateRMACollection");
    LOG.info("Marked item {} as failed with error message: {}", rmaCollection.id(), errorMessage);
  }

  private void validateCreateRMABulkResponse(ResponseCreateBulkOciRMA response) {
    if (response == null) {
      throw new NoRetryReconciliationException("createRMACollection response is null");
    } else if (response.getResults() == null) {
      throw new NoRetryReconciliationException("createRMACollection response result is null");
    } else if (response.getResults().isEmpty()) {
      throw new NoRetryReconciliationException("createRMACollection response result is empty");
    } else if (StringUtils.isEmpty(response.getResults().get(0).getStatus())) {
      throw new NoRetryReconciliationException(
          "createRMACollection response result status is null");
    }
    ResponseCreateBulkOciRMAResultsInner result = response.getResults().get(0);
    String responseStatus = result.getStatus();
    if (!SUCCESS.equals(responseStatus)) {
      throw new NoRetryReconciliationException(
          "Invalid Non Retryable Status: " + response.getResults().get(0).getErrorMsg());
    }
  }
}
