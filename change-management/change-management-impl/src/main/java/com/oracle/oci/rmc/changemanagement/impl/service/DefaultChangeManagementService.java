package com.oracle.oci.rmc.changemanagement.impl.service;

import static com.oracle.oci.rmc.changemanagement.api.model.common.ChangeOrderIdentifierKey.RACK_SERIAL_NUMBER;
import static com.oracle.oci.rmc.changemanagement.api.model.common.ChangeOrderIdentifierKey.SERIAL_NUMBER;
import static com.oracle.oci.rmc.changemanagement.api.model.common.ChangeOrderIdentifierKey.TARGET_ASSET_ID;
import static com.oracle.oci.rmc.changemanagement.api.model.common.ChangeOrderSourceType.BURNINATOR;
import static com.oracle.oci.rmc.changemanagement.api.model.common.ChangeOrderState.VALIDATED;
import static com.oracle.oci.rmc.changemanagement.api.model.common.ChangeOrderState.VALIDATING;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

import com.google.common.base.Strings;
import com.oracle.bmc.util.VisibleForTesting;
import com.oracle.oci.rmc.auditevent.model.AuditEventType;
import com.oracle.oci.rmc.auditevent.service.Audit;
import com.oracle.oci.rmc.changemanagement.api.model.common.AssetsToActionRequest;
import com.oracle.oci.rmc.changemanagement.api.model.common.ChangeManagerCommandType;
import com.oracle.oci.rmc.changemanagement.api.model.common.ChangeOrderIdentifierKey;
import com.oracle.oci.rmc.changemanagement.api.model.common.ChangeOrderIdentifierRequest;
import com.oracle.oci.rmc.changemanagement.api.model.common.ChangeOrderSourceType;
import com.oracle.oci.rmc.changemanagement.api.model.common.ChangeOrderState;
import com.oracle.oci.rmc.changemanagement.api.model.common.ChangeOrderSummaryDto;
import com.oracle.oci.rmc.changemanagement.api.model.common.ChangeOrderType;
import com.oracle.oci.rmc.changemanagement.api.model.common.CreateChangeOrder;
import com.oracle.oci.rmc.changemanagement.api.model.common.CreateRepairOrder;
import com.oracle.oci.rmc.changemanagement.api.model.common.NeedsAttentionLifecycleState;
import com.oracle.oci.rmc.changemanagement.api.model.common.NeedsAttentionType;
import com.oracle.oci.rmc.changemanagement.api.model.common.OtsResolutionSubType;
import com.oracle.oci.rmc.changemanagement.api.model.common.ProbeState;
import com.oracle.oci.rmc.changemanagement.api.model.common.ProbeType;
import com.oracle.oci.rmc.changemanagement.api.model.common.RackInstanceAccessLevel;
import com.oracle.oci.rmc.changemanagement.api.model.common.RepairOperation;
import com.oracle.oci.rmc.changemanagement.api.model.common.TicketData;
import com.oracle.oci.rmc.changemanagement.api.model.common.TicketField;
import com.oracle.oci.rmc.changemanagement.api.model.entity.AssetsToAction;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeCalendarEvent;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeManagerCommand;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeOrder;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeOrderAssetReservation;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeOrderIdentifier;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeOrderLocation;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeOrderNeedsAttentionDetails;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeOrderNodeImpact;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeOrderStateTransition;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeOrderViewRecord;
import com.oracle.oci.rmc.changemanagement.api.model.entity.Probe;
import com.oracle.oci.rmc.changemanagement.api.model.entity.TicketFieldEntity;
import com.oracle.oci.rmc.changemanagement.api.service.ChangeCalendarQuery;
import com.oracle.oci.rmc.changemanagement.api.service.ChangeManagementService;
import com.oracle.oci.rmc.changemanagement.api.service.InternalChangeManagementService;
import com.oracle.oci.rmc.changemanagement.api.service.ProbeService;
import com.oracle.oci.rmc.changemanagement.impl.dal.AssetsToActionRepository;
import com.oracle.oci.rmc.changemanagement.impl.dal.ChangeCalendarEventRepository;
import com.oracle.oci.rmc.changemanagement.impl.dal.ChangeOrderAssetReservationRepository;
import com.oracle.oci.rmc.changemanagement.impl.dal.ChangeOrderIdentifierRepository;
import com.oracle.oci.rmc.changemanagement.impl.dal.ChangeOrderLocationRepository;
import com.oracle.oci.rmc.changemanagement.impl.dal.ChangeOrderNeedsAttentionDetailRepository;
import com.oracle.oci.rmc.changemanagement.impl.dal.ChangeOrderNodeImpactRepository;
import com.oracle.oci.rmc.changemanagement.impl.dal.ChangeOrderRepository;
import com.oracle.oci.rmc.changemanagement.impl.dal.ChangeOrderStateTransitionRepository;
import com.oracle.oci.rmc.changemanagement.impl.dal.ChangeOrderSummaryRepository;
import com.oracle.oci.rmc.changemanagement.impl.dal.ChangeOrderViewRepository;
import com.oracle.oci.rmc.changemanagement.impl.dal.TicketDataRepository;
import com.oracle.oci.rmc.changemanagement.impl.dal.TicketFieldRepository;
import com.oracle.oci.rmc.changemanagement.impl.internal.ChangeManagerCommandService;
import com.oracle.oci.rmc.changemanagement.impl.internal.OtsService;
import com.oracle.oci.rmc.changemanagement.impl.mapper.ChangeOrderNodeImpactMapper;
import com.oracle.oci.rmc.model.MdcCloseables;
import com.oracle.oci.rmc.model.RmcId;
import com.oracle.oci.rmc.model.RmcIdType;
import com.oracle.oci.rmc.telemetry.Telemetry;
import com.oracle.oci.sfw.micronaut.http.pagination.PageQuery;
import com.oracle.oci.sfw.micronaut.http.pagination.PaginatedList;
import com.oracle.oci.sfw.micronaut.http.pagination.TokenPageQuery;
import com.oracle.pic.telemetry.commons.metrics.Metrics;
import com.oracle.pic.telemetry.commons.metrics.model.MetricName;
import io.micrometer.common.util.StringUtils;
import io.micronaut.context.annotation.Value;
import io.micronaut.core.annotation.NonNull;
import io.micronaut.core.annotation.Nullable;
import io.micronaut.core.util.CollectionUtils;
import jakarta.inject.Singleton;
import jakarta.transaction.Transactional;
import java.time.Duration;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
@SuppressWarnings({
  "PMD.ExcessiveParameterList",
  "PMD.TooManyMethods",
  "PMD.CognitiveComplexity",
  "PMD.CouplingBetweenObjects",
  "PMD.CyclomaticComplexity",
  "PMD.GodClass",
  "PMD.NPathComplexity",
  "PMD.ExcessivePublicCount"
})
public class DefaultChangeManagementService implements InternalChangeManagementService {
  private static final Logger LOG = LoggerFactory.getLogger(DefaultChangeManagementService.class);
  private static final Map<ChangeOrderState, List<ChangeOrderState>> STATE_TRANSITION_MAP =
      Map.of(
          ChangeOrderState.ACCEPTED,
          List.of(ChangeOrderState.CANCELED, ChangeOrderState.ACTIONABLE),
          ChangeOrderState.ACTIONABLE,
          List.of(ChangeOrderState.MITIGATING, ChangeOrderState.CANCELED),
          ChangeOrderState.MITIGATING,
          List.of(VALIDATED, VALIDATING, ChangeOrderState.CANCELED),
          VALIDATING,
          List.of(
              ChangeOrderState.MITIGATING,
              VALIDATED,
              ChangeOrderState.VALIDATION_PROBE_TIMEOUT,
              ChangeOrderState.CLOSED,
              ChangeOrderState.CANCELED),
          ChangeOrderState.VALIDATION_PROBE_TIMEOUT,
          List.of(VALIDATED, ChangeOrderState.CANCELED),
          VALIDATED,
          List.of(ChangeOrderState.CLOSED));

  public static final String CLOSE_PROHIBITED = "Order cannot be closed in its current state.";

  private static final String METRIC_PREFIX = DefaultChangeManagementService.class.getSimpleName();
  private static final String METRIC_CREATE_REPAIR_ORDER = "create";
  private static final String METRIC_DIMENSION_REPAIR_ORDER_TYPE = "type";
  private static final String METRIC_DIMENSION_IS_EXTERNAL = "isExternal";
  private static final String METRIC_DIMENSION_SOURCE = "source";
  private static final String METRIC_RESOLVE_TICKET_FAULT = "resolveTicketFault";
  private static final String METRIC_CLOSE_TICKET_FAULT = "closeTicketFault";
  private static final String METRIC_OPEN_TICKET_FAULT = "openTicketFault";
  private static final String DIMENSION_RESOLVE_ACTION = "resolveAction";
  private static final String DIMENSION_OPEN_ACTION = "openAction";
  private static final String ACTION_CLOSE = "close";
  private static final String ACTION_CANCEL = "cancel";
  private static final String DEFAULT_REASON = "Created by ChangeManagementService";
  private static final List<String> CHANGE_CALENDAR_EVENT_FREEZE_STATES =
      Arrays.asList("CHANGE_FREEZE", "ACCESS_FREEZE");

  private static final String CLOSE_REPAIR_ORDER_REASON =
      "Closed as part of closeRepairOrder by ChangeManagementService";

  public static final String CM_UPDATED_BY_STRING = "CHANGE_MANAGEMENT";
  public static final String RESOURCE_NOT_AUTHORIZED_OR_NOT_FOUND =
      "The requested resource is not authorized or not found.";
  public static final String RESOURCE_INVALID_STATE =
      "Cannot %s the requested resource because it has already been shared with the customer.";
  protected static final String TARGET_ASSETS_ID_VALIDATION_FAILURE =
      ".TargetAssetIdValidationFailure";
  protected static final String DEVICE_SERIAL_NUMBER_VALIDATION_FAILURE =
      ".DeviceSerialNumberValidationFailure";
  protected static final String CHANGE_ORDER_TYPE_VALIDATION_FAILURE =
      ".ChangeOrderTypeValidationFailure";
  protected static final String PRIORITY_VALIDATION_FAILURE = ".PriorityValidationFailure";
  private static final String PROD_ASSET_REGION = "abl";
  private static final int REPAIR_ACTION_LIMIT_NUMBER = 19;
  private static final int OPEN_NEEDS_ATTENTION_DETAILS_LIMIT = 1;
  private static final int MAX_LABEL_NUMBER_LIMIT = 10;
  private static final List<String> TERMINAL_STATES =
      Arrays.stream(ChangeOrderState.values())
          .filter(ChangeOrderState::isTerminal)
          .map(Enum::name)
          .toList();
  private final ChangeCalendarEventRepository changeCalendarEventRepository;
  private final ChangeOrderRepository changeOrderRepository;
  private final TicketDataRepository ticketDataRepository;
  private final TicketFieldRepository ticketFieldRepository;
  private final ChangeManagerCommandService changeManagerCommandService;
  private final ProbeService probeService;
  private final ChangeOrderIdentifierRepository changeOrderIdentifierRepository;
  private final ChangeOrderLocationRepository changeOrderLocationRepository;
  private final ChangeOrderSummaryRepository changeOrderSummaryRepository;
  private final ChangeOrderStateTransitionRepository changeOrderStateTransitionRepository;
  private final ChangeOrderViewRepository changeOrderViewRepository;
  private final AssetsToActionRepository assetsToActionRepository;
  private final ChangeOrderAssetReservationRepository changeOrderAssetReservationRepository;
  private final ChangeOrderNeedsAttentionDetailRepository changeOrderNeedsAttentionDetailRepository;
  private final ChangeOrderNodeImpactRepository changeOrderNodeImpactRepository;
  private final OtsService otsService;

  @Value("${change-management.external.target.compartment.id}")
  private String externalCompartmentId;

  @Value("${change-management.internal.target.compartment.id}")
  private String internalCompartmentId;

  @Value("${rmc.change-management.region:dmo}")
  private String defaultRegion;

  public DefaultChangeManagementService(
      ChangeCalendarEventRepository changeCalendarEventRepository,
      ChangeOrderRepository changeOrderRepository,
      TicketDataRepository ticketDataRepository,
      TicketFieldRepository ticketFieldRepository,
      ChangeManagerCommandService changeManagerCommandService,
      ProbeService probeService,
      ChangeOrderIdentifierRepository changeOrderIdentifierRepository,
      ChangeOrderLocationRepository changeOrderLocationRepository,
      ChangeOrderSummaryRepository changeOrderSummaryRepository,
      ChangeOrderNodeImpactRepository changeOrderNodeImpactRepository,
      ChangeOrderStateTransitionRepository changeOrderStateTransitionRepository,
      ChangeOrderViewRepository changeOrderViewRepository,
      ChangeOrderAssetReservationRepository changeOrderAssetReservationRepository,
      ChangeOrderNeedsAttentionDetailRepository changeOrderNeedsAttentionDetailRepository,
      AssetsToActionRepository assetsToActionRepository,
      OtsService otsService) {
    this.changeCalendarEventRepository = changeCalendarEventRepository;
    this.changeOrderRepository = changeOrderRepository;
    this.ticketDataRepository = ticketDataRepository;
    this.ticketFieldRepository = ticketFieldRepository;
    this.changeManagerCommandService = changeManagerCommandService;
    this.changeOrderStateTransitionRepository = changeOrderStateTransitionRepository;
    this.probeService = probeService;
    this.changeOrderIdentifierRepository = changeOrderIdentifierRepository;
    this.changeOrderLocationRepository = changeOrderLocationRepository;
    this.changeOrderSummaryRepository = changeOrderSummaryRepository;
    this.changeOrderNodeImpactRepository = changeOrderNodeImpactRepository;
    this.changeOrderViewRepository = changeOrderViewRepository;
    this.changeOrderAssetReservationRepository = changeOrderAssetReservationRepository;
    this.changeOrderNeedsAttentionDetailRepository = changeOrderNeedsAttentionDetailRepository;
    this.assetsToActionRepository = assetsToActionRepository;
    this.otsService = otsService;
  }

  @Override
  public ChangeOrder create(
      CreateChangeOrder createChangeOrder,
      ChangeOrderSourceType source,
      boolean isSharedWithCustomer) {
    return create(createChangeOrder, source, isSharedWithCustomer, source.toString());
  }

  @Transactional
  @Audit(eventName = "CREATE_REPAIR_ORDER", eventType = AuditEventType.POST)
  @SuppressWarnings({"PMD.NcssCount"})
  public ChangeOrder create(
      CreateChangeOrder createChangeOrder,
      ChangeOrderSourceType source,
      boolean isSharedWithCustomer,
      String createdBy) {
    try (var telemetry =
        new Telemetry(
            Metrics.name(METRIC_PREFIX)
                .child(METRIC_CREATE_REPAIR_ORDER)
                .withDimensions(
                    Map.of(
                        METRIC_DIMENSION_REPAIR_ORDER_TYPE,
                        createChangeOrder.getChangeOrderType().name(),
                        METRIC_DIMENSION_IS_EXTERNAL,
                        isSharedWithCustomer ? "true" : "false",
                        METRIC_DIMENSION_SOURCE,
                        source.name())))) {

      if (createChangeOrder.getChangeOrderType() == ChangeOrderType.MULTI_REPAIR) {
        if (createChangeOrder.getLabels() == null || createChangeOrder.getLabels().isEmpty()) {
          throw new IllegalArgumentException(
              "Labels cannot be empty for MULTI_REPAIR repair orders.");
        }
        if (source != BURNINATOR) {
          throw new IllegalArgumentException(
              "MULTI_REPAIR repair orders must have source BURNINATOR.");
        }
      }
      validateCreateChangeOrderData(createChangeOrder);
      if (createChangeOrder.getChangeOrderType() != ChangeOrderType.NETWORK_LEAF_SPINE_LINK_REPAIR
          && createChangeOrder.getChangeOrderType() != ChangeOrderType.NETWORK_GENERIC_LINK_REPAIR
          && createChangeOrder.getChangeOrderType() != ChangeOrderType.NETWORK_DEVICE_REPAIR) {
        checkForExistingActiveOrder(
            createChangeOrder.getDeviceSerialNumber(),
            createChangeOrder.getTargetAssetId(),
            createChangeOrder.getChangeOrderType());
      }
      RmcId changeOrderId = RmcId.generate(RmcIdType.CM.CHANGE_ORDER);
      List<ChangeOrderIdentifier> identifierList = new ArrayList<>();
      List<ChangeOrderLocation> locationList = new ArrayList<>();
      String compartmentId = createChangeOrder.getCompartmentId();
      String region = createChangeOrder.getRegion();
      if (createChangeOrder.getChangeOrderType() == ChangeOrderType.RACK_INGESTION) {
        compartmentId = internalCompartmentId;
        LOG.info(
            "Creating RACK_INGESTION change order: region='{}', compartmentId='{}' (internal"
                + " default)",
            region,
            compartmentId);
      } else {
        validateCompartmentAndRegion(compartmentId, region);
      }
      Instant timeNow = Instant.now();
      // Build identifierList and locationList
      if (createChangeOrder.getChangeOrderIdentifierRequests() != null
          && !createChangeOrder.getChangeOrderIdentifierRequests().isEmpty()) {
        // TODO: add validation for identifiers and locations
        for (ChangeOrderIdentifierRequest request :
            createChangeOrder.getChangeOrderIdentifierRequests()) {
          RmcId identifierId = RmcId.generate(RmcIdType.CM.CHANGE_ORDER_IDENTIFIER);
          identifierList.add(
              new ChangeOrderIdentifier(
                  identifierId, changeOrderId, request.identifierKey(), request.identifierValue()));
          ChangeOrderIdentifierRequest.LocationData location = request.location();
          if (location != null) {
            locationList.add(
                new ChangeOrderLocation(
                    RmcId.generate(RmcIdType.CM.CHANGE_ORDER_LOCATION),
                    identifierId,
                    location.rackInstanceId(),
                    location.rackPositionId(),
                    location.rackNumber(),
                    location.elevation(),
                    location.dataHallName()));
          }
        }
      }
      List<ChangeOrderNodeImpact> nodeImpactList =
          buildNodeImpacts(createChangeOrder, changeOrderId, timeNow);
      try (var ignored = new MdcCloseables().put(changeOrderId)) {
        String summary = mapChangeOrderTypeToSummary(createChangeOrder.getChangeOrderType());

        String ticketUrlOrKey = ticketKeyOrUrl(createChangeOrder);

        ChangeOrder changeOrder =
            ChangeOrder.id(changeOrderId)
                .labels(createChangeOrder.getLabels())

                // of the url until
                .targetAssetId(
                    createChangeOrder
                        .getTargetAssetId()) // TicketManagerStore asynchronously updates the
                // ChangeOrder's ticket details
                .serialNumber(createChangeOrder.getDeviceSerialNumber())
                .type(createChangeOrder.getChangeOrderType())
                .details(createChangeOrder.getDetails())
                .priority(createChangeOrder.getPriority())
                .severity(createChangeOrder.getChangeOrderType().getDefaultSeverity())
                .state(ChangeOrderState.ACCEPTED)
                .summary(summary)
                .description(createChangeOrder.getRemark())
                .source(source)
                .isSharedWithCustomer(isSharedWithCustomer)
                .previousChangeOrderId(createChangeOrder.getPreviousChangeOrderId())
                .repairOrderId(createChangeOrder.getRepairOrderId())
                .remark(createChangeOrder.getRemark())
                .timeCreated(timeNow)
                .accessLevel(createChangeOrder.getAccessLevel())
                .compartmentId(compartmentId)
                .region(region)
                .targetRepairOperationsTeam(createChangeOrder.getTargetRepairOperationsTeam())
                .ticketId(createChangeOrder.getTicketId())
                .ticketUrl(ticketUrlOrKey)
                .timeHandedOverHandled(
                    createChangeOrder.getRackHandedOverTime() == null ? null : timeNow)
                .build();
        changeOrder = changeOrderRepository.save(changeOrder);
        LOG.info("Change order saved: {}", changeOrder);
        if (createChangeOrder.getTicketData() != null) {
          ticketDataRepository.saveAll(
              createChangeOrder.getTicketData().forChangeOrder(changeOrderId));
        }

        if (createChangeOrder.getTicketFields() != null) {
          ticketFieldRepository.saveAll(
              TicketFieldEntity.entities(createChangeOrder.getTicketFields())
                  .changeOrderId(changeOrderId));
        }

        if (!identifierList.isEmpty()) {
          changeOrderIdentifierRepository.saveAll(identifierList);
          changeOrder =
              changeOrder
                  .withPreviousChangeOrderId(createChangeOrder.getPreviousChangeOrderId())
                  .withIdentifiers(identifierList);
        }
        if (!locationList.isEmpty()) {
          changeOrderLocationRepository.saveAll(locationList);
        }
        if (!nodeImpactList.isEmpty()) {
          changeOrderNodeImpactRepository.saveAll(nodeImpactList);
        }
        // Create a probe if diagnosticProbeOutput is not empty
        String diagnosticProbeOutput = createChangeOrder.getDiagnosticProbeOutputAsYamlString();
        if (isNotBlank(diagnosticProbeOutput)) {

          ProbeService.CreateProbe createProbe =
              new ProbeService.CreateProbe(
                  RmcId.generate(RmcIdType.CM.PROBE),
                  changeOrder.id(),
                  null,
                  ProbeType.DIAGNOSTIC,
                  ProbeState.ACCEPTED);
          Probe probe = probeService.create(createProbe);
          // Attach the ProbeResult

          ProbeService.AttachProbeResult attachProbeResult =
              new ProbeService.AttachProbeResult(
                  diagnosticProbeOutput,
                  "1.0", // Version can be set accordingly
                  false, // isErroneous
                  true, // isTerminal
                  createChangeOrder.getDiagnosticReportObjectStorageUrl(),
                  createChangeOrder.getErrorCodes());
          probeService.attachProbeResult(probe.id(), attachProbeResult);
        }

        // Create preRepair Probe
        if (ProbeService.needsPreRepairProbe(changeOrder)) {
          ProbeService.CreateProbe createPreRepairProbe =
              new ProbeService.CreateProbe(
                  RmcId.generate(RmcIdType.CM.PROBE),
                  changeOrder.id(),
                  null,
                  ProbeType.PRE_REPAIR_VALIDATION,
                  ProbeState.ACCEPTED);
          probeService.create(createPreRepairProbe);
        }

        String ticketKey = createChangeOrder.getExistingOtsTicketKey();
        ChangeManagerCommandType changeManagerCommandType =
            ticketCommandType(ticketKey, changeOrder);
        if (changeManagerCommandType == null) {
          LOG.info(
              "No ticket {} created for changeOrderId: {}",
              ChangeManagerCommand.class.getSimpleName(),
              changeOrderId);
          ChangeOrder head =
              updateRepairOrderTicketDetails(changeOrder.resolvedRepairOrderId(), null, null);
          if (ticketKey != null) {
            LOG.warn(
                "Found existingOtsTicketKey: {} for non-head ChangeOrder {}, repairOrderId: {}",
                ticketKey,
                changeOrderId,
                head.id());
          }
        } else {
          ChangeManagerCommand createTicketCommand =
              changeManagerCommandService.create(
                  new ChangeManagerCommandService.CreateChangeManagerCommand(
                      changeOrder.id(), changeManagerCommandType, DEFAULT_REASON));
          LOG.info("{}} command saved: {}", changeManagerCommandType, createTicketCommand);
        }

        ChangeManagerCommand createTaskCommand =
            changeManagerCommandService.create(
                new ChangeManagerCommandService.CreateChangeManagerCommand(
                    changeOrder.id(), ChangeManagerCommandType.CREATE_TASKS, DEFAULT_REASON));
        LOG.info("Created CREATE_TASKS command {}", createTaskCommand);

        if (createChangeOrder.getAssetsToActions() == null) {
          LOG.error("CreateChangeOrder assets to action is empty!");
          throw new IllegalArgumentException("CreateChangeOrder assets to action is empty!");
        }

        // If type is not MULTI_REPAIR, populate Assets To Action Table
        if (!createChangeOrder.getChangeOrderType().equals(ChangeOrderType.MULTI_REPAIR)) {
          populateAssetsToAction(createChangeOrder, changeOrderId);
        }
        // Set timeApproved unless it's a Burninator repair order with triage not skipped
        if (changeOrder.source() != BURNINATOR
            || Boolean.TRUE.equals(createChangeOrder.isTriageSkipped())) {
          ChangeOrder latestChangeOrder = getByChangeOrderId(changeOrderId).get();
          ChangeOrder updatedOrder =
              latestChangeOrder
                  .withTimeApproved(changeOrder.timeCreated())
                  .withTimeUpdated(Instant.now());

          changeOrder = changeOrderRepository.update(updatedOrder);
        }
        telemetry.recordSuccess();
        return changeOrder;
      }
    }
  }

  private String ticketKeyOrUrl(CreateChangeOrder createChangeOrder) {
    String id = createChangeOrder.getTicketId();
    String url = createChangeOrder.getTicketUrl();
    String key = createChangeOrder.getExistingOtsTicketKey();

    if (url == null && id == null) {
      LOG.info("CreateChangeOrder has no ticket info, using existingOtsTicketKey: {}", key);
      return key;
    }

    if (url != null && id != null && key == null) {
      LOG.info(
          "CreateChangeOrder has ticket info and no existingOtsTicketKey, using ticketUrl {}", url);
      return url;
    }

    String value = id == null ? key : url;
    LOG.error(
        "Found unexpected CreateChangeOrder ticket details, using: {}.\n"
            + "  existingTicketKey: {}, ticketId: {}, ticketUrl: {}",
        value,
        key,
        id,
        url);
    return value;
  }

  /**
   * @return null if a ticket does not need to be created or adopted for the given ChangeOrder
   */
  private ChangeManagerCommandType ticketCommandType(String ticketKey, ChangeOrder changeOrder) {
    if (changeOrder.ticketId() != null || !changeOrder.isHead()) {
      LOG.info(
          "Change Order ID: {} is not a head, no {} created",
          changeOrder.id(),
          ChangeManagerCommand.class.getSimpleName());
      return null;
    }

    if (ChangeOrderSourceType.RACK_INGESTION.equals(changeOrder.source())) {
      LOG.info(
          "Change Order ID: {} is a RACK_INGESTION, no {} created",
          changeOrder.id(),
          ChangeManagerCommand.class.getSimpleName());
      return null;
    }

    return ticketKey == null
        ? ChangeManagerCommandType.CREATE_TICKET
        : ChangeManagerCommandType.ADOPT_TICKET;
  }

  @Override
  public ChangeOrder createInternalRepairOrder(
      CreateChangeOrder createChangeOrder,
      ChangeOrderSourceType changeOrderSourceType,
      boolean isSharedWithCustomer) {
    if (!changeOrderSourceType.isInternal()) {
      LOG.warn("Unexpected repair order type: {}", changeOrderSourceType);
      throw new IllegalArgumentException("Invalid changeOrderSourceType for internal order");
    }
    return create(createChangeOrder, changeOrderSourceType, isSharedWithCustomer);
  }

  @Override
  @Transactional
  public ChangeOrder reopen(
      CreateChangeOrder reopenRepairOrder,
      ChangeOrderSourceType changeOrderSourceType,
      boolean isSharedWithCustomer) {
    ChangeOrder headChangeOrder =
        changeOrderRepository
            .findById(reopenRepairOrder.getRepairOrderId())
            .orElseThrow(
                () ->
                    new IllegalArgumentException(
                        "Head change order not found: " + reopenRepairOrder.getRepairOrderId()));
    if (headChangeOrder.previousChangeOrderId() != null) {
      LOG.warn(
          "Cannot reopen an order that is not a head order. Order ID: {}",
          headChangeOrder.previousChangeOrderId());
      throw new IllegalArgumentException(RESOURCE_NOT_AUTHORIZED_OR_NOT_FOUND);
    }
    ChangeOrder tailChangeOrder =
        headChangeOrder.tailChangeOrderId() == null
            ? headChangeOrder
            : changeOrderRepository
                .findById(headChangeOrder.tailChangeOrderId())
                .orElseThrow(
                    () -> new IllegalArgumentException(RESOURCE_NOT_AUTHORIZED_OR_NOT_FOUND));

    if (tailChangeOrder.type() == ChangeOrderType.NETWORK_LEAF_SPINE_LINK_REPAIR
        || tailChangeOrder.type() == ChangeOrderType.MULTI_REPAIR) {
      LOG.warn("{} type is not supported for reopen operation.", tailChangeOrder.type());
      throw new IllegalArgumentException(
          tailChangeOrder.type().name() + " type is not supported for reopen operation.");
    }
    if (!tailChangeOrder.state().isTerminal()) {
      LOG.warn(
          "Cannot reopen an order that is in non-terminal state. Order ID: {}",
          tailChangeOrder.id());
      throw new IllegalArgumentException("Cannot reopen a non-terminal repair order chain.");
    }

    if (!changeOrderSourceType.isInternal() && !headChangeOrder.isSharedWithCustomer()) {
      LOG.warn(
          "Cannot reopen an internal order with an external type. Order Type: {}",
          changeOrderSourceType.name());
      throw new IllegalArgumentException(RESOURCE_NOT_AUTHORIZED_OR_NOT_FOUND);
    }

    if (!tailChangeOrder.type().equals(reopenRepairOrder.getChangeOrderType())) {
      LOG.info(
          "Change order type changed from {} to {} for reopened chain.",
          tailChangeOrder.type(),
          reopenRepairOrder.getChangeOrderType());
    }

    CreateChangeOrder modifiedCreate =
        CreateRepairOrder.targetAssetId(reopenRepairOrder.getTargetAssetId())
            .labels(reopenRepairOrder.getLabels())
            .diagnosticProbeOutput(reopenRepairOrder.getDiagnosticProbeOutput())
            .deviceSerialNumber(reopenRepairOrder.getDeviceSerialNumber())
            .details(reopenRepairOrder.getDetails())
            .priority(reopenRepairOrder.getPriority())
            .repairOrderType(reopenRepairOrder.getChangeOrderType())
            .previousChangeOrderId(
                tailChangeOrder.id()) // current tail will become the previous order of the new tail
            .tailChangeOrderId(null) // reopened order will be the tail.
            .revisedComment(reopenRepairOrder.getRevisedComment())
            .remark(reopenRepairOrder.getRemark())
            .repairOrderId(reopenRepairOrder.getRepairOrderId()) // set head order id
            .identifierRequests(reopenRepairOrder.getChangeOrderIdentifierRequests())
            .assetsToActions(
                mapAssetsToActionRequestToAssetsToActions(
                    assetsToActionRepository.findByChangeOrderId(tailChangeOrder.id())))
            .compartmentId(tailChangeOrder.compartmentId())
            .region(tailChangeOrder.region())
            .build();
    ChangeOrder newTailChangeOrder =
        create(
            modifiedCreate,
            changeOrderSourceType,
            isSharedWithCustomer,
            "Reopened by ChangeManagementService");

    // Set tailChangeOrderId for head order
    Instant now = Instant.now();
    ChangeOrder newHeadOrder =
        headChangeOrder
            .withTailChangeOrderId(newTailChangeOrder.id())
            .withTimeUpdated(now)
            .withTimeCompleted(now);
    changeOrderRepository.update(newHeadOrder);
    return assembleReopenedChangeOrder(newTailChangeOrder, newHeadOrder);
  }

  @Override
  @Transactional
  public ChangeOrder createChangeOrderWithoutOperationDetails(
      RmcId repairOrderId,
      ChangeOrderSourceType changeOrderSourceType,
      Boolean isSharedWithCustomer,
      Boolean isExternalRequest,
      RepairOperation operation) {
    ChangeOrder headChangeOrder = getHeadChangeOrder(repairOrderId);
    ChangeOrder tailChangeOrder = getTailChangeOrder(headChangeOrder);

    if (!isExternalRequest && !tailChangeOrder.source().isInternal()) {
      LOG.warn("{} do not have access to update the repair order.", tailChangeOrder.source());
      throw new IllegalArgumentException(RESOURCE_NOT_AUTHORIZED_OR_NOT_FOUND);
    }

    if (tailChangeOrder.type() == ChangeOrderType.NETWORK_LEAF_SPINE_LINK_REPAIR
        || tailChangeOrder.type() == ChangeOrderType.MULTI_REPAIR) {
      LOG.warn("{} type is not supported for replyTo or fail operation.", tailChangeOrder.type());
      throw new IllegalArgumentException(
          tailChangeOrder.type().name() + " type is not supported for replyTo or fail operation.");
    }

    prohibitInternalOperationOnCustomerRepair(isExternalRequest, tailChangeOrder, operation.name());

    switch (operation) {
      case FAIL:
        validateAndHandleFailedRepairOrder(tailChangeOrder, null);
        break;
      case REPLY_TO:
        validateNeedsAttentionState(tailChangeOrder, null);
        break;
      default:
        throw new IllegalArgumentException("Unsupported operation type: " + operation);
    }
    Optional<ChangeOrderIdentifier> rackIdOpt =
        getIdentifierByChangeOrderIdAndIdentifierKey(tailChangeOrder.id(), RACK_SERIAL_NUMBER);
    List<ChangeOrderIdentifierRequest> identifierRequests =
        getChangeOrderIdentifierRequests(rackIdOpt, tailChangeOrder);

    CreateChangeOrder modifiedCreate =
        buildCreateChangeOrder(repairOrderId, tailChangeOrder, identifierRequests, headChangeOrder);

    ChangeOrder newTailChangeOrder =
        create(
            modifiedCreate,
            changeOrderSourceType == null ? tailChangeOrder.source() : changeOrderSourceType,
            isSharedWithCustomer == null
                ? tailChangeOrder.isSharedWithCustomer()
                : isSharedWithCustomer,
            "Created as part of no CreateRepairOrderDetails by ChangeManagementService");

    ChangeOrder newHeadOrder =
        updateNewHeadChangeOrder(headChangeOrder.id(), newTailChangeOrder.id());
    // the new change order's ticketId and url got assigned by TicketManager which is an
    // asynchronous process
    openOtsTicket(tailChangeOrder, isExternalRequest, operation);
    return assembleReopenedChangeOrder(newTailChangeOrder, newHeadOrder);
  }

  // Build new tail change order from previous change orders in the chain
  private CreateRepairOrder buildCreateChangeOrder(
      RmcId repairOrderId,
      ChangeOrder tailChangeOrder,
      List<ChangeOrderIdentifierRequest> identifierRequests,
      ChangeOrder headChangeOrder) {

    List<ChangeOrderAssetReservation> allReservations =
        changeOrderAssetReservationRepository.findByChangeOrderIds(List.of(tailChangeOrder.id()));
    if (!allReservations.isEmpty()) {
      throw new IllegalArgumentException(
          "Unsupported operation, please provide serial number of the device");
    }

    return CreateRepairOrder.targetAssetId(tailChangeOrder.targetAssetId())
        .labels(tailChangeOrder)
        .diagnosticProbeOutput(null)
        .deviceSerialNumber(tailChangeOrder.serialNumber())
        .details(tailChangeOrder.details())
        .priority(tailChangeOrder.priority())
        .repairOrderType(tailChangeOrder.type())
        .previousChangeOrderId(tailChangeOrder.id())
        .tailChangeOrderId(null)
        .repairOrderId(repairOrderId)
        .remark(tailChangeOrder.remark())
        .identifierRequests(identifierRequests)
        .assetsToActions(
            mapAssetsToActionRequestToAssetsToActions(
                assetsToActionRepository.findByChangeOrderId(tailChangeOrder.id())))
        // Copy over node impact related data from tail
        .changeOrderNodeImpactRequests(
            ChangeOrderNodeImpactMapper.mapChangeOrderNodeImpactToChangeOrderNodeImpactRequest(
                getChangeOrderNodeImpactByChangeOrderIds(List.of(tailChangeOrder.id()))))
        .compartmentId(tailChangeOrder.compartmentId())
        .region(tailChangeOrder.region())
        .ticketId(headChangeOrder.ticketId())
        .ticketUrl(headChangeOrder.ticketUrl())
        .build();
  }

  private List<ChangeOrderIdentifierRequest> getChangeOrderIdentifierRequests(
      Optional<ChangeOrderIdentifier> rackIdOpt, ChangeOrder tailChangeOrder) {
    String rackSerialNumber =
        rackIdOpt
            .orElseThrow(
                () ->
                    new IllegalStateException(
                        "Rack serial number identifier not found for changeOrder "
                            + tailChangeOrder.id()))
            .identifierValue();
    return List.of(
        new ChangeOrderIdentifierRequest(
            SERIAL_NUMBER.getKey(), tailChangeOrder.serialNumber(), null),
        new ChangeOrderIdentifierRequest(
            TARGET_ASSET_ID.getKey(), tailChangeOrder.targetAssetId().toString(), null),
        new ChangeOrderIdentifierRequest(RACK_SERIAL_NUMBER.getKey(), rackSerialNumber, null));
  }

  @Override
  @Transactional
  public ChangeOrder processRepairOrderOperation(
      CreateChangeOrder createChangeOrder,
      ChangeOrderSourceType changeOrderSourceType,
      boolean isSharedWithCustomer,
      boolean isExternalRequest,
      RepairOperation operation) {
    ChangeOrder headChangeOrder = getHeadChangeOrder(createChangeOrder.getRepairOrderId());
    ChangeOrder tailChangeOrder = getTailChangeOrder(headChangeOrder);

    prohibitInternalOperationOnCustomerRepair(isExternalRequest, tailChangeOrder, operation.name());

    if (tailChangeOrder.type() == ChangeOrderType.NETWORK_LEAF_SPINE_LINK_REPAIR
        || tailChangeOrder.type() == ChangeOrderType.MULTI_REPAIR) {
      LOG.warn("{} type is not supported for replyTo or fail operation.", tailChangeOrder.type());
      throw new IllegalArgumentException(
          tailChangeOrder.type() + " type is not supported for replyTo or fail operation.");
    }

    if (!tailChangeOrder.type().equals(createChangeOrder.getChangeOrderType())) {
      LOG.info(
          "Repair order type changed from {} to {} for new operation.",
          tailChangeOrder.type(),
          createChangeOrder.getChangeOrderType());
    }

    switch (operation) {
      case FAIL:
        validateAndHandleFailedRepairOrder(tailChangeOrder, createChangeOrder.getRevisedComment());
        break;
      case REPLY_TO:
        validateNeedsAttentionState(tailChangeOrder, createChangeOrder.getRevisedComment());
        break;
      default:
        throw new IllegalArgumentException("Unsupported operation type: " + operation);
    }

    ChangeOrder newTailChangeOrder =
        createNewTailChangeOrder(
            createChangeOrder,
            tailChangeOrder,
            changeOrderSourceType,
            isSharedWithCustomer,
            operation == RepairOperation.FAIL
                ? "Fail operation by ChangeManagementService"
                : "Reply operation by ChangeManagementService",
            headChangeOrder);

    ChangeOrder newHeadOrder =
        updateNewHeadChangeOrder(headChangeOrder.id(), newTailChangeOrder.id());
    // the new change order's ticketId and url got assigned by TicketManager which is an
    // asynchronous process
    openOtsTicket(tailChangeOrder, isExternalRequest, operation);
    return assembleReopenedChangeOrder(newTailChangeOrder, newHeadOrder);
  }

  private ChangeOrder getHeadChangeOrder(RmcId changeOrderId) {
    ChangeOrder headChangeOrder =
        changeOrderRepository
            .findById(changeOrderId)
            .orElseThrow(
                () ->
                    new IllegalArgumentException("Head change order not found: " + changeOrderId));
    if (headChangeOrder.previousChangeOrderId() != null) {
      LOG.warn(
          "Cannot recreate an order that is not a head order. Order ID: {}",
          headChangeOrder.previousChangeOrderId());
      throw new IllegalArgumentException(RESOURCE_NOT_AUTHORIZED_OR_NOT_FOUND);
    }
    return headChangeOrder;
  }

  private ChangeOrder getTailChangeOrder(ChangeOrder headChangeOrder) {
    return headChangeOrder.tailChangeOrderId() == null
        ? headChangeOrder
        : changeOrderRepository
            .findById(headChangeOrder.tailChangeOrderId())
            .orElseThrow(() -> new IllegalArgumentException(RESOURCE_NOT_AUTHORIZED_OR_NOT_FOUND));
  }

  private ChangeOrder createNewTailChangeOrder(
      CreateChangeOrder createChangeOrder,
      ChangeOrder tailChangeOrder,
      ChangeOrderSourceType changeOrderSourceType,
      boolean isSharedWithCustomer,
      String createdBy,
      ChangeOrder headOrder) {
    CreateRepairOrder createRepairOrder = (CreateRepairOrder) createChangeOrder;
    CreateRepairOrder modifiedCreate =
        createRepairOrder
            .withPreviousChangeOrderId(tailChangeOrder.id())
            .withTailChangeOrderId(null)
            .withRegion(tailChangeOrder.region())
            .withCompartmentId(tailChangeOrder.compartmentId())
            .withTicketId(headOrder.ticketId())
            .withTicketUrl(headOrder.ticketUrl());

    return create(modifiedCreate, changeOrderSourceType, isSharedWithCustomer, createdBy);
  }

  ChangeOrder updateNewHeadChangeOrder(RmcId headChangeOrderId, RmcId tailChangeOrderId) {
    // Need to get the head changeOrder again, since the original change order may have been updated
    ChangeOrder headChangeOrder =
        changeOrderRepository
            .findById(headChangeOrderId)
            .orElseThrow(
                () ->
                    new IllegalArgumentException(
                        "Head change order not found: " + headChangeOrderId));

    Instant now = Instant.now();

    // Set tailChangeOrderId for head order
    ChangeOrder newHeadOrder =
        headChangeOrder
            .withTailChangeOrderId(tailChangeOrderId)
            .withTimeUpdated(now)
            .withTimeCompleted(now);
    changeOrderRepository.update(newHeadOrder);
    return newHeadOrder;
  }

  void validateAndHandleFailedRepairOrder(ChangeOrder tailChangeOrder, String revisedComment) {
    if (!tailChangeOrder.state().equals(VALIDATED) && !tailChangeOrder.state().equals(VALIDATING)) {
      throw new IllegalArgumentException(
          String.format(
              "Cannot fail repair order that the change order %s is in %s state.",
              tailChangeOrder.id(), tailChangeOrder.state()));
    }
    Instant now = Instant.now();
    ChangeOrder updateTailChangeOrder =
        tailChangeOrder
            .withState(ChangeOrderState.CLOSED)
            .withIsFailed(true)
            .withRevisedComment(revisedComment)
            .withTimeUpdated(now)
            .withTimeCompleted(now);
    changeOrderRepository.update(updateTailChangeOrder);
  }

  private List<AssetsToActionRequest> mapAssetsToActionRequestToAssetsToActions(
      List<AssetsToAction> assetsToActions) {
    List<AssetsToActionRequest> assetsToActionRequests = new ArrayList<>();
    for (AssetsToAction assetsToAction : assetsToActions) {
      assetsToActionRequests.add(createAssetsToActions(assetsToAction));
    }
    return assetsToActionRequests;
  }

  private AssetsToActionRequest createAssetsToActions(AssetsToAction assetsToAction) {
    return new AssetsToActionRequest(
        assetsToAction.failedAssetId(),
        assetsToAction.rackPosition(),
        RmcId.fromString(assetsToAction.rackId()),
        RmcId.fromString(assetsToAction.rackId()),
        assetsToAction.elevation(),
        assetsToAction.verified(),
        null,
        Instant.now(),
        0L,
        assetsToAction.partId());
  }

  void validateNeedsAttentionState(ChangeOrder tailChangeOrder, String revisedComment) {
    // The ReplyTo API requires the tail change order to match the repair order's
    // NEEDS_ATTENTION state (must have at least one open NeedsAttentionDetails).
    List<ChangeOrderNeedsAttentionDetails> openNeedsAttentionDetails =
        changeOrderNeedsAttentionDetailRepository.findNeedsAttentionDetailsByChangeOrderIds(
            List.of(tailChangeOrder.id()), NeedsAttentionLifecycleState.OPEN.name(), null);
    if (openNeedsAttentionDetails.isEmpty()) {
      LOG.warn(
          "Repair order does not have open needs attention details. Change Order ID: {}",
          tailChangeOrder.id());
      throw new IllegalArgumentException(
          "Repair order does not have open needs attention details.");
    }
    if (revisedComment != null) {
      Instant now = Instant.now();
      ChangeOrder updateTailChangeOrder =
          tailChangeOrder
              .withState(ChangeOrderState.CLOSED)
              .withRevisedComment(revisedComment)
              .withTimeUpdated(now)
              .withTimeCompleted(now);
      changeOrderRepository.update(updateTailChangeOrder);
    }
  }

  @Override
  public Optional<ChangeOrder> findChangeOrderByIdInternal(RmcId id) {
    return changeOrderRepository.findById(id);
  }

  @Override
  public ChangeOrder get(RmcId id) {
    Optional<ChangeOrder> maybeChangeOrder = changeOrderRepository.findById(id);
    if (maybeChangeOrder.isEmpty()) {
      throw new IllegalArgumentException(RESOURCE_NOT_AUTHORIZED_OR_NOT_FOUND);
    }
    var headChangeOrder = maybeChangeOrder.get();
    if (headChangeOrder.previousChangeOrderId() != null) {
      // This ID is not a head
      LOG.warn("Cannot get an order that is not a head order. Order ID: {}", headChangeOrder.id());
      throw new IllegalArgumentException(RESOURCE_NOT_AUTHORIZED_OR_NOT_FOUND);
    }

    if (headChangeOrder.tailChangeOrderId() == null) {
      return headChangeOrder;
    }

    ChangeOrder tailChangeOrder =
        changeOrderRepository
            .findById(headChangeOrder.tailChangeOrderId())
            .orElseThrow(() -> new IllegalArgumentException(RESOURCE_NOT_AUTHORIZED_OR_NOT_FOUND));

    // Build the "RepairOrder" but with the head order’s ID, tail order’s content
    return assembleReopenedChangeOrder(tailChangeOrder, headChangeOrder);
  }

  @Override
  public PaginatedList<ChangeOrderSummaryDto> list(
      ChangeManagementService.ListFilters filters, TokenPageQuery pageToken) {
    List<String> lifecycleStates = getEntityStateList(filters.lifecycleState());
    TokenPageQuery effectivePageToken = pageToken;
    if (effectivePageToken.getSortBy() == null || effectivePageToken.getSortBy().isEmpty()) {
      effectivePageToken =
          PageQuery.fromToken(
              effectivePageToken.getPage(),
              effectivePageToken.getLimit(),
              PageQuery.SortOrder.DESC,
              "timeCreated");
    }
    if (Boolean.TRUE.equals(filters.needsAttention())
        || lifecycleStates == null
        || lifecycleStates.isEmpty()) {
      return changeOrderSummaryRepository.list(
          filters.id(),
          filters.deviceSerialNumber(),
          filters.rackSerialNumber(),
          filters.changeOrderTypes(),
          filters.label(),
          filters.source(),
          filters.isSharedWithCustomer(),
          filters.interfaceA(),
          filters.interfaceB(),
          filters.logicalTargetA(),
          filters.logicalTargetB(),
          filters.updatedAfter(),
          filters.needsAttention(),
          filters.linkRole(),
          filters.severity(),
          filters.deviceId(),
          filters.deviceName(),
          filters.deviceRole(),
          filters.compartmentIds() == null
              ? List.of(internalCompartmentId, externalCompartmentId)
              : filters.compartmentIds(),
          filters.region(),
          effectivePageToken);
    } else {
      return changeOrderSummaryRepository.list(
          filters.id(),
          filters.deviceSerialNumber(),
          filters.rackSerialNumber(),
          filters.changeOrderTypes(),
          filters.label(),
          filters.source(),
          lifecycleStates,
          filters.isSharedWithCustomer(),
          filters.interfaceA(),
          filters.interfaceB(),
          filters.logicalTargetA(),
          filters.logicalTargetB(),
          filters.updatedAfter(),
          filters.linkRole(),
          filters.severity(),
          filters.deviceId(),
          filters.deviceName(),
          filters.deviceRole(),
          filters.compartmentIds() == null
              ? List.of(internalCompartmentId, externalCompartmentId)
              : filters.compartmentIds(),
          filters.region(),
          effectivePageToken);
    }
  }

  @Override
  public PaginatedList<ChangeOrder> listChangeOrdersInternal(
      ChangeManagementService.ListFilters filters, TokenPageQuery pageToken) {
    List<String> lifecycleStates = filters.changeOrderStates();
    if (CollectionUtils.isEmpty(lifecycleStates)) {
      lifecycleStates = getEntityStateList(filters.lifecycleState());
    }
    if (CollectionUtils.isEmpty(lifecycleStates)) {
      return changeOrderRepository.list(
          filters.id(),
          filters.deviceSerialNumber(),
          filters.changeOrderTypes(),
          filters.source(),
          filters.isSharedWithCustomer(),
          filters.interfaceA(),
          filters.interfaceB(),
          filters.logicalTargetA(),
          filters.logicalTargetB(),
          filters.linkRole(),
          filters.severity(),
          filters.deviceId(),
          filters.deviceName(),
          filters.deviceRole(),
          filters.updatedAfter(),
          pageToken);
    } else {
      return changeOrderRepository.list(
          filters.id(),
          filters.deviceSerialNumber(),
          filters.changeOrderTypes(),
          filters.source(),
          lifecycleStates,
          filters.isSharedWithCustomer(),
          filters.interfaceA(),
          filters.interfaceB(),
          filters.logicalTargetA(),
          filters.logicalTargetB(),
          filters.linkRole(),
          filters.severity(),
          filters.deviceId(),
          filters.deviceName(),
          filters.deviceRole(),
          filters.updatedAfter(),
          pageToken);
    }
  }

  @Override
  @Transactional
  @Audit(eventName = "PRIORITZE_REPAIR_ORDER", eventType = AuditEventType.PATCH)
  public ChangeOrder prioritizeRepairOrder(
      RmcId repairOrderId, Integer priority, String updatedBy) {

    ChangeOrder changeOrder =
        changeOrderRepository
            .findById(repairOrderId)
            .orElseThrow(() -> new IllegalArgumentException(RESOURCE_NOT_AUTHORIZED_OR_NOT_FOUND));

    if (changeOrder.previousChangeOrderId() != null) {
      throw new IllegalArgumentException(RESOURCE_NOT_AUTHORIZED_OR_NOT_FOUND);
    }

    ChangeOrder tailChangeOrder =
        changeOrder.tailChangeOrderId() == null
            ? changeOrder
            : changeOrderRepository
                .findById(changeOrder.tailChangeOrderId())
                .orElseThrow(
                    () -> new IllegalArgumentException(RESOURCE_NOT_AUTHORIZED_OR_NOT_FOUND));

    if (tailChangeOrder.state().isTerminal()
        && !(tailChangeOrder.state() == ChangeOrderState.CLOSED
            && Boolean.TRUE.equals(tailChangeOrder.isFailed()))) {
      LOG.warn("Cannot update priority of a terminal repair order {}", repairOrderId);
      throw new IllegalArgumentException("Cannot update priority of a terminal repair order");
    }

    ChangeOrder updatedChangeOrder =
        tailChangeOrder.withPriority(priority).withTimeUpdated(Instant.now());

    updatedChangeOrder = update(updatedChangeOrder, updatedBy);

    return assembleReopenedChangeOrder(updatedChangeOrder, changeOrder);
  }

  @Override
  public PaginatedList<ChangeOrder> listNonTerminalChangeOrdersForProtectedRacks(
      TokenPageQuery pageToken) {
    List<String> nonTerminalStates =
        Arrays.stream(ChangeOrderState.values())
            .filter(state -> !state.isTerminal())
            .map(Enum::name)
            .toList();
    return changeOrderRepository.listChangeOrdersByStateAndAccessLevel(
        RackInstanceAccessLevel.RING1, nonTerminalStates, pageToken);
  }

  @Override
  public ChangeOrder cancelInternalRepairOrder(
      RmcId repairOrderId, String reason, String updatedBy, boolean isFromApi) {
    return cancelRepairOrder(repairOrderId, reason, false, updatedBy, isFromApi);
  }

  @Override
  @Transactional
  @Audit(eventName = "CANCEL_REPAIR_ORDER", eventType = AuditEventType.PATCH)
  public ChangeOrder cancelRepairOrder(
      RmcId repairOrderId,
      String reason,
      boolean isExternalRequest,
      String updatedBy,
      boolean isFromApi) {
    ChangeOrder changeOrder =
        changeOrderRepository
            .findById(repairOrderId)
            .orElseThrow(() -> new IllegalArgumentException(RESOURCE_NOT_AUTHORIZED_OR_NOT_FOUND));

    if (changeOrder.previousChangeOrderId() != null) {
      throw new IllegalArgumentException(RESOURCE_NOT_AUTHORIZED_OR_NOT_FOUND);
    }

    ChangeOrder tailChangeOrder =
        changeOrder.tailChangeOrderId() == null
            ? changeOrder
            : changeOrderRepository
                .findById(changeOrder.tailChangeOrderId())
                .orElseThrow(
                    () -> new IllegalArgumentException(RESOURCE_NOT_AUTHORIZED_OR_NOT_FOUND));

    prohibitInternalOperationOnCustomerRepair(isExternalRequest, tailChangeOrder, "cancel");

    if (tailChangeOrder.type().equals(ChangeOrderType.RACK_INGESTION) && isFromApi) {
      throw new IllegalArgumentException(RESOURCE_NOT_AUTHORIZED_OR_NOT_FOUND);
    }

    List<ChangeOrderNeedsAttentionDetails> openNeedsAttentionDetails =
        changeOrderNeedsAttentionDetailRepository.findNeedsAttentionDetailsByChangeOrderIds(
            List.of(tailChangeOrder.id()), NeedsAttentionLifecycleState.OPEN.name(), null);

    // We only allow to cancel a repair order in ACCEPTED or NEEDS_ATTENTION state.
    if (openNeedsAttentionDetails.isEmpty()) {
      if (!(tailChangeOrder.state() == ChangeOrderState.ACCEPTED
          || tailChangeOrder.state() == ChangeOrderState.ACTIONABLE)) {
        LOG.warn(
            "Cancel is allowed only for orders in ACCEPTED/ACTIONABLE state or with open NAD. Order"
                + " ID: {}",
            tailChangeOrder.id());
        throw new IllegalArgumentException("Order cannot be cancelled in its current state.");
      }
    } else {
      if (openNeedsAttentionDetails.size() > OPEN_NEEDS_ATTENTION_DETAILS_LIMIT) {
        LOG.warn(
            "Tail ChangeOrder {} has multiple ({}) open NeedsAttentionDetails; expected only one.",
            tailChangeOrder.id(),
            openNeedsAttentionDetails.size());
      }
      for (ChangeOrderNeedsAttentionDetails nad : openNeedsAttentionDetails) {
        ChangeOrderNeedsAttentionDetails updatedNad =
            nad.withState(NeedsAttentionLifecycleState.CLOSED);
        changeOrderNeedsAttentionDetailRepository.update(updatedNad);
      }
    }

    ChangeOrder updatedChangeOrder =
        updateState(
            tailChangeOrder,
            ChangeOrderState.CANCELED,
            reason,
            tailChangeOrder.isFailed(),
            updatedBy,
            true);
    LOG.info("Canceling repair order {}.", updatedChangeOrder.id());

    resolveOtsTicket(updatedChangeOrder, OtsResolutionSubType.CANCELLED, isExternalRequest);
    closeOtsTicket(updatedChangeOrder, OtsResolutionSubType.CANCELLED, isExternalRequest);
    return assembleReopenedChangeOrder(updatedChangeOrder, changeOrder);
  }

  @Override
  @Audit(eventName = "UPDATE_REPAIR_ORDER", eventType = AuditEventType.PATCH)
  public ChangeOrder update(ChangeOrder changeOrder, String updatedBy) {
    return changeOrderRepository.update(changeOrder);
  }

  @Override
  public ChangeOrder updateState(
      ChangeOrder changeOrder,
      ChangeOrderState newState,
      String updatedBy,
      boolean skipStateValidation) {
    return updateState(changeOrder, newState, null, null, updatedBy, skipStateValidation);
  }

  @Transactional
  public ChangeOrder updateState(
      ChangeOrder changeOrder,
      ChangeOrderState newState,
      @Nullable String reason,
      @Nullable Boolean isFailed,
      @Nullable String stateTransitionOwner,
      boolean skipStateValidation) {
    // If repair order is NEEDS_ATTENTION state, will skip validation for replyTo, approve, cancel,
    // close methods.
    if (!skipStateValidation) {
      validateStateTransition(changeOrder.state(), newState);
    }
    if (newState.equals(changeOrder.state())) {
      LOG.info(
          "No state update needed, skip, change order id {}, current state and new state {}",
          changeOrder.id(),
          newState);
      return changeOrder;
    }
    try (var ignored = new MdcCloseables().put(changeOrder.id())) {
      Instant now = Instant.now();
      ChangeOrder toUpdate =
          changeOrder
              .withState(newState)
              .withReason(reason)
              .withIsFailed(isFailed)
              .withTimeUpdated(now)
              .withTimeCompleted(newState.isTerminal() ? now : null);
      final ChangeOrder updated = update(toUpdate, CM_UPDATED_BY_STRING);
      LOG.info("Updated change order {} to state {}.", changeOrder.id(), newState);
      ChangeOrderStateTransition stateTransition =
          changeOrderStateTransitionRepository.save(
              new ChangeOrderStateTransition(
                  RmcId.generate(RmcIdType.CM.CHANGE_ORDER_STATE_TRANSITION),
                  changeOrder.id(),
                  changeOrder.state(),
                  newState,
                  now,
                  stateTransitionOwner));
      LOG.info("Saved change order state transition {}", stateTransition.id());
      return updated;
    }
  }

  /**
   * Updates a change order with the provided ticket details
   *
   * @param assigneeEmail email address of the tech to whom the OTS ticket is assigned to
   * @param summary summary of the OTS ticket
   * @param changeOrderId change order which needs to be updated
   * @return an Optional containing the corresponding ChangeOrder object if found; otherwise,
   *     returns an empty Optional
   */
  @Override
  public Optional<ChangeOrder> updateChangeOrderTicketDetails(
      @Nullable String assigneeEmail, @NonNull String summary, @NonNull RmcId changeOrderId) {
    final Optional<ChangeOrder> changeOrderOpt = changeOrderRepository.findById(changeOrderId);
    if (changeOrderOpt.isEmpty()) {
      throw new IllegalArgumentException("No change order found with id: " + changeOrderId);
    }

    final ChangeOrder fetchedOrder = changeOrderOpt.get();

    if (fetchedOrder.assigneeEmail() != null
        && fetchedOrder.assigneeEmail().equalsIgnoreCase(assigneeEmail)
        && fetchedOrder.summary().equalsIgnoreCase(summary)) {
      LOG.info("Assignee and Summary have not changed for change order {} ", changeOrderId);
      return Optional.ofNullable(fetchedOrder);
    }

    ChangeOrder updatedOrder =
        fetchedOrder
            .withSummary(summary)
            .withAssigneeEmail(assigneeEmail)
            .withTimeUpdated(Instant.now());
    return Optional.ofNullable(update(updatedOrder, CM_UPDATED_BY_STRING));
  }

  @Override
  public Optional<ChangeOrder> getChangeOrderByTicketId(@NonNull String ticketId) {
    return changeOrderRepository.getChangeOrderByTicketId(ticketId);
  }

  @SuppressWarnings({"PMD.CognitiveComplexity"})
  @Override
  public boolean isChangePermitted(ChangeCalendarQuery query) {
    validateQuery(query);
    ZonedDateTime startTime = query.start();
    Duration duration = query.duration();
    ZonedDateTime endTime = startTime.plus(duration);
    Set<String> dataHallIds = query.dataHallIds();
    String regionId = query.regionId();
    boolean mustBeInChangeWindow = query.mustBeInChangeWindow();
    boolean mustRespectChangeFreeze = query.mustRespectChangeFreeze();
    boolean mustRespectAccessFreeze = query.mustRespectAccessFreeze();
    if (dataHallIds != null && !dataHallIds.isEmpty()) {
      if (mustBeInChangeWindow) {
        List<ChangeCalendarEvent> availableChangeWindow =
            changeCalendarEventRepository.findMatchedChangeWindowWithDataHallIds(
                regionId, dataHallIds, startTime.toInstant(), endTime.toInstant());
        if (availableChangeWindow.isEmpty()) {
          return false;
        }
      }
      if (!mustRespectChangeFreeze && !mustRespectAccessFreeze) {
        return true;
      }
      List<ChangeCalendarEvent> freezeWindow =
          changeCalendarEventRepository.findFreezeWindowWithDataHallIds(
              dataHallIds,
              regionId,
              startTime.toInstant(),
              startTime.plus(duration).toInstant(),
              mustRespectChangeFreeze,
              mustRespectAccessFreeze);
      return freezeWindow.isEmpty();
    } else {
      // regionId can't be null when dataHallIds is not provided, validated in validateQuery
      if (mustBeInChangeWindow) {
        List<ChangeCalendarEvent> availableChangeWindow =
            changeCalendarEventRepository.findMatchedChangeWindow(
                regionId, startTime.toInstant(), endTime.toInstant());
        if (availableChangeWindow.isEmpty()) {
          return false;
        }
      }
      if (!mustRespectChangeFreeze && !mustRespectAccessFreeze) {
        return true;
      }
      List<ChangeCalendarEvent> freezeWindow =
          changeCalendarEventRepository.findFreezeWindow(
              regionId,
              startTime.toInstant(),
              startTime.plus(duration).toInstant(),
              mustRespectChangeFreeze,
              mustRespectAccessFreeze);
      return freezeWindow.isEmpty();
    }
  }

  @Override
  public PaginatedList<ChangeCalendarEvent> listChangeCalendarEvents(
      List<String> changeCalendarEventTypes,
      String changeCalendarEventId,
      String dataHallName,
      ZonedDateTime timeStartGreaterThanOrEqualTo,
      ZonedDateTime timeStartLessThan,
      ZonedDateTime timeEndGreaterThanOrEqualTo,
      ZonedDateTime timeEndLessThan,
      TokenPageQuery pageQuery) {
    return null;
  }

  @SuppressWarnings({"PMD.CognitiveComplexity"})
  private void validateQuery(ChangeCalendarQuery query) {
    if (query.start() == null) {
      throw new IllegalArgumentException("Start time in query can't be null");
    }
    if (query.duration() == null) {
      throw new IllegalArgumentException("Duration in query can't be null");
    }
    if (!query.mustBeInChangeWindow()
        && !query.mustRespectChangeFreeze()
        && !query.mustRespectAccessFreeze()) {
      throw new IllegalArgumentException(
          "Unusual query with mustBeInChangeWindow, mustRespectChangeFreeze and"
              + " mustRespectAccessFreeze as false");
    }
    Set<String> dataHallIds = query.dataHallIds();
    String regionId = query.regionId();
    if ((dataHallIds == null || dataHallIds.isEmpty()) && regionId == null) {
      throw new IllegalArgumentException("Must have data hall or region ID.");
    }
  }

  private List<String> getEntityStateList(String lifecycleState) {
    if (lifecycleState == null) {
      return List.of();
    }
    return switch (lifecycleState.toUpperCase(Locale.US)) {
      case "VALIDATING" -> List.of(VALIDATING.name());
      case "VALIDATED" -> List.of(VALIDATED.name());
      case "CANCELED" -> List.of(ChangeOrderState.CANCELED.name());
      case "IN_PROGRESS" -> List.of(ChangeOrderState.MITIGATING.name());
      case "CLOSED" ->
          List.of(ChangeOrderState.VALIDATION_PROBE_TIMEOUT.name(), ChangeOrderState.CLOSED.name());
      case "ACCEPTED" ->
          List.of(ChangeOrderState.ACCEPTED.name(), ChangeOrderState.ACTIONABLE.name());
      default -> throw new IllegalArgumentException("Invalid order state: " + lifecycleState);
    };
  }

  @Override
  public ChangeOrder closeRepairOrder(RmcId repairOrderId, boolean isFailed, String updatedBy) {
    return closeInternalRepairOrder(
        repairOrderId, CLOSE_REPAIR_ORDER_REASON, true, isFailed, updatedBy);
  }

  @Override
  @Transactional
  public ChangeOrder closeInternalRepairOrder(
      RmcId repairOrderId,
      String reason,
      boolean isExternalRequest,
      boolean isFailed,
      String updatedBy) {
    ChangeOrder headOrder =
        changeOrderRepository
            .findById(repairOrderId)
            .orElseThrow(() -> new IllegalArgumentException(RESOURCE_NOT_AUTHORIZED_OR_NOT_FOUND));

    if (headOrder.previousChangeOrderId() != null) {
      throw new IllegalArgumentException(RESOURCE_NOT_AUTHORIZED_OR_NOT_FOUND);
    }

    ChangeOrder tailChangeOrder =
        (headOrder.tailChangeOrderId() == null)
            ? headOrder
            : changeOrderRepository
                .findById(headOrder.tailChangeOrderId())
                .orElseThrow(
                    () -> new IllegalArgumentException(RESOURCE_NOT_AUTHORIZED_OR_NOT_FOUND));

    prohibitInternalOperationOnCustomerRepair(isExternalRequest, tailChangeOrder, "close");

    List<ChangeOrderNeedsAttentionDetails> openNeedsAttentionDetails =
        changeOrderNeedsAttentionDetailRepository.findNeedsAttentionDetailsByChangeOrderIds(
            List.of(tailChangeOrder.id()), NeedsAttentionLifecycleState.OPEN.name(), null);

    // We only allow to close a repair order in VALIDATED or NEEDS_ATTENTION state.
    if (openNeedsAttentionDetails.isEmpty()) {
      assertClosePermitted(tailChangeOrder);
    } else {
      if (openNeedsAttentionDetails.size() > OPEN_NEEDS_ATTENTION_DETAILS_LIMIT) {
        LOG.warn(
            "Tail ChangeOrder {} has multiple ({}) open NeedsAttentionDetails; expected only one.",
            tailChangeOrder.id(),
            openNeedsAttentionDetails.size());
      }
      for (ChangeOrderNeedsAttentionDetails nad : openNeedsAttentionDetails) {
        ChangeOrderNeedsAttentionDetails updatedNad =
            nad.withState(NeedsAttentionLifecycleState.CLOSED);
        changeOrderNeedsAttentionDetailRepository.update(updatedNad);
      }
    }

    ChangeOrder updatedChangeOrder =
        updateState(tailChangeOrder, ChangeOrderState.CLOSED, reason, isFailed, updatedBy, true);
    LOG.info("Repair order {} closed with isFailed={}", updatedChangeOrder.id(), isFailed);

    resolveOtsTicket(updatedChangeOrder, OtsResolutionSubType.CLOSED, isExternalRequest);
    closeOtsTicket(updatedChangeOrder, OtsResolutionSubType.CLOSED, isExternalRequest);

    return assembleReopenedChangeOrder(updatedChangeOrder, headOrder);
  }

  @SuppressWarnings({"PMD.AvoidCatchingGenericException"})
  void resolveOtsTicket(
      ChangeOrder tail, OtsResolutionSubType resolutionSubType, boolean isExternalRequest) {
    String ticketId = tail.ticketId();
    if (tail.ticketId() == null) {
      LOG.info(
          "No OTS ticket to resolve for {} RepairOrder {}, tail changeOrderId: {}",
          tail.state(),
          tail.resolvedRepairOrderId(),
          tail.id());
      return;
    }

    try {
      // the user who resolved the ticket should not overwrite the current ticket assignee
      otsService.resolveTicket(
          null,
          ticketId,
          resolutionSubType,
          "Closed by %s customer".formatted(isExternalRequest ? "external" : "internal"));
      LOG.info("OTS ticket resolved as {}, url: {}", resolutionSubType, tail.ticketUrl());
    } catch (Exception e) {
      Metrics.emit(
          MetricName.of(METRIC_PREFIX)
              .child(METRIC_RESOLVE_TICKET_FAULT)
              .withDimensions(
                  Map.of(
                      DIMENSION_RESOLVE_ACTION,
                      resolutionSubType == OtsResolutionSubType.CANCELLED
                          ? ACTION_CANCEL
                          : ACTION_CLOSE,
                      METRIC_DIMENSION_IS_EXTERNAL,
                      String.valueOf(isExternalRequest))),
          1d);
      LOG.error(
          "Failed to resolve OTS ticket as {}, url: {}, id: {}",
          resolutionSubType,
          tail.ticketUrl(),
          tail.id(),
          e);
    }
  }

  @SuppressWarnings({"PMD.AvoidCatchingGenericException"})
  void closeOtsTicket(
      ChangeOrder tail, OtsResolutionSubType resolutionSubType, boolean isExternalRequest) {
    try {
      // the user who closes the ticket should not overwrite the current ticket assignee
      otsService.closeTicket(
          null,
          tail.ticketId(),
          resolutionSubType,
          "%s by %s customer"
              .formatted(resolutionSubType, isExternalRequest ? "external" : "internal"));
      LOG.info("OTS ticket closed, url: {}", tail.ticketUrl());
    } catch (Exception e) {
      Metrics.emit(
          MetricName.of(METRIC_PREFIX)
              .child(METRIC_CLOSE_TICKET_FAULT)
              .withDimensions(
                  Map.of(METRIC_DIMENSION_IS_EXTERNAL, String.valueOf(isExternalRequest))),
          1d);
      LOG.error("Failed to close OTS ticket, url: {}, id: {}", tail.ticketUrl(), tail.id(), e);
    }
  }

  @SuppressWarnings({"PMD.AvoidCatchingGenericException"})
  void openOtsTicket(ChangeOrder tail, boolean isExternalRequest, RepairOperation operation) {
    try {
      // the user who opens the ticket should not overwrite the current ticket assignee
      otsService.openTicket(null, tail.ticketId());
      LOG.info("OTS ticket opened, url: {}", tail.ticketUrl());
    } catch (Exception e) {
      Metrics.emit(
          MetricName.of(METRIC_PREFIX)
              .child(METRIC_OPEN_TICKET_FAULT)
              .withDimensions(
                  Map.of(
                      DIMENSION_OPEN_ACTION,
                      operation.name(),
                      METRIC_DIMENSION_IS_EXTERNAL,
                      String.valueOf(isExternalRequest))),
          1d);
      LOG.error("Failed to open OTS ticket, url: {}, id: {}", tail.ticketUrl(), tail.id(), e);
    }
  }

  @VisibleForTesting
  public static void assertClosePermitted(ChangeOrder tailChangeOrder) {
    final ChangeOrderSourceType source = tailChangeOrder.source();
    final ChangeOrderState state = tailChangeOrder.state();
    final RmcId tailId = tailChangeOrder.id();
    final RmcId repairOrderId = tailChangeOrder.repairOrderId();
    if (VALIDATED.equals(state)) {
      LOG.debug(
          "CloseRepairOrder permitted from {}, for {} RepairOrder: {}, tail id: {}",
          state,
          source,
          repairOrderId,
          tailId);
      return;
    }

    if (!VALIDATING.equals(state)) {
      LOG.error(
          "CloseRepairOrder is prohibited from {}, for {} RepairOrder: {}, tail id: {}.  State must"
              + " be VALIDATING",
          state,
          source,
          repairOrderId,
          tailId);
      throw new IllegalArgumentException(CLOSE_PROHIBITED);
    }

    if (tailChangeOrder.isSharedWithCustomer()) {
      LOG.error(
          "CloseRepairOrder is prohibited from {}, for {} RepairOrder: {}, tail id: {}. "
              + " RepairOrder isSharedWithCustomer must be false.",
          state,
          source,
          repairOrderId,
          tailId);
      throw new IllegalArgumentException(CLOSE_PROHIBITED);
    }

    LOG.debug(
        "CloseRepairOrder permitted from {}, for {} RepairOrder: {}, tail id: {}",
        state,
        source,
        repairOrderId,
        tailId);
  }

  @Override
  public List<ChangeOrderAssetReservation> listChangeOrderAssetReservation(RmcId changeOrderId) {
    return changeOrderAssetReservationRepository.findByChangeOrderId(changeOrderId);
  }

  private void validateCreateChangeOrderData(CreateChangeOrder createChangeOrder) {
    if (createChangeOrder == null) {
      throw new IllegalArgumentException("CreateChangeOrder cannot be null.");
    }
    if (createChangeOrder.getLabels() != null
        && createChangeOrder.getLabels().size() > MAX_LABEL_NUMBER_LIMIT) {
      throw new IllegalArgumentException("A maximum of 10 labels are allowed.");
    }
    if (createChangeOrder.getChangeOrderType() != ChangeOrderType.NETWORK_LEAF_SPINE_LINK_REPAIR
        && createChangeOrder.getChangeOrderType() != ChangeOrderType.NETWORK_GENERIC_LINK_REPAIR
        && createChangeOrder.getChangeOrderType() != ChangeOrderType.NETWORK_DEVICE_REPAIR) {
      validateIdentifiersForSerialNumberAndAssetId(
          createChangeOrder.getChangeOrderIdentifierRequests());
    }
    validateChangeOrderType(createChangeOrder.getChangeOrderType());
    validatePriority(createChangeOrder.getPriority());
    // Validate the ChangeOrderIdentifierRequests match required keys
    validateChangeOrderIdentifiers(
        createChangeOrder.getChangeOrderType(),
        createChangeOrder.getChangeOrderIdentifierRequests());
  }

  private void validateChangeOrderIdentifiers(
      ChangeOrderType changeOrderType, List<ChangeOrderIdentifierRequest> identifierRequests) {
    if (identifierRequests == null || identifierRequests.isEmpty()) {
      throw new IllegalArgumentException("ChangeOrderIdentifierRequests cannot be null or empty.");
    }

    Set<String> requiredKeys =
        changeOrderType.getRequiredKeys().stream()
            .map(ChangeOrderIdentifierKey::getKey)
            .collect(Collectors.toSet());

    Set<String> providedKeys = new HashSet<>();
    for (ChangeOrderIdentifierRequest req : identifierRequests) {
      validateSingleIdentifierRequest(req);
      providedKeys.add(req.identifierKey());
    }

    if (!requiredKeys.equals(providedKeys)) {
      throw new IllegalArgumentException(
          String.format(
              "Mismatch between required and provided keys. Required: %s, Provided: %s",
              requiredKeys, providedKeys));
    }
  }

  private void validateSingleIdentifierRequest(ChangeOrderIdentifierRequest req) {
    validateField(req.identifierKey(), "Identifier key");
    validateField(req.identifierValue(), "Identifier value");

    if (req.location() != null) {
      validateRmcIdField(req.location().rackInstanceId(), "Location rackInstanceId");
      validateRmcIdField(req.location().rackPositionId(), "Location rackPositionId");
      validateField(req.location().rackNumber(), "Location rackNumber");
      validateField(req.location().elevation(), "Location elevation");
      validateField(req.location().dataHallName(), "Location dataHallName");
    }
  }

  private void validateField(String fieldValue, String fieldName) {
    if (fieldValue == null || fieldValue.isEmpty()) {
      throw new IllegalArgumentException(fieldName + " cannot be null or empty.");
    }
  }

  private void validateRmcIdField(RmcId fieldValue, String fieldName) {
    if (fieldValue == null) {
      throw new IllegalArgumentException(fieldName + " cannot be null.");
    }
  }

  private void validateIdentifiersForSerialNumberAndAssetId(
      List<ChangeOrderIdentifierRequest> identifierRequests) {
    if (identifierRequests == null || identifierRequests.isEmpty()) {
      throw new IllegalArgumentException("ChangeOrderIdentifierRequests cannot be null or empty.");
    }

    boolean hasValidSerialNumber =
        identifierRequests.stream()
            .anyMatch(
                req ->
                    SERIAL_NUMBER.getKey().equals(req.identifierKey())
                        && req.identifierValue() != null
                        && !req.identifierValue().isEmpty());

    boolean hasValidAssetId =
        identifierRequests.stream()
            .anyMatch(
                req ->
                    TARGET_ASSET_ID.getKey().equals(req.identifierKey())
                        && req.identifierValue() != null
                        && !req.identifierValue().isEmpty());

    if (!hasValidSerialNumber) {
      LOG.warn("Valid serial number is required in ChangeOrderIdentifierRequests.");
      Metrics.emit(METRIC_PREFIX + DEVICE_SERIAL_NUMBER_VALIDATION_FAILURE, 1d);
      throw new IllegalArgumentException(
          "Valid serial number is required in ChangeOrderIdentifierRequests.");
    }

    if (!hasValidAssetId) {
      LOG.warn("Valid asset ID is required in ChangeOrderIdentifierRequests.");
      Metrics.emit(METRIC_PREFIX + TARGET_ASSETS_ID_VALIDATION_FAILURE, 1d);
      throw new IllegalArgumentException(
          "Valid asset ID is required in ChangeOrderIdentifierRequests.");
    }
  }

  private void validateChangeOrderType(ChangeOrderType changeOrderType) {
    if (changeOrderType == null) {
      LOG.warn("ChangeOrderType cannot be null.");
      Metrics.emit(METRIC_PREFIX + CHANGE_ORDER_TYPE_VALIDATION_FAILURE, 1d);
      throw new IllegalArgumentException("ChangeOrderType cannot be null.");
    }
  }

  private void validatePriority(Integer priority) {
    if (priority == null || priority < 1 || priority > 4) {
      LOG.warn("New orders must have a priority from 1 to 4");
      Metrics.emit(METRIC_PREFIX + PRIORITY_VALIDATION_FAILURE, 1d);
      throw new IllegalArgumentException("New orders must have a priority from 1 to 4");
    }
  }

  private void checkForExistingActiveOrder(
      String serialNumber, RmcId targetAssetId, ChangeOrderType changeOrderType) {
    if (changeOrderType == ChangeOrderType.MULTI_REPAIR) {
      return;
    }
    List<ChangeOrder> existingOrders =
        changeOrderRepository.findActiveChangeOrderBySerialNumber(
            serialNumber, targetAssetId, null, TERMINAL_STATES);
    boolean hasConflict =
        existingOrders.stream()
            .anyMatch(
                order ->
                    order.type() != ChangeOrderType.MULTI_REPAIR && order.state() != VALIDATED);

    if (hasConflict) {
      LOG.warn("An active repair order already exists for serial number {}", serialNumber);
      throw new IllegalArgumentException(
          "An active repair order already exists for this serial number.");
    }
  }

  private String mapChangeOrderTypeToSummary(ChangeOrderType type) {
    return switch (type) {
      case E5_SERVER_REPAIR -> "E5 Server Repair";
      case SERVER_REPAIR -> "Server Repair";
      case GB200_SERVER_REPAIR -> "GB200 Server Repair";
      case GB200_POWER_SHELF_REPAIR -> "GB200 Power Shelf Repair";
      case GB200_POWER_SUPPLY_UNIT_REPAIR -> "GB200 Power Supply Unit Repair";
      case GB200_POWER_MANAGEMENT_CONTROLLER_REPAIR -> "GB200 Power Management Controller Repair";
      case GB200_RACK_REPAIR -> "GB200 Rack Repair";
      case MANAGEMENT_SWITCH_REPAIR -> "Management Switch Repair";
      case NVSWITCH_REPAIR -> "NvSwitch Repair";
      case NETWORK_LINK_REPAIR -> "Network Link Repair";
      case DISK_REPAIR -> "Disk Repair";
      case GB200_DISK_REPAIR -> "GB200 Disk Repair";
      case E5_RPDU_REPAIR -> "E5 RPDU Repair";
      case RACK_INGESTION -> "Rack Ingestion";
      case NETWORK_LEAF_SPINE_LINK_REPAIR -> "Network Leaf Spine Link Repair";
      case NETWORK_GENERIC_LINK_REPAIR -> "Network Leaf Genric Link Repair";
      case NETWORK_DEVICE_REPAIR -> "Network Device Repair";
      case MULTI_REPAIR -> "Multi Repair";
      default -> throw new IllegalArgumentException("Invalid change order type: " + type);
    };
  }

  private void validateStateTransition(ChangeOrderState exitState, ChangeOrderState enterState) {
    if (exitState.isTerminal()) {
      LOG.warn("State transition failed due to the current state {} is terminal", exitState);
      throw new IllegalArgumentException(
          "State transition failed due to the current state %s is terminal"
              .formatted(exitState.name()));
    }

    if (!STATE_TRANSITION_MAP.containsKey(exitState)
        || !STATE_TRANSITION_MAP.get(exitState).contains(enterState)) {
      LOG.warn("State transition from {} to {} is not allowed", exitState, enterState);
      throw new IllegalArgumentException(
          "State transition from %s to %s is not allowed"
              .formatted(exitState.name(), enterState.name()));
    }
  }

  @Override
  @Transactional
  public List<ChangeOrder> findReopenChain(RmcId headChangeOrderId) {
    ChangeOrder head =
        changeOrderRepository
            .findById(headChangeOrderId)
            .orElseThrow(
                () ->
                    new IllegalArgumentException(
                        "The requested resource is not authorized or not found."));
    if (head.previousChangeOrderId() != null) {
      throw new IllegalArgumentException("Provided order is not a head order, can't find chain.");
    }

    PaginatedList<ChangeOrder> pageableReopenedOrders =
        changeOrderRepository.listByRepairOrderIdLimited(
            headChangeOrderId,
            PageQuery.fromEmptyToken(
                REPAIR_ACTION_LIMIT_NUMBER, PageQuery.SortOrder.DESC, "timeCreated"));
    List<ChangeOrder> reopenedOrders = pageableReopenedOrders.getItems();
    if (reopenedOrders == null || reopenedOrders.isEmpty()) {
      reopenedOrders = List.of();
    } else {
      reopenedOrders.sort(Comparator.comparing(ChangeOrder::timeCreated));
    }
    List<ChangeOrder> chain = new ArrayList<>();
    chain.add(head);
    chain.addAll(reopenedOrders);

    return chain;
  }

  @Override
  public ChangeOrder assembleReopenedChangeOrder(
      ChangeOrder tailChangeOrder, ChangeOrder headChangeOrder) {
    LOG.info(
        "Assemble reopened change order. Head Order ID: {}, Tail Order ID: {}",
        headChangeOrder.id(),
        tailChangeOrder.id());
    return tailChangeOrder
        .withId(headChangeOrder.id())
        .withTimeCreated(headChangeOrder.timeCreated());
  }

  @Override
  public List<ChangeOrderNodeImpact> getChangeOrderNodeImpactByChangeOrderIds(
      List<RmcId> changeOrderIds) {
    return changeOrderNodeImpactRepository.findByChangeOrderIds(changeOrderIds);
  }

  @Override
  public List<ChangeOrderAssetReservation> getChangeOrderAssetReservationsByChangeOrderIds(
      List<RmcId> changeOrderIds) {
    return changeOrderAssetReservationRepository.findByChangeOrderIds(changeOrderIds);
  }

  @Override
  public List<ChangeOrderIdentifier> getIdentifierListByChangeOrderIds(List<RmcId> changeOrderIds) {
    return changeOrderIdentifierRepository.findByChangeOrderIds(changeOrderIds);
  }

  @Override
  public List<ChangeOrderNeedsAttentionDetails> getNeedsAttentionDetailsByChangeOrderIds(
      List<RmcId> changeOrderIds) {
    return changeOrderNeedsAttentionDetailRepository.findNeedsAttentionDetailsByChangeOrderIds(
        changeOrderIds, NeedsAttentionLifecycleState.OPEN.name(), null);
  }

  @Override
  @Transactional
  public void approveRepairOrder(RmcId repairOrderId, String updatedBy, boolean isExternalRequest) {
    ChangeOrder headOrder =
        changeOrderRepository
            .findById(repairOrderId)
            .orElseThrow(() -> new IllegalArgumentException(RESOURCE_NOT_AUTHORIZED_OR_NOT_FOUND));

    if (headOrder.previousChangeOrderId() != null) {
      throw new IllegalArgumentException("The provided order is not a head order.");
    }

    ChangeOrder tailChangeOrder =
        (headOrder.tailChangeOrderId() == null)
            ? headOrder
            : changeOrderRepository
                .findById(headOrder.tailChangeOrderId())
                .orElseThrow(
                    () -> new IllegalArgumentException(RESOURCE_NOT_AUTHORIZED_OR_NOT_FOUND));

    prohibitInternalOperationOnCustomerRepair(isExternalRequest, tailChangeOrder, "approve");

    // Approval API is currently unused since approval will be handled via the UI.
    // Support for network‑repair orders will be added soon.
    if (!tailChangeOrder.source().equals(BURNINATOR)
        || tailChangeOrder.type().equals(ChangeOrderType.MULTI_REPAIR)) {
      throw new IllegalArgumentException(
          "ApproveRepairOrder is not supported for the current repair order.");
    }

    if (tailChangeOrder.timeApproved() != null) {
      LOG.warn(
          "Change order {} is already approved at {}.",
          tailChangeOrder.id(),
          tailChangeOrder.timeApproved());
      return;
    }
    // If the order is not in a terminal state, update the approval time (the state will later
    // transition automatically to ACTIONABLE).
    Instant now = Instant.now();
    if (!tailChangeOrder.state().isTerminal()) {
      ChangeOrder updatedTail = tailChangeOrder.withTimeUpdated(now).withTimeApproved(now);
      update(updatedTail, updatedBy);
    }

    List<ChangeOrderNeedsAttentionDetails> openNeedsAttentionDetails =
        changeOrderNeedsAttentionDetailRepository.findNeedsAttentionDetailsByChangeOrderIds(
            List.of(tailChangeOrder.id()),
            NeedsAttentionLifecycleState.OPEN.name(),
            NeedsAttentionType.APPROVAL.name());

    // If no open APPROVAL NAD records are found, skip further approval processing
    if (openNeedsAttentionDetails.isEmpty()) {
      // TODO: For internally created network repair orders, consider allowing approval even if
      // there are no open NeedsAttention records.
      LOG.warn(
          "No open NeedsAttention details with APPROVAL type found for change order {}. Approval is"
              + " skipped.",
          tailChangeOrder.id());
      return;
    }

    if (openNeedsAttentionDetails.size() > OPEN_NEEDS_ATTENTION_DETAILS_LIMIT) {
      LOG.warn(
          "Change order {} has {} open NeedsAttention details; expected only one.",
          tailChangeOrder.id(),
          openNeedsAttentionDetails.size());
    }

    for (ChangeOrderNeedsAttentionDetails detail : openNeedsAttentionDetails) {
      ChangeOrderNeedsAttentionDetails updated =
          detail.withState(NeedsAttentionLifecycleState.CLOSED);
      changeOrderNeedsAttentionDetailRepository.update(updated);
    }
  }

  @Override
  public Optional<ChangeOrderViewRecord> getChangeOrderViewRecord(RmcId changeOrderId) {
    return changeOrderViewRepository.queryView(changeOrderId);
  }

  @Override
  public List<ChangeOrderAssetReservation> listAssetReservationBySpareSerial(
      String spareSerialNumber) {
    return changeOrderAssetReservationRepository.findBySpareSerialNumber(spareSerialNumber);
  }

  private void populateAssetsToAction(CreateChangeOrder createChangeOrder, RmcId changeOrderId) {
    // Populate Asset To Action Table
    LOG.info(
        "Populating assets to action table for repair order type {} with the list of"
            + " AssetsToActions {} ",
        createChangeOrder.getChangeOrderType(),
        createChangeOrder.getAssetsToActions().toString());

    List<AssetsToAction> assetsToActions = new ArrayList<>();
    for (AssetsToActionRequest assetsToActionRequest : createChangeOrder.getAssetsToActions()) {
      validateAssetsToActionRequest(assetsToActionRequest);
      assetsToActions.add(
          new AssetsToAction(
              changeOrderId,
              assetsToActionRequest.getFailedAssetId(),
              assetsToActionRequest.getRackInstanceId().toString(),
              assetsToActionRequest.getRackNumber(),
              assetsToActionRequest.getElevation(),
              assetsToActionRequest.getVerified(),
              assetsToActionRequest.getAssetReservationId(),
              assetsToActionRequest.getTimeUpdated(),
              assetsToActionRequest.getVersion(),
              assetsToActionRequest.getPartId()));
    }
    if (!assetsToActions.isEmpty()) {
      assetsToActionRepository.saveAll(assetsToActions);
    }
    LOG.info("Successfully populated Assets To Action!");
  }

  private void validateAssetsToActionRequest(AssetsToActionRequest request) {
    List<String> missingFields = new ArrayList<>();
    if (request.getFailedAssetId() == null) {
      missingFields.add("FAILED_ASSET_ID");
    }
    if (request.getRackInstanceId() == null) {
      missingFields.add("RACK_ID");
    }
    if (request.getRackNumber() == null || request.getRackNumber().isBlank()) {
      missingFields.add("RACK_POSITION");
    }
    if (request.getElevation() == null || request.getElevation().isBlank()) {
      missingFields.add("ELEVATION");
    }

    if (!missingFields.isEmpty()) {
      throw new IllegalArgumentException(
          "AssetsToActionRequest is missing required field(s): "
              + String.join(", ", missingFields));
    }
  }

  @Override
  public TicketData getTicketData(RmcId changeOrderId) {
    return ticketDataRepository.get(changeOrderId);
  }

  @Override
  public List<TicketField> getTicketFields(RmcId changeOrderId) {
    return ticketFieldRepository.getTicketFields(changeOrderId);
  }

  private void validateCompartmentAndRegion(String compartmentId, String region) {
    if (StringUtils.isBlank(compartmentId)) {
      throw new IllegalArgumentException("Invalid input. CompartmentId cannot be empty.");
    }

    if (StringUtils.isBlank(region)) {
      throw new IllegalArgumentException("Invalid input. Region cannot be empty.");
    }
  }

  private List<ChangeOrderNodeImpact> buildNodeImpacts(
      CreateChangeOrder req, RmcId changeOrderId, Instant now) {
    if (req.getChangeOrderNodeImpact() == null) {
      return List.of();
    }
    return ChangeOrderNodeImpactMapper.mapChangeOrderNodeImpactRequestToChangeOrderNodeImpact(
        req.getChangeOrderNodeImpact(), changeOrderId, now);
  }

  @Override
  @Transactional
  public void backfillRepairOrderCompartment() {
    LOG.info(
        "Running back-fill for compartmentId (internal={}, external={})",
        internalCompartmentId,
        externalCompartmentId);

    List<String> externalSourceTypes =
        Arrays.stream(ChangeOrderSourceType.values())
            .filter(src -> !src.isInternal())
            .map(Enum::name)
            .toList();

    changeOrderRepository.backfillCompartmentIds(
        internalCompartmentId, externalCompartmentId, externalSourceTypes);
    LOG.info("Back-fill completed.");
  }

  @Override
  public Optional<ChangeOrderIdentifier> getIdentifierByChangeOrderIdAndIdentifierKey(
      RmcId changeOrderId, ChangeOrderIdentifierKey changeOrderIdentifierKey) {
    return changeOrderIdentifierRepository.fetchByChangeOrderAndIdentifier(
        changeOrderId, changeOrderIdentifierKey.getKey());
  }

  @Override
  @Transactional
  public void purgeCanaryChangeOrders(@NonNull String region, @Nullable RmcId repairOrderId) {
    if (StringUtils.isBlank(region)) {
      throw new IllegalArgumentException("Region cannot be null for Canary data cleanup.");
    }
    if (PROD_ASSET_REGION.equalsIgnoreCase(region) || region.equalsIgnoreCase(defaultRegion)) {
      throw new IllegalArgumentException(
          "Region " + region + " is not allowed for purge operation");
    }

    List<String> changeOrderIdsToPurge;

    if (repairOrderId == null) {
      changeOrderIdsToPurge = changeOrderRepository.findChangeOrderIdsByRegion(region);
      if (changeOrderIdsToPurge.isEmpty()) {
        LOG.info("No changeOrders found for region {} – nothing to purge.", region);
        return;
      }
      changeOrderRepository.purgeByRegion(region);
    } else {
      changeOrderIdsToPurge =
          findReopenChain(repairOrderId).stream()
              .filter(co -> region.equalsIgnoreCase(co.region()))
              .map(co -> co.id().toString())
              .distinct()
              .collect(Collectors.toList());

      if (changeOrderIdsToPurge.isEmpty()) {
        LOG.info(
            "No changeOrders in region {} found for repairOrder {} – nothing to purge.",
            region,
            repairOrderId);
        return;
      }
      changeOrderRepository.purgeByRegionAndRepairOrderId(region, changeOrderIdsToPurge);
    }

    assetsToActionRepository.purgeByChangeOrderIds(changeOrderIdsToPurge);

    for (String coId : changeOrderIdsToPurge) {
      changeOrderRepository.removeAssetReservationsFromCoar(coId);
      changeOrderRepository.purgeDcmsAssetReservation(coId);
    }

    LOG.info(
        "Purged changeOrders {} and related cm_assets_to_action, cm_change_order_asset_reservation,"
            + " and dcms_asset_reservation rows for region {}",
        changeOrderIdsToPurge,
        region);
  }

  @Override
  @SuppressWarnings("PMD.AvoidReassigningParameters")
  public PaginatedList<ChangeCalendarEvent> listInternalChangeCalendarEvents(
      List<String> changeCalendarEventTypes,
      String changeCalendarEventId,
      String dataHallName,
      ZonedDateTime timeStartGreaterThanOrEqualTo,
      ZonedDateTime timeEndLessThan,
      TokenPageQuery pageQuery) {
    LOG.info("Fetching internal change calendar events");

    if (Strings.isNullOrEmpty(dataHallName)) {
      throw new IllegalArgumentException("DataHall name cannot be empty.");
    }

    Instant startTime =
        timeStartGreaterThanOrEqualTo == null
            ? ZonedDateTime.now().toInstant()
            : timeStartGreaterThanOrEqualTo.toInstant();
    Instant endTime = timeEndLessThan == null ? startTime : timeEndLessThan.toInstant();

    if (changeCalendarEventTypes == null || changeCalendarEventTypes.isEmpty()) {
      changeCalendarEventTypes = CHANGE_CALENDAR_EVENT_FREEZE_STATES;
    }

    return changeCalendarEventRepository.findInternalCalendarEvents(
        changeCalendarEventTypes,
        changeCalendarEventId,
        dataHallName,
        startTime,
        endTime,
        pageQuery);
  }

  @Override
  public PaginatedList<ChangeOrder> listNonTerminalChangeOrdersForProbe(
      ListFilters filters, TokenPageQuery pageToken) {
    String serialNumber = filters.deviceSerialNumber();
    String interfaceA = filters.interfaceA();
    String interfaceB = filters.interfaceB();
    String logicalTargetA = filters.logicalTargetA();
    String logicalTargetB = filters.logicalTargetB();
    boolean hasInterfaceFilters =
        interfaceA != null
            || interfaceB != null
            || logicalTargetA != null
            || logicalTargetB != null;

    if (serialNumber != null && hasInterfaceFilters) {
      throw new IllegalArgumentException(
          "Cannot filter by both serialNumber and interface/logicalTarget parameters at the same"
              + " time.");
    }

    if (serialNumber == null && !hasInterfaceFilters) {
      throw new IllegalArgumentException(
          "Must provide either serialNumber or at least one interface/logicalTarget filter.");
    }

    if (serialNumber != null) {
      return changeOrderRepository.findNonTerminalBySerialNumber(
          serialNumber, TERMINAL_STATES, pageToken);
    }
    return changeOrderRepository.findNonTerminalByInterfaceKeys(
        interfaceA, interfaceB, logicalTargetA, logicalTargetB, TERMINAL_STATES, pageToken);
  }

  @Override
  public ChangeOrder updateRepairOrderTicketDetails(
      RmcId repairOrderId, String ticketId, String ticketUrl) {
    LOG.info("Updating ticketDetails for all the ChangeOrder of RepairOrder {}", repairOrderId);
    List<ChangeOrder> chain = findReopenChain(repairOrderId);
    ChangeOrder head =
        chain.stream()
            .filter(ChangeOrder::isHead)
            .findFirst()
            .orElseThrow(
                () ->
                    new IllegalArgumentException(
                        "Head not found for repairOrder " + repairOrderId));
    String id = ticketId == null ? head.ticketId() : ticketId;
    String url = ticketUrl == null ? head.ticketUrl() : ticketUrl;
    if (id == null || url == null) {
      LOG.warn(
          "No ticket details for repairOrder {}, ticketId: {}, ticketUrl: {}",
          repairOrderId,
          id,
          url);
      return head;
    }
    if (!(id.equals(head.ticketId()) && url.equals(head.ticketUrl()))) {
      LOG.info(
          "Resetting head ChangeOrder\n  {}\nreplaces\n  {}\n  {}\nreplaces\n  {}",
          id,
          head.ticketId(),
          url,
          head.ticketUrl());
    }
    chain.forEach(
        changeOrder -> {
          LOG.info(
              "Updating ticketId {} and ticketUrl {} for change order {}, repairOrderId: {}",
              id,
              url,
              changeOrder.id(),
              changeOrder.repairOrderId());
          ChangeOrder co =
              changeOrder
                  .withTicketId(id)
                  .withTicketUrl(url)
                  .withAssigneeEmail(null)
                  .withReason(null)
                  .withTimeUpdated(Instant.now());
          update(co, CM_UPDATED_BY_STRING);
        });
    return head;
  }

  @Override
  public Optional<ChangeOrder> getByChangeOrderId(RmcId changeOrderId) {
    return changeOrderRepository.findById(changeOrderId);
  }

  @Override
  public PaginatedList<ChangeOrder> listTerminalOrdersNeedingTaskCleanup(
      List<String> taskStates, TokenPageQuery pageToken) {

    return changeOrderRepository.findTerminalOrdersNeedingTaskCleanup(taskStates, pageToken);
  }

  @Override
  public List<String> listOldSerialNotInValidatedStates(
      @NonNull List<ChangeOrderState> nonValidatedStates) {
    return changeOrderRepository.findOldSerialNotInValidatedStates(
        nonValidatedStates.stream().map(ChangeOrderState::name).collect(Collectors.toList()));
  }

  @Override
  public List<String> listNewSerialNotInValidatedStates(
      @NonNull List<ChangeOrderState> nonValidatedStates) {
    return changeOrderRepository.findNewSerialNotInValidatedStates(
        nonValidatedStates.stream().map(ChangeOrderState::name).collect(Collectors.toList()));
  }

  private void prohibitInternalOperationOnCustomerRepair(
      boolean isExternalRequest, ChangeOrder tailChangeOrder, String operation) {
    prohibitInternalOperationOnCustomerRepair(
        isExternalRequest,
        tailChangeOrder.isSharedWithCustomer(),
        tailChangeOrder.source(),
        operation);
  }

  private void prohibitInternalOperationOnCustomerRepair(
      boolean isExternalRequest,
      boolean isSharedWithCustomer,
      ChangeOrderSourceType sourceType,
      String operation) {
    if (!isExternalRequest && isSharedWithCustomer && sourceType.equals(BURNINATOR)) {
      throw new IllegalArgumentException(String.format(RESOURCE_INVALID_STATE, operation));
    }
  }

  @Override
  public PaginatedList<ChangeOrder> listHandleHandedOverEligibleChangeOrders(
      List<ChangeOrderType> changeOrderTypes,
      List<ChangeOrderState> nonTerminalChangeOrderStates,
      TokenPageQuery pageToken) {
    return changeOrderRepository.listHandleHandedOverEligibleChangeOrders(
        changeOrderTypes.stream().map(ChangeOrderType::name).collect(Collectors.toList()),
        nonTerminalChangeOrderStates.stream()
            .map(ChangeOrderState::name)
            .collect(Collectors.toList()),
        pageToken);
  }
}
