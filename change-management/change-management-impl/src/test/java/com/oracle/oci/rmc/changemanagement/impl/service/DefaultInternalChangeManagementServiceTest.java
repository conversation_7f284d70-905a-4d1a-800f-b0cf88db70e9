package com.oracle.oci.rmc.changemanagement.impl.service;

import static com.oracle.oci.rmc.changemanagement.api.model.common.ChangeOrderIdentifierKey.RACK_SERIAL_NUMBER;
import static com.oracle.oci.rmc.changemanagement.api.model.common.ChangeOrderIdentifierKey.SERIAL_NUMBER;
import static com.oracle.oci.rmc.changemanagement.api.model.common.ChangeOrderIdentifierKey.TARGET_ASSET_ID;
import static com.oracle.oci.rmc.changemanagement.impl.dal.RepositoryTestUtil.TEST_COMPARTMENT_ID;
import static com.oracle.oci.rmc.changemanagement.impl.dal.RepositoryTestUtil.TEST_REGION;
import static java.util.Collections.emptySet;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.oracle.oci.rmc.auditevent.dal.AuditEvent;
import com.oracle.oci.rmc.auditevent.service.AuditEventService;
import com.oracle.oci.rmc.changemanagement.api.model.common.AssetsToActionRequest;
import com.oracle.oci.rmc.changemanagement.api.model.common.ChangeManagerCommandType;
import com.oracle.oci.rmc.changemanagement.api.model.common.ChangeOrderIdentifierKey;
import com.oracle.oci.rmc.changemanagement.api.model.common.ChangeOrderIdentifierRequest;
import com.oracle.oci.rmc.changemanagement.api.model.common.ChangeOrderNodeImpactRequestBuilder;
import com.oracle.oci.rmc.changemanagement.api.model.common.ChangeOrderSourceType;
import com.oracle.oci.rmc.changemanagement.api.model.common.ChangeOrderState;
import com.oracle.oci.rmc.changemanagement.api.model.common.ChangeOrderSummaryDto;
import com.oracle.oci.rmc.changemanagement.api.model.common.ChangeOrderType;
import com.oracle.oci.rmc.changemanagement.api.model.common.ChangeTaskState;
import com.oracle.oci.rmc.changemanagement.api.model.common.CreateChangeOrder;
import com.oracle.oci.rmc.changemanagement.api.model.common.CreateRepairOrder;
import com.oracle.oci.rmc.changemanagement.api.model.common.CreateRepairOrderBuilder;
import com.oracle.oci.rmc.changemanagement.api.model.common.EffectiveState;
import com.oracle.oci.rmc.changemanagement.api.model.common.NeedsAttentionLifecycleState;
import com.oracle.oci.rmc.changemanagement.api.model.common.NeedsAttentionType;
import com.oracle.oci.rmc.changemanagement.api.model.common.ProbeState;
import com.oracle.oci.rmc.changemanagement.api.model.common.ProbeType;
import com.oracle.oci.rmc.changemanagement.api.model.common.RackInstanceAccessLevel;
import com.oracle.oci.rmc.changemanagement.api.model.common.RepairOperation;
import com.oracle.oci.rmc.changemanagement.api.model.common.TicketData;
import com.oracle.oci.rmc.changemanagement.api.model.common.TicketField;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeOrder;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeOrderAssetReservation;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeOrderAssetReservationId;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeOrderBuilder;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeOrderDetails;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeOrderIdentifier;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeOrderIdentifierBuilder;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeOrderNeedsAttentionDetails;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeOrderNodeImpact;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeTask;
import com.oracle.oci.rmc.changemanagement.api.model.entity.Probe;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ProbeResult;
import com.oracle.oci.rmc.changemanagement.api.model.entity.TicketFieldEntity;
import com.oracle.oci.rmc.changemanagement.api.service.ChangeManagementService;
import com.oracle.oci.rmc.changemanagement.api.service.InternalChangeManagementService;
import com.oracle.oci.rmc.changemanagement.impl.dal.ChangeOrderAssetReservationRepository;
import com.oracle.oci.rmc.changemanagement.impl.dal.ChangeOrderIdentifierRepository;
import com.oracle.oci.rmc.changemanagement.impl.dal.ChangeOrderNeedsAttentionDetailRepository;
import com.oracle.oci.rmc.changemanagement.impl.dal.ChangeOrderNodeImpactRepository;
import com.oracle.oci.rmc.changemanagement.impl.dal.ChangeOrderRepository;
import com.oracle.oci.rmc.changemanagement.impl.dal.ChangeTaskRepository;
import com.oracle.oci.rmc.changemanagement.impl.dal.OtsAssignmentRepository;
import com.oracle.oci.rmc.changemanagement.impl.dal.ProbeRepository;
import com.oracle.oci.rmc.changemanagement.impl.dal.ProbeResultRepository;
import com.oracle.oci.rmc.changemanagement.impl.dal.TicketFieldFactory;
import com.oracle.oci.rmc.changemanagement.impl.dal.TicketFieldRepository;
import com.oracle.oci.rmc.changemanagement.impl.internal.ChangeManagerCommandService;
import com.oracle.oci.rmc.model.RmcId;
import com.oracle.oci.rmc.model.RmcIdType;
import com.oracle.oci.sfw.micronaut.http.pagination.PageQuery;
import com.oracle.oci.sfw.micronaut.http.pagination.PaginatedList;
import io.micronaut.context.annotation.Requires;
import io.micronaut.test.annotation.TransactionMode;
import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import jakarta.inject.Inject;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;
import org.assertj.core.api.AutoCloseableSoftAssertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

@MicronautTest(transactionMode = TransactionMode.SINGLE_TRANSACTION)
@Requires(property = "database.type", value = "ORACLE")
public class DefaultInternalChangeManagementServiceTest {
  private static final String TEST_USER = "test-user";
  private static final String RACK_SERIAL_NUMBER1 = "test_rack_serial_number";
  public static final String RESOURCE_INVALID_STATE =
      "Cannot %s the requested resource because it has already been shared with the customer.";

  private static final List<TicketField> TICKET_FIELDS =
      List.of(TicketFieldFactory.COMPONENT, TicketFieldFactory.ITEM);
  private static final TicketData TICKET_DATA =
      TicketData.key(TicketFieldFactory.COMPONENT.name())
          .value("Component A")
          .key(TicketFieldFactory.ITEM.name())
          .value("Item 1")
          .build();

  @Inject private ChangeOrderRepository changeOrderRepository;

  @Inject protected InternalChangeManagementService internalChangeManagementService;
  @Inject protected AuditEventService auditEventService;
  @Inject private ChangeManagerCommandService changeManagerCommandService;
  @Inject private ProbeRepository probeRepository;
  @Inject private ProbeResultRepository probeResultRepository;
  @Inject private TicketFieldRepository ticketFieldRepository;
  @Inject private ChangeOrderAssetReservationRepository changeOrderAssetReservationRepository;
  @Inject private ChangeOrderIdentifierRepository changeOrderIdentifierRepository;
  @Inject private ChangeTaskRepository changeTaskRepository;
  @Inject private OtsAssignmentRepository otsAssignmentRepository;
  @Inject private ChangeOrderNodeImpactRepository changeOrderNodeImpactRepository;

  @Inject
  private ChangeOrderNeedsAttentionDetailRepository changeOrderNeedsAttentionDetailRepository;

  private RmcId changeOrderId;
  private RmcId assetId;

  @BeforeEach
  void setup() {
    changeOrderId = RmcId.generate(RmcIdType.CM.CHANGE_ORDER);
    assetId = RmcId.generate(RmcIdType.Inventory.ASSET);
  }

  protected ChangeOrder createTestOrder(
      String serialNumber, boolean isSharedWithCustomer, ChangeOrderType type) {
    return createTestOrder(serialNumber, isSharedWithCustomer, ro -> ro.repairOrderType(type));
  }

  protected ChangeOrder createTestOrder(String serialNumber, ChangeOrderType type) {
    return createTestOrder(serialNumber, true, ro -> ro.repairOrderType(type));
  }

  protected ChangeOrder createTestOrder(Consumer<CreateRepairOrderBuilder> setters) {
    return createTestOrder(null, true, setters);
  }

  protected ChangeOrder createTestOrder(
      String serialNumber,
      boolean isSharedWithCustomer,
      Consumer<CreateRepairOrderBuilder> setters) {
    if (serialNumber == null) {
      serialNumber = "sn-" + System.currentTimeMillis();
    }
    CreateRepairOrderBuilder builder =
        CreateRepairOrderBuilder.builder()
            .targetAssetId(assetId)
            .deviceSerialNumber(serialNumber)
            .details(new ChangeOrderDetails(null, null, null, null))
            .priority(1)
            .accessLevel(RackInstanceAccessLevel.REGULAR)
            .repairOrderType(ChangeOrderType.GB200_SERVER_REPAIR) // overriddent by
            .ticketFields(TICKET_FIELDS)
            .ticketData(TICKET_DATA)
            .identifierRequests(
                List.of(
                    new ChangeOrderIdentifierRequest(SERIAL_NUMBER.getKey(), serialNumber, null),
                    new ChangeOrderIdentifierRequest(
                        TARGET_ASSET_ID.getKey(), assetId.toString(), null),
                    new ChangeOrderIdentifierRequest(
                        RACK_SERIAL_NUMBER.getKey(), RACK_SERIAL_NUMBER1, null)))
            .assetsToActions(
                List.of(
                    new AssetsToActionRequest(
                        assetId,
                        "RN-001",
                        RmcId.generate(RmcIdType.DCMS.RACK_INSTANCE),
                        RmcId.generate(RmcIdType.DCMS.RACK_POSITION),
                        "Elevation-1U",
                        "Y",
                        null,
                        Instant.now(),
                        0L,
                        RmcId.generate(RmcIdType.Catalog.PART))))
            .compartmentId(TEST_COMPARTMENT_ID)
            .changeOrderNodeImpactRequests(
                List.of(
                    ChangeOrderNodeImpactRequestBuilder.builder()
                        .nodeAssetId(assetId)
                        .nodeSerialNumber(serialNumber)
                        .dataHallId(RmcId.generate(RmcIdType.Inventory.DATA_HALL))
                        .effectiveState(EffectiveState.CREATION)
                        .rackType("Server")
                        .rackPositionId(RmcId.generate(RmcIdType.DCMS.RACK_POSITION))
                        .build(),
                    ChangeOrderNodeImpactRequestBuilder.builder()
                        .nodeAssetId(assetId)
                        .nodeSerialNumber(serialNumber)
                        .dataHallId(RmcId.generate(RmcIdType.Inventory.DATA_HALL))
                        .effectiveState(EffectiveState.CREATION)
                        .rackType("Server")
                        .rackPositionId(RmcId.generate(RmcIdType.DCMS.RACK_POSITION))
                        .build()))
            .region(TEST_REGION);

    setters.accept(builder);
    CreateRepairOrder createRepairOrder = builder.build();
    return internalChangeManagementService.create(
        createRepairOrder, ChangeOrderSourceType.DEFAULT, isSharedWithCustomer);
  }

  protected ChangeOrder createTestOrderWithDiagnosticOutput(
      String deviceSerialNumber, String diagnosticProbeOutput, String rackSerialNumber) {
    CreateRepairOrder createRepairOrder =
        CreateRepairOrder.targetAssetId(assetId)
            .diagnosticProbeOutput(diagnosticProbeOutput)
            .labels(List.of())
            .deviceSerialNumber(deviceSerialNumber)
            .details(new ChangeOrderDetails(null, null, null, null))
            .priority(1)
            .accessLevel(RackInstanceAccessLevel.REGULAR)
            .repairOrderType(ChangeOrderType.GB200_SERVER_REPAIR)
            .identifierRequests(
                List.of(
                    new ChangeOrderIdentifierRequest(SERIAL_NUMBER.getKey(), "SN8001", null),
                    new ChangeOrderIdentifierRequest(
                        TARGET_ASSET_ID.getKey(), assetId.toString(), null),
                    new ChangeOrderIdentifierRequest(
                        RACK_SERIAL_NUMBER.getKey(), rackSerialNumber, null)))
            .assetsToActions(
                List.of(
                    new AssetsToActionRequest(
                        assetId,
                        "RN-001",
                        RmcId.generate(RmcIdType.DCMS.RACK_INSTANCE),
                        RmcId.generate(RmcIdType.DCMS.RACK_POSITION),
                        "Elevation-1U",
                        "Y",
                        null,
                        Instant.now(),
                        0L,
                        RmcId.generate(RmcIdType.Catalog.PART))))
            .compartmentId(TEST_COMPARTMENT_ID)
            .region(TEST_REGION)
            .build();
    return internalChangeManagementService.create(
        createRepairOrder, ChangeOrderSourceType.DEFAULT, true);
  }

  protected ChangeOrder createTestWithSharedFieldOrder(
      String serialNumber,
      ChangeOrderType type,
      ChangeOrderSourceType sourceType,
      boolean isSharedWithCustomer) {
    CreateRepairOrder createRepairOrder =
        CreateRepairOrderBuilder.builder()
            .targetAssetId(assetId)
            .deviceSerialNumber(serialNumber)
            .details(new ChangeOrderDetails(null, null, null, null))
            .priority(1)
            .repairOrderType(type)
            .identifierRequests(
                List.of(
                    new ChangeOrderIdentifierRequest(SERIAL_NUMBER.getKey(), serialNumber, null),
                    new ChangeOrderIdentifierRequest(
                        TARGET_ASSET_ID.getKey(), assetId.toString(), null),
                    new ChangeOrderIdentifierRequest(
                        RACK_SERIAL_NUMBER.getKey(), RACK_SERIAL_NUMBER1, null)))
            .assetsToActions(
                List.of(
                    new AssetsToActionRequest(
                        assetId,
                        "RN-001",
                        RmcId.generate(RmcIdType.DCMS.RACK_INSTANCE),
                        RmcId.generate(RmcIdType.DCMS.RACK_POSITION),
                        "Elevation-1U",
                        "Y",
                        null,
                        Instant.now(),
                        0L,
                        RmcId.generate(RmcIdType.Catalog.PART))))
            .compartmentId(TEST_COMPARTMENT_ID)
            .region(TEST_REGION)
            .build();
    return internalChangeManagementService.create(
        createRepairOrder, sourceType, isSharedWithCustomer);
  }

  protected ChangeOrder createTestOrderWithProtectedRack(
      String serialNumber,
      ChangeOrderType type,
      RackInstanceAccessLevel accessLevel,
      String rackSerialNumber) {
    CreateRepairOrder createRepairOrder =
        CreateRepairOrderBuilder.builder()
            .targetAssetId(assetId)
            .deviceSerialNumber(serialNumber)
            .details(new ChangeOrderDetails(null, null, null, "https://example.com"))
            .priority(1)
            .accessLevel(accessLevel)
            .repairOrderType(type)
            .identifierRequests(
                List.of(
                    new ChangeOrderIdentifierRequest(SERIAL_NUMBER.getKey(), serialNumber, null),
                    new ChangeOrderIdentifierRequest(
                        TARGET_ASSET_ID.getKey(), assetId.toString(), null),
                    new ChangeOrderIdentifierRequest(
                        RACK_SERIAL_NUMBER.getKey(), rackSerialNumber, null)))
            .assetsToActions(
                List.of(
                    new AssetsToActionRequest(
                        assetId,
                        "RN-001",
                        RmcId.generate(RmcIdType.DCMS.RACK_INSTANCE),
                        RmcId.generate(RmcIdType.DCMS.RACK_POSITION),
                        "Elevation-1U",
                        "Y",
                        null,
                        Instant.now(),
                        0L,
                        RmcId.generate(RmcIdType.Catalog.PART))))
            .compartmentId(TEST_COMPARTMENT_ID)
            .region(TEST_REGION)
            .build();
    return internalChangeManagementService.create(
        createRepairOrder, ChangeOrderSourceType.DEFAULT, true);
  }

  private List<AuditEvent> listAuditEventsByEventName(String eventName) {
    return auditEventService
        .listAuditEvents(eventName, null, null, null, PageQuery.fromEmptyToken(10))
        .getResults();
  }

  @Test
  void createMultipleChangeOrdersAndVerifyListing() {
    ChangeOrder order1 = createTestOrder("SN1001", ChangeOrderType.GB200_DISK_REPAIR);
    ChangeOrder order2 = createTestOrder("SN1002", ChangeOrderType.NVSWITCH_REPAIR);

    assertEquals(order1.severity(), ChangeOrderType.GB200_DISK_REPAIR.getDefaultSeverity());
    assertEquals(order2.severity(), ChangeOrderType.NVSWITCH_REPAIR.getDefaultSeverity());

    PaginatedList<ChangeOrderSummaryDto> results =
        internalChangeManagementService.list(
            new ChangeManagementService.ListFilters.Builder()
                .changeOrderTypes(getFilterChangeOrderTypes())
                .compartmentIds(List.of(TEST_COMPARTMENT_ID))
                .region(TEST_REGION)
                .build(),
            PageQuery.fromEmptyToken(10));
    assertNotNull(results);
    assertEquals(2, results.getResults().size());
  }

  @Test
  void filterChangeOrdersByState() {
    createTestOrder("SN2001", ChangeOrderType.GB200_DISK_REPAIR);
    createTestOrder("SN2002", ChangeOrderType.NVSWITCH_REPAIR);

    PaginatedList<ChangeOrderSummaryDto> filteredSummaryResults =
        internalChangeManagementService.list(
            new ChangeManagementService.ListFilters.Builder()
                .lifecycleState(ChangeOrderState.ACCEPTED.name())
                .changeOrderTypes(getFilterChangeOrderTypes())
                .compartmentIds(List.of(TEST_COMPARTMENT_ID))
                .region(TEST_REGION)
                .build(),
            PageQuery.fromEmptyToken(10));
    assertEquals(2, filteredSummaryResults.getResults().size());
    assertEquals(ChangeOrderState.ACCEPTED, filteredSummaryResults.getResults().get(0).state());
    PaginatedList<ChangeOrderSummaryDto> serialNumberSummaryFilteredResults =
        internalChangeManagementService.list(
            new ChangeManagementService.ListFilters.Builder()
                .deviceSerialNumber("SN2001")
                .changeOrderTypes(getFilterChangeOrderTypes())
                .compartmentIds(List.of(TEST_COMPARTMENT_ID))
                .region(TEST_REGION)
                .build(),
            PageQuery.fromEmptyToken(10));
    assertEquals(1, serialNumberSummaryFilteredResults.getResults().size());
    assertEquals("SN2001", serialNumberSummaryFilteredResults.getResults().get(0).serialNumber());

    PaginatedList<ChangeOrder> filteredResults =
        internalChangeManagementService.listChangeOrdersInternal(
            new ChangeManagementService.ListFilters.Builder()
                .lifecycleState(ChangeOrderState.ACCEPTED.name())
                .changeOrderTypes(getFilterChangeOrderTypes())
                .build(),
            PageQuery.fromEmptyToken(10));
    assertEquals(2, filteredResults.getResults().size());
    assertEquals(ChangeOrderState.ACCEPTED, filteredResults.getResults().get(0).state());
    PaginatedList<ChangeOrder> serialNumberFilteredResults =
        internalChangeManagementService.listChangeOrdersInternal(
            new ChangeManagementService.ListFilters.Builder()
                .deviceSerialNumber("SN2001")
                .changeOrderTypes(getFilterChangeOrderTypes())
                .build(),
            PageQuery.fromEmptyToken(10));
    assertEquals(1, serialNumberFilteredResults.getResults().size());
    assertEquals("SN2001", serialNumberFilteredResults.getResults().get(0).serialNumber());
  }

  @Test
  void filterChangeOrdersByRackSerialNumber() {
    String testRackSerialNumber1 = "testRackSerialNumber1";
    createTestOrderWithDiagnosticOutput("SN8002", "Test output", testRackSerialNumber1);
    createTestOrderWithDiagnosticOutput("SN8003", "Test output", testRackSerialNumber1);
    createTestOrderWithDiagnosticOutput("SN8004", "Test output", testRackSerialNumber1);
    createTestOrderWithDiagnosticOutput("SN80042", "Test output", "testRackSerialNumber2");
    PaginatedList<ChangeOrderSummaryDto> filteredSummaryResults =
        internalChangeManagementService.list(
            new ChangeManagementService.ListFilters.Builder()
                .rackSerialNumber(testRackSerialNumber1)
                .changeOrderTypes(List.of(ChangeOrderType.GB200_SERVER_REPAIR.name()))
                .compartmentIds(List.of(TEST_COMPARTMENT_ID))
                .region(TEST_REGION)
                .build(),
            PageQuery.fromEmptyToken(10));
    assertEquals(3, filteredSummaryResults.getResults().size());
  }

  @Test
  void filterChangeOrdersByRackSerialNumberAndState() {
    String testRackSerialNumber1 = "testRackSerialNumber1";
    createTestOrderWithDiagnosticOutput("SN8006", "Test output", testRackSerialNumber1);
    createTestOrderWithDiagnosticOutput("SN8007", "Test output", testRackSerialNumber1);
    createTestOrderWithDiagnosticOutput("SN8008", "Test output", testRackSerialNumber1);
    createTestOrderWithDiagnosticOutput("SN8009", "Test output", testRackSerialNumber1);
    createTestOrderWithDiagnosticOutput("SN80042", "Test output", "testRackSerialNumber2");
    PaginatedList<ChangeOrderSummaryDto> filteredSummaryResults =
        internalChangeManagementService.list(
            new ChangeManagementService.ListFilters.Builder()
                .rackSerialNumber(testRackSerialNumber1)
                .lifecycleState(ChangeOrderState.ACCEPTED.name())
                .changeOrderTypes(List.of(ChangeOrderType.GB200_SERVER_REPAIR.name()))
                .compartmentIds(List.of(TEST_COMPARTMENT_ID))
                .region(TEST_REGION)
                .build(),
            PageQuery.fromEmptyToken(10));
    assertEquals(4, filteredSummaryResults.getResults().size());
  }

  @Test
  void createChangeOrder_Success() {
    ChangeOrder order = createTestOrder("SN3001", ChangeOrderType.GB200_SERVER_REPAIR);
    Optional<ChangeOrder> optionalChangeOrder =
        internalChangeManagementService.findChangeOrderByIdInternal(order.id());
    assertTrue(optionalChangeOrder.isPresent());
    assertEquals("SN3001", optionalChangeOrder.get().serialNumber());

    var listPage =
        changeManagerCommandService.listNotCompleted(
            ChangeManagerCommandType.CREATE_TICKET, PageQuery.fromEmptyToken(1));
    assertEquals(1, listPage.getResults().size());
    assertEquals(optionalChangeOrder.get().id(), listPage.getResults().get(0).changeOrderId());

    List<AuditEvent> auditEvents = listAuditEventsByEventName("CREATE_REPAIR_ORDER");
    assertEquals(1, auditEvents.size());
  }

  @Test
  void getChangeOrder_NotFound() {
    RmcId id = RmcId.generate(RmcIdType.CM.CHANGE_ORDER);
    Optional<ChangeOrder> result = internalChangeManagementService.findChangeOrderByIdInternal(id);
    assertFalse(result.isPresent());
  }

  @Test
  void createChangeOrder_DuplicateSerialNumberThrowsException() {
    ChangeOrder order1 = createTestOrder("SN4001", ChangeOrderType.GB200_SERVER_REPAIR);
    assertNotNull(order1);
    assertEquals(order1.severity(), ChangeOrderType.GB200_SERVER_REPAIR.getDefaultSeverity());
    // Create an order with duplicate serial number, expected to throw an exception.
    Exception exception =
        assertThrows(
            IllegalArgumentException.class,
            () -> {
              createTestOrder("SN4001", ChangeOrderType.GB200_DISK_REPAIR);
            });

    String expectedMessage = "An active repair order already exists for this serial number.";
    String actualMessage = exception.getMessage();
    assertTrue(actualMessage.contains(expectedMessage));
  }

  /**
   * Note that this also serves as a regression test for ensuring repair actions can still be
   * performed by external users prior to the asset being shared with the customer. isExternalApi =
   * true, isSharedWithCustomer = false, source = DEFAULT ==> allows repair operations
   */
  @Test
  void prioritizeRepairOrder_Success() {
    // Create a new repair order.
    ChangeOrder order = createTestOrder("SN5001", false, ChangeOrderType.GB200_SERVER_REPAIR);
    assertNotNull(order);
    assertEquals(ChangeOrderState.ACCEPTED, order.state());

    Integer expected = order.priority() + 1;
    ChangeOrder updatedOrder =
        internalChangeManagementService.prioritizeRepairOrder(order.id(), expected, TEST_USER);

    assertNotNull(updatedOrder);
    assertNotNull(updatedOrder);
    assertEquals(expected, updatedOrder.priority());

    ChangeOrder fetchedOrder = internalChangeManagementService.get(order.id());
    assertNotNull(fetchedOrder);
    assertEquals(expected, fetchedOrder.priority());
  }

  @Test
  void prioritizeRepairOrder_TerminalOrderThrowsException() {
    ChangeOrder order = createTestOrder("SN5001", ChangeOrderType.GB200_SERVER_REPAIR);
    assertNotNull(order);
    assertEquals(ChangeOrderState.ACCEPTED, order.state());

    Integer expected = order.priority() + 1;

    internalChangeManagementService.cancelRepairOrder(
        order.id(), "Put order in terminal state", true, "tester", true);
    // Try to cancel repair order, expected to throw an exception.
    Exception exception =
        assertThrows(
            IllegalArgumentException.class,
            () -> {
              internalChangeManagementService.prioritizeRepairOrder(
                  order.id(), expected, TEST_USER);
            });

    String expectedMessage = "Cannot update priority of a terminal repair order";
    String actualMessage = exception.getMessage();
    assertTrue(actualMessage.contains(expectedMessage));
  }

  /**
   * Note that this also serves as a regression test for ensuring repair actions can still be
   * performed by external users after the asset has been shared with the customer. isExternalApi =
   * true, isSharedWithCustomer = true, all sources ==> allows repair operations
   */
  @Test
  void prioritizeRepairOrderInChain() {
    ChangeOrder order = createTestOrder("SN-PROCESS-REPLY", ChangeOrderType.NVSWITCH_REPAIR);
    order =
        internalChangeManagementService.updateState(
            order, ChangeOrderState.ACTIONABLE, "unitTest", false);

    order =
        internalChangeManagementService.updateState(
            order, ChangeOrderState.MITIGATING, "unitTest", false);

    order =
        internalChangeManagementService.updateState(
            order, ChangeOrderState.VALIDATED, "unitTest", false);
    order = internalChangeManagementService.closeRepairOrder(order.id(), true, TEST_USER);

    String comment = "Reply comment for testing";
    CreateRepairOrder replyRequest =
        CreateRepairOrderBuilder.builder()
            .targetAssetId(order.targetAssetId())
            .deviceSerialNumber(order.serialNumber())
            .details(order.details())
            .priority(order.priority())
            .repairOrderType(order.type())
            .repairOrderId(order.id())
            .identifierRequests(
                order.identifiers().stream()
                    .map(
                        i ->
                            new ChangeOrderIdentifierRequest(
                                i.identifierKey(), i.identifierValue(), null))
                    .toList())
            .assetsToActions(
                List.of(
                    new AssetsToActionRequest(
                        assetId,
                        "RN-001",
                        RmcId.generate(RmcIdType.DCMS.RACK_INSTANCE),
                        RmcId.generate(RmcIdType.DCMS.RACK_POSITION),
                        "Elevation-1U",
                        "Y",
                        null,
                        Instant.now(),
                        0L,
                        RmcId.generate(RmcIdType.Catalog.PART))))
            .build();

    ChangeOrderNeedsAttentionDetails needsAttentionDetails =
        new ChangeOrderNeedsAttentionDetails(
            RmcId.generate(RmcIdType.CM.NEEDS_ATTENTION_DETAILS),
            order.id(),
            NeedsAttentionType.GENERIC_COMMENT,
            NeedsAttentionLifecycleState.OPEN,
            "test comment",
            null);
    changeOrderNeedsAttentionDetailRepository.save(needsAttentionDetails);

    ChangeOrder result =
        internalChangeManagementService.processRepairOrderOperation(
            replyRequest, ChangeOrderSourceType.DEFAULT, true, true, RepairOperation.REPLY_TO);
    assertEquals(result.previousChangeOrderId(), replyRequest.getRepairOrderId());

    Integer expected = order.priority() + 1;
    ChangeOrder updatedOrder =
        internalChangeManagementService.prioritizeRepairOrder(order.id(), expected, TEST_USER);

    assertNotNull(updatedOrder);
    assertNotNull(updatedOrder);
    assertEquals(expected, updatedOrder.priority());

    ChangeOrder fetchedOrder = internalChangeManagementService.get(order.id());
    assertNotNull(fetchedOrder);
    assertEquals(expected, fetchedOrder.priority());
  }

  @Test
  void cancelRepairOrder_Success() {
    // Create a new repair order.
    ChangeOrder order = createTestOrder("SN5001", ChangeOrderType.GB200_SERVER_REPAIR);
    assertNotNull(order);
    assertEquals(ChangeOrderState.ACCEPTED, order.state());

    // Cancel this repair order.
    ChangeOrder canceledOrder =
        internalChangeManagementService.cancelRepairOrder(
            order.id(), "Customer requested cancellation", true, TEST_USER, true);
    assertNotNull(canceledOrder);
    assertEquals(ChangeOrderState.CANCELED, canceledOrder.state());
    assertEquals("Customer requested cancellation", canceledOrder.reason());

    // Validate the state of the repair order.
    Optional<ChangeOrder> fetchedOrder =
        internalChangeManagementService.findChangeOrderByIdInternal(order.id());
    assertTrue(fetchedOrder.isPresent());
    assertEquals(ChangeOrderState.CANCELED, fetchedOrder.get().state());

    List<AuditEvent> auditEvents = listAuditEventsByEventName("CREATE_REPAIR_ORDER");
    assertEquals(1, auditEvents.size());

    auditEvents = listAuditEventsByEventName("UPDATE_REPAIR_ORDER");
    assertEquals(1, auditEvents.size());

    auditEvents = listAuditEventsByEventName("CANCEL_REPAIR_ORDER");
    assertEquals(1, auditEvents.size());
  }

  @Test
  void cancelRepairOrder_TerminalOrderThrowsException() {
    ChangeOrder order = createTestOrder("SN6001", ChangeOrderType.GB200_SERVER_REPAIR);
    assertNotNull(order);

    // Update state to a terminal state
    ChangeOrder closedOrder =
        order
            .withState(ChangeOrderState.CLOSED)
            .withTimeUpdated(Instant.now())
            .withTimeCompleted(Instant.now());
    changeOrderRepository.update(closedOrder);

    // Try to cancel repair order, expected to throw an exception.
    var exception =
        assertThrows(
            IllegalArgumentException.class,
            () -> {
              internalChangeManagementService.cancelRepairOrder(
                  order.id(), "Attempt to cancel a closed order", true, TEST_USER, true);
            });

    String expectedMessage = "Order cannot be cancelled in its current state.";
    String actualMessage = exception.getMessage();
    assertTrue(actualMessage.contains(expectedMessage));
  }

  @Test
  void createChangeOrder_VerifyCreatedByAndIsInternal() {
    ChangeOrder order = createTestOrder("SN7001", ChangeOrderType.GB200_SERVER_REPAIR);
    assertNotNull(order);
    assertEquals(ChangeOrderSourceType.DEFAULT, order.source());
    assertTrue(order.isSharedWithCustomer());
  }

  @Test
  void createChangeOrder_WithDiagnosticProbeOutput_CreatesProbeAndResult() {
    String diagnosticProbeOutput = "Test diagnostic output";
    ChangeOrder order =
        createTestOrderWithDiagnosticOutput("SN8001", diagnosticProbeOutput, RACK_SERIAL_NUMBER1);
    assertNotNull(order);

    List<Probe> probes = probeRepository.findAll();
    assertFalse(probes.isEmpty());
    Probe probe = probes.get(0);
    assertEquals(order.id(), probe.changeOrderId());
    assertEquals(ProbeType.DIAGNOSTIC, probe.type());
    assertEquals(ProbeState.SUCCEEDED, probe.state());

    List<ProbeResult> probeResults = probeResultRepository.findAll();
    assertFalse(probeResults.isEmpty());
    ProbeResult probeResult = probeResults.get(0);
    assertEquals(probe.id(), probeResult.probeId());
    assertThat(probeResult.result().resultDetails()).contains(diagnosticProbeOutput);
  }

  @Test
  void testCancelRepairOrder_ExternalUserCanCancelInternalOrder() {
    ChangeOrder order =
        createTestWithSharedFieldOrder(
            "SN10002", ChangeOrderType.GB200_SERVER_REPAIR, ChangeOrderSourceType.BURNINATOR, true);

    ChangeOrder changeOrder =
        internalChangeManagementService.cancelRepairOrder(
            order.id(), "Reason", true, TEST_USER, true);
    assertEquals(changeOrder.state(), ChangeOrderState.CANCELED);
  }

  @Test
  void testApproveRepairOrder_ExternalUserCanApproveInternalOrder() {
    ChangeOrder order =
        createTestWithSharedFieldOrder(
            "SN10002", ChangeOrderType.GB200_SERVER_REPAIR, ChangeOrderSourceType.BURNINATOR, true);

    internalChangeManagementService.approveRepairOrder(order.id(), TEST_USER, true);
    assertEquals(order.state(), ChangeOrderState.ACCEPTED);
  }

  @Test
  void testPrioritizeRepairOrder_ExternalUserCanPrioritizeInternalOrder() {
    ChangeOrder order =
        createTestWithSharedFieldOrder(
            "SN10002", ChangeOrderType.GB200_SERVER_REPAIR, ChangeOrderSourceType.BURNINATOR, true);

    ChangeOrder changeOrder =
        internalChangeManagementService.prioritizeRepairOrder(order.id(), 2, TEST_USER);
    assertEquals(changeOrder.priority(), 2);
  }

  @Test
  void testCloseRepairOrder_ExternalUserCanCloseInternalOrder() {
    ChangeOrder order =
        createTestWithSharedFieldOrder(
            "SN10002", ChangeOrderType.GB200_SERVER_REPAIR, ChangeOrderSourceType.BURNINATOR, true);
    ChangeOrder validatedOrder = order.withState(ChangeOrderState.VALIDATED).withIsFailed(null);
    internalChangeManagementService.update(validatedOrder, TEST_USER);

    ChangeOrder changeOrder =
        internalChangeManagementService.closeRepairOrder(order.id(), false, TEST_USER);
    assertEquals(changeOrder.state(), ChangeOrderState.CLOSED);
  }

  @Test
  void testGetOrder_Visibility() {
    ChangeOrder externalOrder =
        createTestWithSharedFieldOrder(
            "SN10003", ChangeOrderType.GB200_SERVER_REPAIR, ChangeOrderSourceType.DEFAULT, true);

    ChangeOrder internalOrder =
        createTestWithSharedFieldOrder(
            "SN10004",
            ChangeOrderType.GB200_SERVER_REPAIR,
            ChangeOrderSourceType.BURNINATOR,
            false);

    Optional<ChangeOrder> fetchedExternalOrder =
        internalChangeManagementService.findChangeOrderByIdInternal(externalOrder.id());
    assertTrue(fetchedExternalOrder.isPresent());
    assertEquals(externalOrder.id(), fetchedExternalOrder.get().id());

    Optional<ChangeOrder> fetchedInternalOrder =
        internalChangeManagementService.findChangeOrderByIdInternal(internalOrder.id());
    assertTrue(fetchedInternalOrder.isPresent());
  }

  @Test
  void reopenChangeOrder_Success() {
    ChangeOrder closedOrder = createTestOrder("SN9001", ChangeOrderType.GB200_SERVER_REPAIR);
    ChangeOrder closedOrderUpdated =
        closedOrder
            .withState(ChangeOrderState.CLOSED)
            .withTimeUpdated(Instant.now())
            .withTimeCompleted(Instant.now());
    internalChangeManagementService.update(closedOrderUpdated, "test-user");

    CreateRepairOrder reopenRequest =
        CreateRepairOrder.targetAssetId(closedOrder.targetAssetId())
            .diagnosticProbeOutput("Reopen diagnostic")
            .labels(emptySet())
            .deviceSerialNumber(closedOrder.serialNumber())
            .details(closedOrder.details())
            .priority(closedOrder.priority())
            .repairOrderType(closedOrder.type())
            .repairOrderId(closedOrder.id())
            .identifierRequests(
                List.of(
                    new ChangeOrderIdentifierRequest(
                        SERIAL_NUMBER.getKey(),
                        "SN1234",
                        new ChangeOrderIdentifierRequest.LocationData(
                            RmcId.generate(RmcIdType.DCMS.RACK_INSTANCE),
                            RmcId.generate(RmcIdType.DCMS.RACK_POSITION),
                            "RN-001",
                            "Elevation-1U",
                            "DataHall-X")),
                    new ChangeOrderIdentifierRequest(
                        TARGET_ASSET_ID.getKey(),
                        assetId.toString(),
                        new ChangeOrderIdentifierRequest.LocationData(
                            RmcId.generate(RmcIdType.DCMS.RACK_INSTANCE),
                            RmcId.generate(RmcIdType.DCMS.RACK_POSITION),
                            "RN-001",
                            "Elevation-1U",
                            "DataHall-X")),
                    new ChangeOrderIdentifierRequest(
                        RACK_SERIAL_NUMBER.getKey(),
                        RACK_SERIAL_NUMBER1,
                        new ChangeOrderIdentifierRequest.LocationData(
                            RmcId.generate(RmcIdType.DCMS.RACK_INSTANCE),
                            RmcId.generate(RmcIdType.DCMS.RACK_POSITION),
                            "RN-001",
                            "Elevation-1U",
                            "DataHall-X"))))
            .build();

    ChangeOrder reopenedOrder =
        internalChangeManagementService.reopen(reopenRequest, ChangeOrderSourceType.DEFAULT, true);

    assertNotNull(reopenedOrder);
    assertEquals(closedOrder.id(), reopenedOrder.previousChangeOrderId());
    assertEquals(closedOrder.serialNumber(), reopenedOrder.serialNumber());
    assertEquals(closedOrder.targetAssetId(), reopenedOrder.targetAssetId());
    assertEquals(closedOrder.type(), reopenedOrder.type());
    assertEquals(ChangeOrderState.ACCEPTED, reopenedOrder.state());
  }

  @Test
  void listNonTerminalChangeOrdersWithAssigneeInternal_Success() {
    ChangeOrder order1 =
        createTestOrderWithProtectedRack(
            "SN9001",
            ChangeOrderType.GB200_SERVER_REPAIR,
            RackInstanceAccessLevel.RING1,
            "rackSerialNumber1");
    ChangeOrder order2 =
        createTestOrderWithProtectedRack(
            "SN9002",
            ChangeOrderType.GB200_SERVER_REPAIR,
            RackInstanceAccessLevel.RING1,
            "rackSerialNumber2");
    ChangeOrder order3 =
        createTestOrderWithProtectedRack(
            "SN9003",
            ChangeOrderType.GB200_SERVER_REPAIR,
            RackInstanceAccessLevel.RING1,
            "rackSerialNumber3");
    ChangeOrder order4 =
        createTestOrderWithProtectedRack(
            "SN9004",
            ChangeOrderType.GB200_SERVER_REPAIR,
            RackInstanceAccessLevel.REGULAR,
            "rackSerialNumber4");
    // Four new change order without assignee email
    PaginatedList<ChangeOrder> nonTerminalChangeOrders =
        internalChangeManagementService.listNonTerminalChangeOrdersForProtectedRacks(
            PageQuery.fromEmptyToken(10));
    assertEquals(0, nonTerminalChangeOrders.getResults().size());
    // Add 2 assignee emails and close one of the change order
    updateChangeOrder(order1, ChangeOrderState.ACCEPTED, "<EMAIL>");
    updateChangeOrder(order2, ChangeOrderState.CLOSED, "<EMAIL>");
    nonTerminalChangeOrders =
        internalChangeManagementService.listNonTerminalChangeOrdersForProtectedRacks(
            PageQuery.fromEmptyToken(10));
    assertEquals(1, nonTerminalChangeOrders.getResults().size());
  }

  @Test
  void reopenChangeOrder_NonTerminalOrderThrowsException() {
    ChangeOrder nonTerminalOrder = createTestOrder("SN9002", ChangeOrderType.GB200_SERVER_REPAIR);
    CreateRepairOrder reopenRequest =
        CreateRepairOrderBuilder.builder()
            .targetAssetId(nonTerminalOrder.targetAssetId())
            .deviceSerialNumber(nonTerminalOrder.serialNumber())
            .details(nonTerminalOrder.details())
            .priority(nonTerminalOrder.priority())
            .repairOrderType(nonTerminalOrder.type())
            .repairOrderId(nonTerminalOrder.id())
            .identifierRequests(
                List.of(
                    new ChangeOrderIdentifierRequest(
                        SERIAL_NUMBER.getKey(),
                        "SN1234",
                        new ChangeOrderIdentifierRequest.LocationData(
                            RmcId.generate(RmcIdType.DCMS.RACK_INSTANCE),
                            RmcId.generate(RmcIdType.DCMS.RACK_POSITION),
                            "RN-001",
                            "Elevation-1U",
                            "DataHall-X")),
                    new ChangeOrderIdentifierRequest(
                        TARGET_ASSET_ID.getKey(),
                        assetId.toString(),
                        new ChangeOrderIdentifierRequest.LocationData(
                            RmcId.generate(RmcIdType.DCMS.RACK_INSTANCE),
                            RmcId.generate(RmcIdType.DCMS.RACK_POSITION),
                            "RN-001",
                            "Elevation-1U",
                            "DataHall-X")),
                    new ChangeOrderIdentifierRequest(
                        RACK_SERIAL_NUMBER.getKey(),
                        RACK_SERIAL_NUMBER1,
                        new ChangeOrderIdentifierRequest.LocationData(
                            RmcId.generate(RmcIdType.DCMS.RACK_INSTANCE),
                            RmcId.generate(RmcIdType.DCMS.RACK_POSITION),
                            "RN-001",
                            "Elevation-1U",
                            "DataHall-X"))))
            .build();

    Exception exception =
        assertThrows(
            IllegalArgumentException.class,
            () -> {
              internalChangeManagementService.reopen(
                  reopenRequest, ChangeOrderSourceType.DEFAULT, true);
            });

    String expectedMessage = "Cannot reopen a non-terminal repair order chain.";
    String actualMessage = exception.getMessage();
    assertTrue(actualMessage.contains(expectedMessage));
  }

  @Test
  void getOrder_ForHeadWithoutReopen_ShouldReturnItself() {
    ChangeOrder headOnly = createTestOrder("NO_REOPEN", ChangeOrderType.GB200_DISK_REPAIR);
    assertNotNull(headOnly);
    assertNull(headOnly.tailChangeOrderId(), "Should have no tailChangeOrderId if never reopened");
    ChangeOrder fetched = internalChangeManagementService.get(headOnly.id());
    assertEquals(headOnly.id(), fetched.id());
    assertEquals(headOnly.state(), fetched.state());
    assertEquals(headOnly.details(), fetched.details());
  }

  @Test
  void createOrderWithDiagnosticProbeOutput_ProbeHasCorrectHeadChangeOrderId() {
    String probeOutput = "Diagnostic from test";
    RmcId assetId = RmcId.generate(RmcIdType.Inventory.ASSET);
    CreateRepairOrder createRepairOrder =
        CreateRepairOrder.targetAssetId(assetId)
            .diagnosticProbeOutput(probeOutput)
            .labels(emptySet())
            .deviceSerialNumber("SERIAL-PROBE-HEAD")
            .details(new ChangeOrderDetails(null, null, null, null))
            .priority(1)
            .repairOrderType(ChangeOrderType.GB200_SERVER_REPAIR)
            .identifierRequests(
                List.of(
                    new ChangeOrderIdentifierRequest(
                        SERIAL_NUMBER.getKey(), "SERIAL-PROBE-HEAD", null),
                    new ChangeOrderIdentifierRequest(
                        TARGET_ASSET_ID.getKey(), assetId.toString(), null),
                    new ChangeOrderIdentifierRequest(
                        RACK_SERIAL_NUMBER.getKey(), RACK_SERIAL_NUMBER1, null)))
            .assetsToActions(
                List.of(
                    new AssetsToActionRequest(
                        assetId,
                        "RN-001",
                        RmcId.generate(RmcIdType.DCMS.RACK_INSTANCE),
                        RmcId.generate(RmcIdType.DCMS.RACK_POSITION),
                        "Elevation-1U",
                        "Y",
                        null,
                        Instant.now(),
                        0L,
                        RmcId.generate(RmcIdType.Catalog.PART))))
            .compartmentId(TEST_COMPARTMENT_ID)
            .region(TEST_REGION)
            .build();
    ChangeOrder created =
        internalChangeManagementService.create(
            createRepairOrder, ChangeOrderSourceType.DEFAULT, true);
    assertNotNull(created);
    assertEquals(assetId, created.targetAssetId());

    List<Probe> probeList =
        probeRepository.listProbes(created.id(), ProbeType.DIAGNOSTIC, ProbeState.SUCCEEDED);
    assertFalse(probeList.isEmpty());
    Probe probe = probeList.get(0);
    assertEquals(created.id(), probe.changeOrderId());

    PaginatedList<ProbeResult> probeResults =
        probeResultRepository.list(probe.id(), PageQuery.fromEmptyToken(10));
    assertEquals(1, probeResults.getItems().size());
    assertThat(probeResults.getItems().get(0).result().resultDetails()).contains(probeOutput);
  }

  @Test
  void reopenChain_MultipleTimes() {
    // 1. Create Order A
    ChangeOrder orderA = createTestOrder("SN_A", ChangeOrderType.GB200_SERVER_REPAIR);
    assertNotNull(orderA);
    assertNull(orderA.previousChangeOrderId());
    assertNull(orderA.tailChangeOrderId());

    // 2. Close A
    ChangeOrder closedA =
        orderA
            .withState(ChangeOrderState.CLOSED)
            .withReason("First closure")
            .withTimeUpdated(Instant.now())
            .withTimeCompleted(Instant.now());
    internalChangeManagementService.update(closedA, TEST_USER);

    // 3. reopen A -> B
    CreateRepairOrder reopenB =
        CreateRepairOrder.targetAssetId(closedA.targetAssetId())
            .diagnosticProbeOutput("reopen B output")
            .labels(emptySet())
            .deviceSerialNumber(closedA.serialNumber())
            .details(closedA.details())
            .priority(2)
            .repairOrderType(closedA.type())
            .repairOrderId(closedA.id())
            .identifierRequests(
                closedA.identifiers().stream()
                    .map(
                        i ->
                            new ChangeOrderIdentifierRequest(
                                i.identifierKey(), i.identifierValue(), null))
                    .toList())
            .assetsToActions(
                List.of(
                    new AssetsToActionRequest(
                        closedA.targetAssetId(),
                        "RN-001",
                        RmcId.generate(RmcIdType.DCMS.RACK_INSTANCE),
                        RmcId.generate(RmcIdType.DCMS.RACK_POSITION),
                        "Elevation-1U",
                        "Y",
                        null,
                        Instant.now(),
                        0L,
                        RmcId.generate(RmcIdType.Catalog.PART))))
            .build();
    ChangeOrder orderB =
        internalChangeManagementService.reopen(reopenB, ChangeOrderSourceType.DEFAULT, true);
    assertNotNull(orderB);
    assertEquals(
        closedA.id(), orderB.previousChangeOrderId(), "B's previousChangeOrderId should be A");
    // check A's tailChangeOrderId updated
    Optional<ChangeOrder> fetchedA1 =
        internalChangeManagementService.findChangeOrderByIdInternal(closedA.id());
    assertTrue(fetchedA1.isPresent());
    Optional<ChangeOrder> realOrderBOptional =
        internalChangeManagementService.findChangeOrderByIdInternal(
            fetchedA1.get().tailChangeOrderId());
    assertTrue(realOrderBOptional.isPresent());
    ChangeOrder realOrderB = realOrderBOptional.get();

    // 4. Close B
    ChangeOrder closedB =
        orderB
            .withId(realOrderB.id())
            .withState(ChangeOrderState.CLOSED)
            .withReason("Second closure")
            .withRepairOrderId(orderA.id())
            .withTimeCreated(realOrderB.timeCreated())
            .withTimeUpdated(Instant.now())
            .withTimeCompleted(Instant.now());
    internalChangeManagementService.update(closedB, TEST_USER);

    // 5. reopen A -> C
    CreateRepairOrder reopenC =
        CreateRepairOrder.targetAssetId(closedB.targetAssetId())
            .diagnosticProbeOutput("reopen C output")
            .labels(emptySet())
            .deviceSerialNumber(closedB.serialNumber())
            .details(closedB.details())
            .priority(3)
            .repairOrderType(closedB.type())
            .repairOrderId(closedA.id())
            .identifierRequests(
                closedB.identifiers().stream()
                    .map(
                        i ->
                            new ChangeOrderIdentifierRequest(
                                i.identifierKey(), i.identifierValue(), null))
                    .toList())
            .build();
    ChangeOrder orderC =
        internalChangeManagementService.reopen(reopenC, ChangeOrderSourceType.DEFAULT, true);
    assertNotNull(orderC);
    assertEquals(
        realOrderB.id(), orderC.previousChangeOrderId(), "C's previousChangeOrderId should be B");

    // check A's tailChangeOrderId updated to C
    Optional<ChangeOrder> fetchedA2 =
        internalChangeManagementService.findChangeOrderByIdInternal(closedA.id());
    assertTrue(fetchedA2.isPresent());
    Optional<ChangeOrder> realCOptional =
        internalChangeManagementService.findChangeOrderByIdInternal(
            fetchedA2.get().tailChangeOrderId());
    assertTrue(realCOptional.isPresent());

    // 6. Validate id = A，content = C
    ChangeOrder assembled = internalChangeManagementService.get(closedA.id());
    assertEquals(closedA.id(), assembled.id(), "Assembled object uses A's id");
    assertEquals(orderC.state(), assembled.state(), "Assembled object should reflect C's state");
    assertEquals(orderC.details(), assembled.details());
    assertEquals(orderC.serialNumber(), assembled.serialNumber());
  }

  @Test
  void closeRepairOrder_HappyPath() {
    ChangeOrder changeOrder =
        createTestOrder("SN-CLOSE-INT-001", ChangeOrderType.GB200_SERVER_REPAIR);
    ChangeOrder validatedOrder =
        changeOrder.withState(ChangeOrderState.VALIDATED).withIsFailed(null);
    internalChangeManagementService.update(validatedOrder, TEST_USER);

    ChangeOrder closedOrder =
        internalChangeManagementService.closeRepairOrder(
            validatedOrder.id(),
            true, // isFailed
            TEST_USER);
    assertNotNull(closedOrder);
    assertEquals(ChangeOrderState.CLOSED, closedOrder.state());
    assertTrue(Boolean.TRUE.equals(closedOrder.isFailed()));

    Optional<ChangeOrder> finalCheckOpt =
        internalChangeManagementService.findChangeOrderByIdInternal(closedOrder.id());
    assertTrue(finalCheckOpt.isPresent());
    ChangeOrder finalCheck = finalCheckOpt.get();
    assertEquals(ChangeOrderState.CLOSED, finalCheck.state());
    assertTrue(Boolean.TRUE.equals(finalCheck.isFailed()));
  }

  @Test
  void closeInternalRepairOrder_happyPath_2() {
    ChangeOrder changeOrder =
        createTestOrder("SN-CLOSE-INT-002", ChangeOrderType.GB200_SERVER_REPAIR);
    ChangeOrder validatedOrder =
        changeOrder.withState(ChangeOrderState.VALIDATED).withIsFailed(null);
    internalChangeManagementService.update(validatedOrder, TEST_USER);

    ChangeOrder closedOrder =
        internalChangeManagementService.closeInternalRepairOrder(
            validatedOrder.id(),
            "Testing for the new device is failed",
            true,
            true, // isFailed
            TEST_USER);
    assertNotNull(closedOrder);
    assertEquals(ChangeOrderState.CLOSED, closedOrder.state());
    assertTrue(Boolean.TRUE.equals(closedOrder.isFailed()));
    assertEquals("Testing for the new device is failed", closedOrder.reason());

    Optional<ChangeOrder> finalCheckOpt =
        internalChangeManagementService.findChangeOrderByIdInternal(closedOrder.id());
    assertTrue(finalCheckOpt.isPresent());
    ChangeOrder finalCheck = finalCheckOpt.get();
    assertEquals(ChangeOrderState.CLOSED, finalCheck.state());
    assertTrue(Boolean.TRUE.equals(finalCheck.isFailed()));
    assertEquals("Testing for the new device is failed", finalCheck.reason());
  }

  @Test
  void closeInternalRepairOrder_Success() {
    ChangeOrder internalOrder =
        createTestWithSharedFieldOrder(
            "SN-CLOSE-INT-001",
            ChangeOrderType.GB200_SERVER_REPAIR,
            ChangeOrderSourceType.BURNINATOR,
            false // isSharedWithCustomer
            );

    ChangeOrder validatedOrder = internalOrder.withState(ChangeOrderState.VALIDATED);
    internalChangeManagementService.update(validatedOrder, TEST_USER);

    ChangeOrder closedOrder =
        internalChangeManagementService.closeInternalRepairOrder(
            validatedOrder.id(),
            "Testing for the new device is failed",
            false,
            true, // isFailed
            TEST_USER);
    assertNotNull(closedOrder);
    assertEquals(ChangeOrderState.CLOSED, closedOrder.state());
    assertTrue(Boolean.TRUE.equals(closedOrder.isFailed()));
    assertEquals("Testing for the new device is failed", closedOrder.reason());

    Optional<ChangeOrder> finalCheckOpt =
        internalChangeManagementService.findChangeOrderByIdInternal(closedOrder.id());
    assertTrue(finalCheckOpt.isPresent());
    ChangeOrder finalCheck = finalCheckOpt.get();
    assertEquals(ChangeOrderState.CLOSED, finalCheck.state());
    assertTrue(Boolean.TRUE.equals(finalCheck.isFailed()));
    assertEquals("Testing for the new device is failed", finalCheck.reason());
  }

  @Test
  void closeInternalRepairOrder_FailIfStateNotValidated() {
    // create an internal network order but state = ACCEPTED
    ChangeOrder networkAccepted =
        createTestWithSharedFieldOrder(
            "SN-CLOSE-INT-003",
            ChangeOrderType.GB200_SERVER_REPAIR,
            ChangeOrderSourceType.NETWORK,
            false);

    Exception ex =
        assertThrows(
            IllegalArgumentException.class,
            () ->
                internalChangeManagementService.closeInternalRepairOrder(
                    networkAccepted.id(), "Wrong state", false, false, TEST_USER));
    assertTrue(ex.getMessage().contains("Order cannot be closed in its current state."));
  }

  @Test
  void testFindReopenChain() {
    RmcId headId = RmcId.generate(RmcIdType.CM.CHANGE_ORDER);
    RmcId reopenId1 = RmcId.generate(RmcIdType.CM.CHANGE_ORDER);
    RmcId reopenId2 = RmcId.generate(RmcIdType.CM.CHANGE_ORDER);
    ChangeOrder head =
        ChangeOrderBuilder.builder()
            .id(headId)
            .targetAssetId(RmcId.generate(RmcIdType.Inventory.ASSET))
            .serialNumber("HEAD-SN")
            .type(ChangeOrderType.SERVER_REPAIR)
            .details(new ChangeOrderDetails(null, null, null, null))
            .priority(1)
            .state(ChangeOrderState.ACCEPTED)
            .summary("Head summary")
            .description("Head description")
            .source(ChangeOrderSourceType.DEFAULT)
            .isSharedWithCustomer(true)
            .tailChangeOrderId(reopenId2)
            .compartmentId(TEST_COMPARTMENT_ID)
            .region(TEST_REGION)
            .build();
    changeOrderRepository.save(head);

    ChangeOrder reopen1 =
        ChangeOrderBuilder.builder()
            .id(reopenId1)
            .targetAssetId(RmcId.generate(RmcIdType.Inventory.ASSET))
            .serialNumber("REOPEN1-SN")
            .type(ChangeOrderType.SERVER_REPAIR)
            .details(new ChangeOrderDetails(null, null, null, null))
            .priority(2)
            .state(ChangeOrderState.ACCEPTED)
            .summary("Reopen1 summary")
            .description("Reopen1 description")
            .source(ChangeOrderSourceType.DEFAULT)
            .isSharedWithCustomer(true)
            .previousChangeOrderId(headId)
            .repairOrderId(headId)
            .version(0L)
            .compartmentId(TEST_COMPARTMENT_ID)
            .region(TEST_REGION)
            .timeCreated(Instant.now().minusSeconds(60))
            .timeUpdated(Instant.now().minusSeconds(60))
            .build();
    changeOrderRepository.save(reopen1);

    ChangeOrder reopen2 =
        ChangeOrderBuilder.builder()
            .id(reopenId2)
            .targetAssetId(RmcId.generate(RmcIdType.Inventory.ASSET))
            .serialNumber("REOPEN2-SN")
            .type(ChangeOrderType.SERVER_REPAIR)
            .details(new ChangeOrderDetails(null, null, null, null))
            .priority(3)
            .state(ChangeOrderState.ACCEPTED)
            .summary("Reopen2 summary")
            .description("Reopen2 description")
            .source(ChangeOrderSourceType.DEFAULT)
            .isSharedWithCustomer(true)
            .previousChangeOrderId(reopenId1)
            .repairOrderId(headId)
            .version(1L)
            .compartmentId(TEST_COMPARTMENT_ID)
            .region(TEST_REGION)
            .timeCreated(Instant.now())
            .timeUpdated(Instant.now())
            .build();
    changeOrderRepository.save(reopen2);

    List<ChangeOrder> chain = internalChangeManagementService.findReopenChain(headId);
    assertNotNull(chain);
    assertEquals(3, chain.size());
    assertEquals(headId, chain.get(0).id());
    assertTrue(chain.get(1).timeCreated().isBefore(chain.get(2).timeCreated()));

    RmcId head2Id = RmcId.generate(RmcIdType.CM.CHANGE_ORDER);
    ChangeOrder head2 =
        ChangeOrderBuilder.builder()
            .id(head2Id)
            .targetAssetId(RmcId.generate(RmcIdType.Inventory.ASSET))
            .serialNumber("HEAD2-SN")
            .type(ChangeOrderType.SERVER_REPAIR)
            .details(new ChangeOrderDetails(null, null, null, null))
            .priority(1)
            .state(ChangeOrderState.ACCEPTED)
            .summary("Head2 summary")
            .description("Head2 description")
            .source(ChangeOrderSourceType.DEFAULT)
            .isSharedWithCustomer(true)
            .compartmentId(TEST_COMPARTMENT_ID)
            .region(TEST_REGION)
            .build();
    changeOrderRepository.save(head2);
    List<ChangeOrder> chain2 = internalChangeManagementService.findReopenChain(head2Id);
    assertEquals(1, chain2.size());
  }

  @Test
  void testGetChangeOrderAssetReservationsByChangeOrderIds() {
    ChangeOrder order1 = createTestOrder("SN-RES-001", ChangeOrderType.DISK_REPAIR);
    ChangeOrder order2 = createTestOrder("SN-RES-002", ChangeOrderType.GB200_SERVER_REPAIR);

    ChangeOrderAssetReservation reservation1 =
        new ChangeOrderAssetReservation(
            new ChangeOrderAssetReservationId(order1.id(), "reservation-1"),
            "partId-1",
            "locationId-1",
            "spareSerial-1",
            0);
    ChangeOrderAssetReservation reservation2 =
        new ChangeOrderAssetReservation(
            new ChangeOrderAssetReservationId(order2.id(), "reservation-2"),
            "partId-2",
            "locationId-2",
            "spareSerial-2",
            0);

    changeOrderAssetReservationRepository.save(reservation1);
    changeOrderAssetReservationRepository.save(reservation2);

    List<RmcId> coIds = List.of(order1.id(), order2.id());
    List<ChangeOrderAssetReservation> foundReservations =
        internalChangeManagementService.getChangeOrderAssetReservationsByChangeOrderIds(coIds);

    assertNotNull(foundReservations);
    assertEquals(2, foundReservations.size());

    assertTrue(
        foundReservations.stream()
            .anyMatch(
                r ->
                    "reservation-1"
                        .equals(r.changeOrderAssetReservationId().assetReservationId())));
    assertTrue(
        foundReservations.stream()
            .anyMatch(
                r ->
                    "reservation-2"
                        .equals(r.changeOrderAssetReservationId().assetReservationId())));
  }

  @Test
  void testGetIdentifierListByChangeOrderIds() {
    ChangeOrder order1 = createTestOrder("SN-ID-001", ChangeOrderType.DISK_REPAIR);
    ChangeOrder order2 = createTestOrder("SN-ID-002", ChangeOrderType.GB200_SERVER_REPAIR);
    RmcId identifierId1 = RmcId.generate(RmcIdType.CM.CHANGE_ORDER_IDENTIFIER);
    RmcId identifierId2 = RmcId.generate(RmcIdType.CM.CHANGE_ORDER_IDENTIFIER);

    ChangeOrderIdentifier identifier1 =
        new ChangeOrderIdentifier(identifierId1, order1.id(), "serial_number", "SN-ID-001");
    ChangeOrderIdentifier identifier2 =
        new ChangeOrderIdentifier(identifierId2, order2.id(), "serial_number", "SN-ID-002");

    changeOrderIdentifierRepository.save(identifier1);
    changeOrderIdentifierRepository.save(identifier2);

    List<RmcId> coIds = List.of(order1.id(), order2.id());
    List<ChangeOrderIdentifier> foundIdentifiers =
        internalChangeManagementService.getIdentifierListByChangeOrderIds(coIds);

    assertNotNull(foundIdentifiers);
    assertEquals(8, foundIdentifiers.size());

    assertTrue(foundIdentifiers.stream().anyMatch(i -> i.identifierValue().equals("SN-ID-001")));
    assertTrue(foundIdentifiers.stream().anyMatch(i -> i.identifierValue().equals("SN-ID-002")));
  }

  @Test
  void testProcessRepairOrderOperation_Fail() {
    ChangeOrder order = createTestOrder("SN-PROCESS-FAIL", ChangeOrderType.NVSWITCH_REPAIR);
    ChangeOrder validatedOrder = order.withState(ChangeOrderState.VALIDATED);
    internalChangeManagementService.update(validatedOrder, TEST_USER);

    CreateRepairOrder failRequest =
        CreateRepairOrderBuilder.builder()
            .targetAssetId(order.targetAssetId())
            .deviceSerialNumber(order.serialNumber())
            .details(order.details())
            .priority(order.priority())
            .repairOrderType(order.type())
            .repairOrderId(order.id())
            .ticketData(TICKET_DATA)
            .ticketFields(TICKET_FIELDS)
            .identifierRequests(
                order.identifiers().stream()
                    .map(
                        i ->
                            new ChangeOrderIdentifierRequest(
                                i.identifierKey(), i.identifierValue(), null))
                    .toList())
            .assetsToActions(
                List.of(
                    new AssetsToActionRequest(
                        order.targetAssetId(),
                        "RN-001",
                        RmcId.generate(RmcIdType.DCMS.RACK_INSTANCE),
                        RmcId.generate(RmcIdType.DCMS.RACK_POSITION),
                        "Elevation-1U",
                        "Y",
                        null,
                        Instant.now(),
                        0L,
                        RmcId.generate(RmcIdType.Catalog.PART))))
            .build();

    ChangeOrder result =
        internalChangeManagementService.processRepairOrderOperation(
            failRequest, ChangeOrderSourceType.DEFAULT, true, true, RepairOperation.FAIL);

    assertNotNull(result);
    assertEquals(validatedOrder.id(), result.previousChangeOrderId());
    assertEquals(failRequest.targetAssetId(), result.targetAssetId());
    assertEquals(failRequest.deviceSerialNumber(), result.serialNumber());
    assertEquals(failRequest.repairOrderType(), result.type());
    assertEquals(failRequest.priority(), result.priority());
    assertEquals(failRequest.details(), result.details());
    Optional<ChangeOrder> optionalChangeOrder = changeOrderRepository.findById(validatedOrder.id());
    assertTrue(optionalChangeOrder.isPresent());
    Optional<ChangeOrder> optionalTailChangeOrder =
        changeOrderRepository.findById(optionalChangeOrder.get().tailChangeOrderId());
    assertTrue(optionalTailChangeOrder.isPresent());
    List<TicketField> ticketFields =
        ticketFieldRepository.getTicketFields(optionalTailChangeOrder.get().id());
    assertNotNull(ticketFields);
  }

  @Test
  void testProcessRepairOrderOperation_ReplyTo() {
    ChangeOrder order = createTestOrder("SN-PROCESS-REPLY", ChangeOrderType.NVSWITCH_REPAIR);
    ChangeOrder closedOrder =
        order
            .withState(ChangeOrderState.VALIDATED)
            .withIsFailed(true)
            .withTimeUpdated(Instant.now())
            .withTimeCompleted(Instant.now());
    internalChangeManagementService.update(closedOrder, TEST_USER);
    ChangeOrderNeedsAttentionDetails needsAttentionDetails =
        new ChangeOrderNeedsAttentionDetails(
            RmcId.generate(RmcIdType.CM.NEEDS_ATTENTION_DETAILS),
            order.id(),
            NeedsAttentionType.GENERIC_COMMENT,
            NeedsAttentionLifecycleState.OPEN,
            "test comment",
            null);
    changeOrderNeedsAttentionDetailRepository.save(needsAttentionDetails);
    String comment = "Reply comment for testing";
    CreateRepairOrder replyRequest =
        CreateRepairOrderBuilder.builder()
            .targetAssetId(order.targetAssetId())
            .deviceSerialNumber(order.serialNumber())
            .details(order.details())
            .priority(order.priority())
            .repairOrderType(order.type())
            .repairOrderId(order.id())
            .ticketData(TICKET_DATA)
            .ticketFields(TICKET_FIELDS)
            .identifierRequests(
                order.identifiers().stream()
                    .map(
                        i ->
                            new ChangeOrderIdentifierRequest(
                                i.identifierKey(), i.identifierValue(), null))
                    .toList())
            .assetsToActions(
                List.of(
                    new AssetsToActionRequest(
                        order.targetAssetId(),
                        "RN-001",
                        RmcId.generate(RmcIdType.DCMS.RACK_INSTANCE),
                        RmcId.generate(RmcIdType.DCMS.RACK_POSITION),
                        "Elevation-1U",
                        "Y",
                        null,
                        Instant.now(),
                        0L,
                        RmcId.generate(RmcIdType.Catalog.PART))))
            .build();

    ChangeOrder result =
        internalChangeManagementService.processRepairOrderOperation(
            replyRequest, ChangeOrderSourceType.DEFAULT, true, true, RepairOperation.REPLY_TO);

    assertNotNull(result);
    assertEquals(closedOrder.id(), result.previousChangeOrderId());
    assertEquals(replyRequest.targetAssetId(), result.targetAssetId());
    assertEquals(replyRequest.deviceSerialNumber(), result.serialNumber());
    assertEquals(replyRequest.repairOrderType(), result.type());
    assertEquals(replyRequest.priority(), result.priority());
    assertEquals(replyRequest.details(), result.details());
    Optional<ChangeOrder> optionalChangeOrder = changeOrderRepository.findById(closedOrder.id());
    assertTrue(optionalChangeOrder.isPresent());
    Optional<ChangeOrder> optionalTailChangeOrder =
        changeOrderRepository.findById(optionalChangeOrder.get().tailChangeOrderId());
    assertTrue(optionalTailChangeOrder.isPresent());
    List<TicketField> ticketFields =
        ticketFieldRepository.getTicketFields(optionalTailChangeOrder.get().id());
    assertNotNull(ticketFields);
  }

  @Test
  void testCreateChangeOrderWithoutOperationDetails_Fail() {
    ChangeOrder order = createTestOrder("SN-WOD-Fail", ChangeOrderType.NVSWITCH_REPAIR);
    ChangeOrder validatedOrder = order.withState(ChangeOrderState.VALIDATED);
    internalChangeManagementService.update(validatedOrder, TEST_USER);

    ChangeOrder result =
        internalChangeManagementService.createChangeOrderWithoutOperationDetails(
            order.id(), ChangeOrderSourceType.DEFAULT, true, true, RepairOperation.FAIL);

    assertNotNull(result);
    assertEquals(validatedOrder.id(), result.previousChangeOrderId());
    List<ChangeOrderNodeImpact> changeOrderNodeImpactsOldTail =
        changeOrderNodeImpactRepository.findByChangeOrderIds(List.of(order.id()));
    List<ChangeOrderNodeImpact> changeOrderNodeImpactsNewTail =
        changeOrderNodeImpactRepository.findByChangeOrderIds(List.of(result.id()));
    assertNotNull(changeOrderNodeImpactsOldTail);
    assertThat(changeOrderNodeImpactsOldTail.size()).isEqualTo(2);
    assertThat(changeOrderNodeImpactsOldTail).isEqualTo(changeOrderNodeImpactsNewTail);
  }

  @Test
  void testCreateChangeOrderWithoutOperationDetails_FailRepairOrder_with_tail_reservations() {
    ChangeOrder order = createTestOrder("SN-WOD-Fail", ChangeOrderType.NVSWITCH_REPAIR);
    ChangeOrder validatedOrder = order.withState(ChangeOrderState.VALIDATED);
    internalChangeManagementService.update(validatedOrder, TEST_USER);

    ChangeOrderAssetReservationId assetReservationId =
        new ChangeOrderAssetReservationId(order.id(), "testAssetId");
    ChangeOrderAssetReservation assetReservation =
        new ChangeOrderAssetReservation(
            assetReservationId, "testPartId", "testLocationId", "SN-TASK-CLEAN-SPARE", 0);
    changeOrderAssetReservationRepository.save(assetReservation);

    assertThrows(
        IllegalArgumentException.class,
        () ->
            internalChangeManagementService.createChangeOrderWithoutOperationDetails(
                order.id(), ChangeOrderSourceType.DEFAULT, true, true, RepairOperation.FAIL));
  }

  @Test
  void testCreateChangeOrderWithoutOperationDetails_validating_fail() {
    ChangeOrder order = createTestOrder("SN-WOD-Fail", ChangeOrderType.NVSWITCH_REPAIR);
    ChangeOrder validatedOrder = order.withState(ChangeOrderState.VALIDATING);
    internalChangeManagementService.update(validatedOrder, TEST_USER);

    ChangeOrder result =
        internalChangeManagementService.createChangeOrderWithoutOperationDetails(
            order.id(), ChangeOrderSourceType.DEFAULT, true, true, RepairOperation.FAIL);

    assertNotNull(result);
    assertEquals(validatedOrder.id(), result.previousChangeOrderId());
    List<ChangeOrderNodeImpact> changeOrderNodeImpactsOldTail =
        changeOrderNodeImpactRepository.findByChangeOrderIds(List.of(order.id()));
    List<ChangeOrderNodeImpact> changeOrderNodeImpactsNewTail =
        changeOrderNodeImpactRepository.findByChangeOrderIds(List.of(result.id()));
    assertNotNull(changeOrderNodeImpactsOldTail);
    assertThat(changeOrderNodeImpactsOldTail.size()).isEqualTo(2);
    assertThat(changeOrderNodeImpactsOldTail).isEqualTo(changeOrderNodeImpactsNewTail);
  }

  @Test
  void testCreateChangeOrderWithoutOperationDetails_ReplyTo() {
    ChangeOrder order = createTestOrder("SN-WOD-Reply", ChangeOrderType.NVSWITCH_REPAIR);
    ChangeOrder closedOrder =
        order
            .withState(ChangeOrderState.CLOSED)
            .withIsFailed(true)
            .withTimeUpdated(Instant.now())
            .withTimeCompleted(Instant.now());
    internalChangeManagementService.update(closedOrder, TEST_USER);

    ChangeOrderNeedsAttentionDetails needsAttentionDetails =
        new ChangeOrderNeedsAttentionDetails(
            RmcId.generate(RmcIdType.CM.NEEDS_ATTENTION_DETAILS),
            order.id(),
            NeedsAttentionType.GENERIC_COMMENT,
            NeedsAttentionLifecycleState.OPEN,
            "test comment",
            null);
    changeOrderNeedsAttentionDetailRepository.save(needsAttentionDetails);
    ChangeOrder result =
        internalChangeManagementService.createChangeOrderWithoutOperationDetails(
            order.id(), ChangeOrderSourceType.DEFAULT, true, true, RepairOperation.REPLY_TO);

    assertNotNull(result);
    assertEquals(closedOrder.id(), result.previousChangeOrderId());
    List<ChangeOrderNodeImpact> changeOrderNodeImpactsOldTail =
        changeOrderNodeImpactRepository.findByChangeOrderIds(List.of(order.id()));
    List<ChangeOrderNodeImpact> changeOrderNodeImpactsNewTail =
        changeOrderNodeImpactRepository.findByChangeOrderIds(List.of(result.id()));
    assertNotNull(changeOrderNodeImpactsOldTail);
    assertThat(changeOrderNodeImpactsOldTail.size()).isEqualTo(2);
    assertThat(changeOrderNodeImpactsOldTail).isEqualTo(changeOrderNodeImpactsNewTail);
  }

  @Test
  void testTicketDecoratorDataIsPersisted() {
    ChangeOrder changeOrder = createTestOrder(ro -> {});
    RmcId changeOrderId = changeOrder.id();
    TicketData ticketData = internalChangeManagementService.getTicketData(changeOrderId);
    List<TicketFieldEntity> ticketFields =
        ticketFieldRepository.listByChangeOrderIdOrderByIndex(changeOrderId);

    try (AutoCloseableSoftAssertions softly = new AutoCloseableSoftAssertions()) {
      TICKET_DATA
          .keySet()
          .forEach(
              key -> {
                Object actualValue = ticketData.get(key);
                softly.assertThat(ticketData.get(key)).as(key).isEqualTo(TICKET_DATA.get(key));
              });

      // Order of ticketFields matters
      for (int index = 0; index < TICKET_FIELDS.size(); index++) {
        TicketFieldEntity actualTicketField = ticketFields.get(index);
        softly.assertThat(actualTicketField.key().index()).as("index").isEqualTo(index);
        softly
            .assertThat(actualTicketField)
            .usingRecursiveComparison()
            .ignoringFields("key")
            .isEqualTo(TICKET_FIELDS.get(index));
      }
    }
  }

  @Test
  void testCloseInternalRepairOrder_WithValidatedState_Success() {
    String assetSerial = "SN_CLOSE_VALIDATED_001";
    ChangeOrder internalOrder =
        createTestWithSharedFieldOrder(
            assetSerial,
            ChangeOrderType.GB200_SERVER_REPAIR,
            ChangeOrderSourceType.BURNINATOR,
            false);
    ChangeOrder validatedOrder = internalOrder.withState(ChangeOrderState.VALIDATED);
    internalChangeManagementService.update(validatedOrder, TEST_USER);

    ChangeOrder closedOrder =
        internalChangeManagementService.closeInternalRepairOrder(
            validatedOrder.id(), "Closing validated order", false, true, TEST_USER);
    assertNotNull(closedOrder);
    assertEquals(ChangeOrderState.CLOSED, closedOrder.state());
  }

  @Test
  void closeInternalRepairOrder_ValidatingState_Success() {
    ChangeOrder internalOrder =
        createTestWithSharedFieldOrder(
            "SN-CLOSE-INT-VALIDATING",
            ChangeOrderType.GB200_SERVER_REPAIR,
            ChangeOrderSourceType.BURNINATOR,
            false);
    ChangeOrder validatingOrder = internalOrder.withState(ChangeOrderState.VALIDATING);
    ChangeOrder updated = internalChangeManagementService.update(validatingOrder, TEST_USER);
    assertEquals(ChangeOrderState.VALIDATING, updated.state());

    ChangeOrder closedOrder =
        internalChangeManagementService.closeInternalRepairOrder(
            validatingOrder.id(), "Closing from VALIDATING state", false, true, TEST_USER);
    assertNotNull(closedOrder);
    assertEquals(ChangeOrderState.CLOSED, closedOrder.state());
    assertTrue(Boolean.TRUE.equals(closedOrder.isFailed()));
    assertEquals("Closing from VALIDATING state", closedOrder.reason());

    Optional<ChangeOrder> finalCheckOpt =
        internalChangeManagementService.findChangeOrderByIdInternal(closedOrder.id());
    assertTrue(finalCheckOpt.isPresent());
    ChangeOrder finalCheck = finalCheckOpt.get();
    assertEquals(ChangeOrderState.CLOSED, finalCheck.state());
    assertTrue(Boolean.TRUE.equals(finalCheck.isFailed()));
    assertEquals("Closing from VALIDATING state", finalCheck.reason());
  }

  @Test
  void testCancelRepairOrder_WhenStateAccepted_Success() {
    String assetSerial = "SN_CANCEL_ACCEPTED_001";
    ChangeOrder order = createTestOrder(assetSerial, ChangeOrderType.GB200_SERVER_REPAIR);
    assertNotNull(order);
    assertEquals(ChangeOrderState.ACCEPTED, order.state());

    ChangeOrder canceledOrder =
        internalChangeManagementService.cancelRepairOrder(
            order.id(), "Customer requested cancellation", true, TEST_USER, true);
    assertNotNull(canceledOrder);
    assertEquals(ChangeOrderState.CANCELED, canceledOrder.state());
    assertEquals("Customer requested cancellation", canceledOrder.reason());
  }

  @Test
  void testCancelRepairOrder_WithOpenNAD_Success() {
    String assetSerial = "SN_CANCEL_NAD_001";
    ChangeOrder internalOrder =
        createTestWithSharedFieldOrder(
            assetSerial,
            ChangeOrderType.GB200_SERVER_REPAIR,
            ChangeOrderSourceType.BURNINATOR,
            false);
    assertNotNull(internalOrder.id());
    PaginatedList<ChangeOrder> filteredResults =
        internalChangeManagementService.listChangeOrdersInternal(
            new ChangeManagementService.ListFilters.Builder()
                .changeOrderTypes(getFilterChangeOrderTypes())
                .source(ChangeOrderSourceType.BURNINATOR.name())
                .needsAttention(true)
                .build(),
            PageQuery.fromEmptyToken(10));
    assertEquals(1, filteredResults.getResults().size());
    assertEquals(ChangeOrderState.ACCEPTED, filteredResults.getResults().get(0).state());
    ChangeOrder canceledOrder =
        internalChangeManagementService.cancelRepairOrder(
            internalOrder.id(), "Internal cancellation with NAD", false, TEST_USER, true);
    assertNotNull(canceledOrder);
    assertEquals(ChangeOrderState.CANCELED, canceledOrder.state());
    List<ChangeOrderNeedsAttentionDetails> nadList =
        internalChangeManagementService.getNeedsAttentionDetailsByChangeOrderIds(
            List.of(canceledOrder.id()));
    nadList.forEach(nad -> assertEquals(NeedsAttentionLifecycleState.CLOSED, nad.state()));
  }

  @Test
  void testBurninatorRepairOrder_Success() {
    String assetSerial = "SN_APPROVE_001";
    ChangeOrder internalOrder =
        createTestWithSharedFieldOrder(
            assetSerial,
            ChangeOrderType.GB200_SERVER_REPAIR,
            ChangeOrderSourceType.BURNINATOR,
            false);
    assertNotNull(internalOrder.id());
    List<ChangeOrderNeedsAttentionDetails> openNAD =
        internalChangeManagementService.getNeedsAttentionDetailsByChangeOrderIds(
            List.of(internalOrder.id()));
    assertTrue(openNAD.isEmpty());

    ChangeOrder burninOrder = internalChangeManagementService.get(internalOrder.id());
    assertNull(burninOrder.timeApproved());
  }

  @Test
  void backfillRepairOrderCompartment_invokesRepo_andUpdatesRecords() {
    ChangeOrder internalOrder =
        createTestWithSharedFieldOrder(
                "SN-BF-SVC",
                ChangeOrderType.GB200_SERVER_REPAIR,
                ChangeOrderSourceType.BURNINATOR,
                false)
            .withCompartmentId(null);
    changeOrderRepository.update(internalOrder);
    String testInternalCompartmentId = "test_internal_compartment_id";
    internalChangeManagementService.backfillRepairOrderCompartment();

    ChangeOrder updated =
        changeOrderRepository
            .findById(internalOrder.id())
            .orElseThrow(() -> new IllegalStateException("ChangeOrder not found"));
    assertThat(updated.compartmentId()).isEqualTo(testInternalCompartmentId);
  }

  @Test
  void purgeCanaryChangeOrders_withoutRepairOrderId_shouldPurgeWholeRegion() {
    String canaryRegion = "tst";
    ChangeOrder o1 = createTestOrder("SN-PCO-1", ChangeOrderType.GB200_SERVER_REPAIR);
    ChangeOrder o2 = createTestOrder("SN-PCO-2", ChangeOrderType.DISK_REPAIR);
    o1 = changeOrderRepository.update(o1.withRegion(canaryRegion));
    o2 = changeOrderRepository.update(o2.withRegion(canaryRegion));
    ChangeOrder keep = createTestOrder("SN-PCO-KEEP", ChangeOrderType.NVSWITCH_REPAIR);
    internalChangeManagementService.purgeCanaryChangeOrders(canaryRegion, null);
    assertThat(changeOrderRepository.findById(keep.id())).isPresent();
    assertThat(changeOrderRepository.findById(o1.id())).isEmpty();
    assertThat(changeOrderRepository.findById(o2.id())).isEmpty();
  }

  @Test
  void purgeCanaryChangeOrders_withRepairOrderId_shouldPurgeSpecifiedChainOnly() {
    String purgeRegion = "tst";

    RmcId headId = RmcId.generate(RmcIdType.CM.CHANGE_ORDER);
    RmcId tailId = RmcId.generate(RmcIdType.CM.CHANGE_ORDER);

    ChangeOrder head =
        ChangeOrderBuilder.builder()
            .id(headId)
            .targetAssetId(assetId)
            .serialNumber("SN-PURGE-H")
            .type(ChangeOrderType.GB200_SERVER_REPAIR)
            .details(new ChangeOrderDetails(null, null, null, null))
            .priority(1)
            .state(ChangeOrderState.ACCEPTED)
            .source(ChangeOrderSourceType.DEFAULT)
            .isSharedWithCustomer(true)
            .tailChangeOrderId(tailId)
            .compartmentId(TEST_COMPARTMENT_ID)
            .region(purgeRegion)
            .build();

    ChangeOrder tail =
        ChangeOrderBuilder.builder()
            .id(tailId)
            .targetAssetId(assetId)
            .serialNumber("SN-PURGE-T")
            .type(ChangeOrderType.GB200_SERVER_REPAIR)
            .details(new ChangeOrderDetails(null, null, null, null))
            .priority(2)
            .state(ChangeOrderState.ACCEPTED)
            .source(ChangeOrderSourceType.DEFAULT)
            .isSharedWithCustomer(true)
            .previousChangeOrderId(headId)
            .repairOrderId(headId)
            .compartmentId(TEST_COMPARTMENT_ID)
            .region(purgeRegion)
            .build();

    changeOrderRepository.saveAll(List.of(head, tail));

    ChangeOrder survivor = createTestOrder("SN-PURGE-SURVIVOR", ChangeOrderType.GB200_DISK_REPAIR);
    survivor = changeOrderRepository.update(survivor.withRegion(purgeRegion));

    internalChangeManagementService.purgeCanaryChangeOrders(purgeRegion, headId);

    assertThat(changeOrderRepository.findById(headId)).isEmpty();
    assertThat(changeOrderRepository.findById(tailId)).isEmpty();
    assertThat(changeOrderRepository.findById(survivor.id())).isPresent();
  }

  @Test
  void listChangeOrdersInternal_PaginationNextPage() {
    for (int i = 0; i < 15; i++) {
      createTestOrder("SN-PG-" + i, ChangeOrderType.GB200_SERVER_REPAIR);
    }

    PaginatedList<ChangeOrder> firstPage =
        internalChangeManagementService.listChangeOrdersInternal(
            new ChangeManagementService.ListFilters.Builder()
                .changeOrderTypes(getFilterChangeOrderTypes())
                .build(),
            PageQuery.fromEmptyToken(10));

    assertEquals(10, firstPage.getResults().size());
    assertNotNull(firstPage.getNextPageToken());

    PaginatedList<ChangeOrder> secondPage =
        internalChangeManagementService.listChangeOrdersInternal(
            new ChangeManagementService.ListFilters.Builder()
                .changeOrderTypes(getFilterChangeOrderTypes())
                .build(),
            PageQuery.fromToken(firstPage.getNextPageToken(), 10));

    assertEquals(5, secondPage.getResults().size());
  }

  void createMultiServerRepair_Success() {
    ChangeOrder order =
        createTestOrderWithLabels(
            "SN-MULTI-001",
            ChangeOrderType.MULTI_REPAIR,
            Set.of("a", "b"),
            ChangeOrderSourceType.BURNINATOR,
            false);

    assertNotNull(order);
    assertEquals(ChangeOrderType.MULTI_REPAIR, order.type());
    assertEquals(ChangeOrderState.ACCEPTED, order.state());
    assertEquals(ChangeOrderType.MULTI_REPAIR.getDefaultSeverity(), order.severity());
  }

  @Test
  void createChangeOrder_WithInvalidSource_ThrowsException() {
    assertThrows(
        IllegalArgumentException.class,
        () ->
            createTestOrderWithLabels(
                "SN-EMPTY-LABELS-001",
                ChangeOrderType.MULTI_REPAIR,
                Set.of("a", "b"),
                ChangeOrderSourceType.NETWORK,
                false));
  }

  @Test
  void createChangeOrder_WithEmptyLabels_ThrowsException() {
    assertThrows(
        IllegalArgumentException.class,
        () ->
            createTestOrderWithLabels(
                "SN-EMPTY-LABELS-001",
                ChangeOrderType.MULTI_REPAIR,
                Set.of(),
                ChangeOrderSourceType.BURNINATOR,
                false));
  }

  protected ChangeOrder createTestOrderWithLabels(
      String serialNumber,
      ChangeOrderType type,
      Set<String> labels,
      ChangeOrderSourceType sourceType,
      boolean isSharedWithCustomer) {

    String sn = serialNumber != null ? serialNumber : "sn-" + System.currentTimeMillis();

    List<ChangeOrderIdentifierRequest> identifiers =
        List.of(
            new ChangeOrderIdentifierRequest(SERIAL_NUMBER.getKey(), sn, null),
            new ChangeOrderIdentifierRequest(TARGET_ASSET_ID.getKey(), assetId.toString(), null),
            new ChangeOrderIdentifierRequest(
                RACK_SERIAL_NUMBER.getKey(), RACK_SERIAL_NUMBER1, null));

    AssetsToActionRequest actionRequest =
        new AssetsToActionRequest(
            assetId,
            "RN-001",
            RmcId.generate(RmcIdType.DCMS.RACK_INSTANCE),
            RmcId.generate(RmcIdType.DCMS.RACK_POSITION),
            "11",
            "Y",
            null,
            Instant.now(),
            0L,
            RmcId.generate(RmcIdType.Catalog.PART));

    CreateRepairOrder createRepairOrder =
        CreateRepairOrderBuilder.builder()
            .targetAssetId(assetId)
            .deviceSerialNumber(sn)
            .details(new ChangeOrderDetails(null, null, null, null))
            .priority(1)
            .accessLevel(RackInstanceAccessLevel.REGULAR)
            .repairOrderType(type)
            .labels(labels)
            .ticketFields(TICKET_FIELDS)
            .ticketData(TICKET_DATA)
            .identifierRequests(identifiers)
            .assetsToActions(List.of(actionRequest))
            .compartmentId(TEST_COMPARTMENT_ID)
            .region(TEST_REGION)
            .build();

    return internalChangeManagementService.create(
        createRepairOrder, sourceType, isSharedWithCustomer);
  }

  private List<String> getFilterChangeOrderTypes() {
    return List.of(
        ChangeOrderType.DISK_REPAIR.name(),
        ChangeOrderType.GB200_DISK_REPAIR.name(),
        ChangeOrderType.NVSWITCH_REPAIR.name(),
        ChangeOrderType.NETWORK_LINK_REPAIR.name(),
        ChangeOrderType.GB200_SERVER_REPAIR.name(),
        ChangeOrderType.NETWORK_LEAF_SPINE_LINK_REPAIR.name());
  }

  private void updateChangeOrder(
      ChangeOrder changeOrder, ChangeOrderState state, String assigneeEmail) {
    ChangeOrder updatedOrder =
        new ChangeOrder(
            changeOrder.id(),
            changeOrder.targetAssetId(),
            changeOrder.serialNumber(),
            changeOrder.type(),
            changeOrder.details(),
            changeOrder.priority(),
            changeOrder.severity(),
            state,
            changeOrder.summary(),
            changeOrder.description(),
            changeOrder.ticketId(),
            changeOrder.ticketUrl(),
            assigneeEmail,
            changeOrder.reason(),
            changeOrder.source(),
            changeOrder.isSharedWithCustomer(),
            changeOrder.previousChangeOrderId(),
            changeOrder.tailChangeOrderId(),
            changeOrder.repairOrderId(),
            changeOrder.isFailed(),
            changeOrder.revisedComment(),
            changeOrder.remark(),
            changeOrder.accessLevel(),
            changeOrder.compartmentId(),
            changeOrder.region(),
            null,
            changeOrder.version(),
            changeOrder.timeUpdated(),
            changeOrder.timeCreated(),
            changeOrder.timeUpdated(),
            Instant.now(),
            null);
    internalChangeManagementService.update(updatedOrder, "test-user");
  }

  @Test
  void listNonTerminalChangeOrdersForProbe_filterBySerialNumber_shouldReturnExpected() {
    String serial = "SN-PROBE-SERIAL";
    createTestOrder(serial, ChangeOrderType.GB200_SERVER_REPAIR);

    PaginatedList<ChangeOrder> result =
        internalChangeManagementService.listNonTerminalChangeOrdersForProbe(
            new ChangeManagementService.ListFilters.Builder().deviceSerialNumber(serial).build(),
            PageQuery.fromEmptyToken(10));

    assertEquals(1, result.getResults().size());
    assertEquals(serial, result.getResults().get(0).serialNumber());
  }

  @Test
  void listNonTerminalChangeOrdersForProbe_filterByInterfaces_shouldReturnExpected() {
    ChangeOrder order = createTestOrder("SN-PROBE-INT", ChangeOrderType.GB200_SERVER_REPAIR);
    List<ChangeOrderIdentifier> identifiers =
        List.of(
            ChangeOrderIdentifierBuilder.builder()
                .changeOrderId(order.id())
                .identifierKey(ChangeOrderIdentifierKey.INTERFACE_A.getKey())
                .location(null)
                .id(RmcId.generate(RmcIdType.CM.CHANGE_ORDER_IDENTIFIER))
                .identifierValue("intA")
                .build(),
            ChangeOrderIdentifierBuilder.builder()
                .changeOrderId(order.id())
                .identifierKey(ChangeOrderIdentifierKey.INTERFACE_LOGICAL_TARGET_B.getKey())
                .location(null)
                .id(RmcId.generate(RmcIdType.CM.CHANGE_ORDER_IDENTIFIER))
                .identifierValue("ltB")
                .build());
    changeOrderIdentifierRepository.saveAll(identifiers);
    PaginatedList<ChangeOrder> result =
        internalChangeManagementService.listNonTerminalChangeOrdersForProbe(
            new ChangeManagementService.ListFilters.Builder()
                .interfaceA("intA")
                .logicalTargetB("ltB")
                .build(),
            PageQuery.fromEmptyToken(10));
    assertNotNull(result);
    assertEquals(1, result.getResults().size());
  }

  @Test
  void listNonTerminalChangeOrdersForProbe_conflictingFilters_shouldThrow() {
    IllegalArgumentException exception =
        assertThrows(
            IllegalArgumentException.class,
            () ->
                internalChangeManagementService.listNonTerminalChangeOrdersForProbe(
                    new ChangeManagementService.ListFilters.Builder()
                        .deviceSerialNumber("conflict")
                        .interfaceA("intA")
                        .build(),
                    PageQuery.fromEmptyToken(10)));

    assertTrue(exception.getMessage().contains("Cannot filter by both serialNumber"));
  }

  @Test
  void listTerminalOrdersNeedingTaskCleanup_returnsExpectedOrders() {
    ChangeOrder order = createTestOrder("SN-TASK-CLEAN", ChangeOrderType.GB200_SERVER_REPAIR);
    ChangeTask toSave =
        new ChangeTask(
            RmcId.generate(RmcIdType.CM.CHANGE_TASK),
            order.id(),
            "name",
            "details",
            ChangeTaskState.OPEN,
            "runbook",
            null,
            "<EMAIL>",
            "probeType",
            0L,
            Instant.now(),
            Instant.now(),
            null,
            Instant.now());
    changeTaskRepository.save(toSave);
    order =
        internalChangeManagementService.updateState(
            order, ChangeOrderState.CANCELED, "DefaultInternalChangeManagementServiceTest", true);
    List<String> taskStates =
        Arrays.stream(ChangeTaskState.values())
            .filter(s -> !s.isTerminal())
            .map(Enum::name)
            .toList();
    PaginatedList<ChangeOrder> result =
        internalChangeManagementService.listTerminalOrdersNeedingTaskCleanup(
            taskStates, PageQuery.fromEmptyToken(10));

    assertNotNull(result);
    assertFalse(result.getItems().isEmpty());
    assertEquals(order.id(), result.getItems().get(0).id());
  }

  @Test
  void listTerminalOrdersNeedingTaskCleanup_HavingReservedAsset() {
    ChangeOrder order = createTestOrder("SN-TASK-CLEAN", ChangeOrderType.GB200_SERVER_REPAIR);
    order =
        internalChangeManagementService.updateState(
            order, ChangeOrderState.CLOSED, "DefaultInternalChangeManagementServiceTest", true);
    ChangeTask toSave =
        new ChangeTask(
            RmcId.generate(RmcIdType.CM.CHANGE_TASK),
            order.id(),
            "name",
            "details",
            ChangeTaskState.OPEN,
            "runbook",
            null,
            "<EMAIL>",
            "probeType",
            0L,
            Instant.now(),
            Instant.now(),
            null,
            Instant.now());
    changeTaskRepository.save(toSave);
    ChangeOrderAssetReservationId assetReservationId =
        new ChangeOrderAssetReservationId(order.id(), "testAssetId");
    ChangeOrderAssetReservation assetReservation =
        new ChangeOrderAssetReservation(
            assetReservationId, "testPartId", "testLocationId", "SN-TASK-CLEAN-SPARE", 0);
    changeOrderAssetReservationRepository.save(assetReservation);
    List<String> taskStates =
        Arrays.stream(ChangeTaskState.values())
            .filter(s -> !s.isTerminal())
            .map(Enum::name)
            .toList();
    PaginatedList<ChangeOrder> result =
        internalChangeManagementService.listTerminalOrdersNeedingTaskCleanup(
            taskStates, PageQuery.fromEmptyToken(10));

    assertNotNull(result);
    assertTrue(result.getItems().isEmpty());

    internalChangeManagementService.updateState(
        order, ChangeOrderState.CANCELED, "DefaultInternalChangeManagementServiceTest", true);

    PaginatedList<ChangeOrder> canceledResult =
        internalChangeManagementService.listTerminalOrdersNeedingTaskCleanup(
            taskStates, PageQuery.fromEmptyToken(10));

    assertNotNull(canceledResult);
    assertFalse(canceledResult.getItems().isEmpty());
  }

  @Test
  void cancelChangeOrder_InternalSharedWithCustomerThrowsException() {
    ChangeOrder order =
        createTestWithSharedFieldOrder(
            "SN5705", ChangeOrderType.GB200_SERVER_REPAIR, ChangeOrderSourceType.BURNINATOR, true);
    assertNotNull(order);

    Exception exception =
        assertThrows(
            IllegalArgumentException.class,
            () -> {
              internalChangeManagementService.cancelInternalRepairOrder(
                  order.id(),
                  "Attempt to cancel an order that has been shared with the customer",
                  TEST_USER,
                  true);
            });

    String expectedMessage = String.format(RESOURCE_INVALID_STATE, "cancel");
    String actualMessage = exception.getMessage();
    assertTrue(actualMessage.contains(expectedMessage));
  }

  @Test
  void processRepairOperation_InternalSharedWithCustomerThrowsException() {
    ChangeOrder order =
        createTestWithSharedFieldOrder(
            "SN5704", ChangeOrderType.GB200_SERVER_REPAIR, ChangeOrderSourceType.BURNINATOR, true);
    assertNotNull(order);

    CreateChangeOrder createChangeOrder =
        CreateRepairOrderBuilder.builder()
            .targetAssetId(order.targetAssetId())
            .deviceSerialNumber(order.serialNumber())
            .details(order.details())
            .priority(order.priority())
            .repairOrderType(order.type())
            .repairOrderId(order.id())
            .build();

    Exception exception =
        assertThrows(
            IllegalArgumentException.class,
            () -> {
              internalChangeManagementService.processRepairOrderOperation(
                  createChangeOrder,
                  ChangeOrderSourceType.USER_INITIATED,
                  true,
                  false,
                  RepairOperation.FAIL);
            });

    String expectedMessage = String.format(RESOURCE_INVALID_STATE, RepairOperation.FAIL);
    String actualMessage = exception.getMessage();
    assertTrue(actualMessage.contains(expectedMessage));

    exception =
        assertThrows(
            IllegalArgumentException.class,
            () -> {
              internalChangeManagementService.processRepairOrderOperation(
                  createChangeOrder,
                  ChangeOrderSourceType.USER_INITIATED,
                  true,
                  false,
                  RepairOperation.REPLY_TO);
            });

    expectedMessage = String.format(RESOURCE_INVALID_STATE, RepairOperation.REPLY_TO);
    actualMessage = exception.getMessage();
    assertTrue(actualMessage.contains(expectedMessage));
  }

  @Test
  void closeRepairOperation_InternalSharedWithCustomerThrowsException() {
    ChangeOrder order =
        createTestWithSharedFieldOrder(
            "SN5703", ChangeOrderType.GB200_SERVER_REPAIR, ChangeOrderSourceType.BURNINATOR, true);
    assertNotNull(order);

    Exception exception =
        assertThrows(
            IllegalArgumentException.class,
            () -> {
              internalChangeManagementService.closeInternalRepairOrder(
                  order.id(), "Test close repair operation", false, true, TEST_USER);
            });

    String expectedMessage = String.format(RESOURCE_INVALID_STATE, "close");
    String actualMessage = exception.getMessage();
    assertTrue(actualMessage.contains(expectedMessage));
  }

  @Test
  void approveRepairOperation_InternalSharedWithCustomerThrowsException() {
    ChangeOrder changeOrder =
        createTestWithSharedFieldOrder(
            "SN5702", ChangeOrderType.GB200_SERVER_REPAIR, ChangeOrderSourceType.BURNINATOR, true);
    assertNotNull(changeOrder);

    Exception exception =
        assertThrows(
            IllegalArgumentException.class,
            () -> {
              internalChangeManagementService.approveRepairOrder(
                  changeOrder.id(), TEST_USER, false);
            });

    String expectedMessage = String.format(RESOURCE_INVALID_STATE, "approve");
    String actualMessage = exception.getMessage();
    assertTrue(actualMessage.contains(expectedMessage));
  }

  @Test
  void createChangeOrderWithoutOperationDetails_InternalSharedWithCustomerThrowsException() {
    ChangeOrder order =
        createTestWithSharedFieldOrder(
            "SN5701", ChangeOrderType.GB200_SERVER_REPAIR, ChangeOrderSourceType.BURNINATOR, true);
    assertNotNull(order);

    Exception exception =
        assertThrows(
            IllegalArgumentException.class,
            () -> {
              internalChangeManagementService.createChangeOrderWithoutOperationDetails(
                  order.id(), ChangeOrderSourceType.BURNINATOR, true, false, RepairOperation.FAIL);
            });

    String expectedMessage = String.format(RESOURCE_INVALID_STATE, RepairOperation.FAIL);
    String actualMessage = exception.getMessage();
    assertTrue(actualMessage.contains(expectedMessage));

    exception =
        assertThrows(
            IllegalArgumentException.class,
            () -> {
              internalChangeManagementService.createChangeOrderWithoutOperationDetails(
                  order.id(),
                  ChangeOrderSourceType.USER_INITIATED,
                  true,
                  false,
                  RepairOperation.REPLY_TO);
            });

    expectedMessage = String.format(RESOURCE_INVALID_STATE, RepairOperation.REPLY_TO);
    actualMessage = exception.getMessage();
    assertTrue(actualMessage.contains(expectedMessage));
  }
}
