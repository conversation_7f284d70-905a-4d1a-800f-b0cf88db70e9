package com.oracle.oci.rmc.changemanagement.impl.dal;

import com.oracle.oci.rmc.auditevent.dal.AuditEventRepository;
import com.oracle.oci.rmc.changemanagement.api.model.common.ChangeOrderSourceType;
import com.oracle.oci.rmc.changemanagement.api.model.common.ChangeOrderState;
import com.oracle.oci.rmc.changemanagement.api.model.common.ChangeOrderType;
import com.oracle.oci.rmc.changemanagement.api.model.common.ChangeTaskState;
import com.oracle.oci.rmc.changemanagement.api.model.common.EffectiveState;
import com.oracle.oci.rmc.changemanagement.api.model.common.NeedsAttentionLifecycleState;
import com.oracle.oci.rmc.changemanagement.api.model.common.NeedsAttentionType;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeOrder;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeOrderBuilder;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeOrderDetails;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeOrderNeedsAttentionDetails;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeOrderNeedsAttentionDetailsBuilder;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeOrderNodeImpact;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeOrderNodeImpactBuilder;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeOrderStateTransition;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeTask;
import com.oracle.oci.rmc.model.RmcId;
import com.oracle.oci.rmc.model.RmcIdType;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Singleton
public class RepositoryTestUtil {
  @Inject
  private ChangeOrderNeedsAttentionDetailRepository changeOrderNeedsAttentionDetailRepository;

  @Inject private ChangeOrderNodeImpactRepository changeOrderNodeImpactRepository;
  @Inject private ChangeOrderStateTransitionRepository changeOrderStateTransitionRepository;
  @Inject private ChangeOrderRepository changeOrderRepository;
  @Inject private ChangeTaskRepository changeTaskRepository;
  @Inject private AuditEventRepository auditEventRepository;
  public static final String TEST_COMPARTMENT_ID = "testCompartmentId";
  public static final String TEST_REGION = "trg";

  public ChangeOrder createChangeOrder(
      RmcId id,
      RmcId targetAssetId,
      String serialNumber,
      ChangeOrderType type,
      @Nullable ChangeOrderDetails details,
      Integer priority,
      @Nullable Integer severity,
      ChangeOrderState state,
      @Nullable String summary,
      @Nullable String description,
      @Nullable String ticketId,
      @Nullable String ticketUrl,
      @Nullable String assigneeEmail,
      @Nullable String reason,
      ChangeOrderSourceType createdBy) {
    return createChangeOrder(
        id,
        targetAssetId,
        serialNumber,
        type,
        details,
        priority,
        severity,
        state,
        summary,
        description,
        ticketId,
        ticketUrl,
        assigneeEmail,
        reason,
        createdBy,
        null);
  }

  public ChangeOrder createChangeOrder(
      RmcId id,
      RmcId targetAssetId,
      String serialNumber,
      ChangeOrderType type,
      @Nullable ChangeOrderDetails details,
      Integer priority,
      @Nullable Integer severity,
      ChangeOrderState state,
      @Nullable String summary,
      @Nullable String description,
      @Nullable String ticketId,
      @Nullable String ticketUrl,
      @Nullable String assigneeEmail,
      @Nullable String reason,
      ChangeOrderSourceType createdBy,
      @Nullable Instant timeApproved) {
    return changeOrderRepository.save(
        ChangeOrderBuilder.builder()
            .id(id)
            .targetAssetId(targetAssetId)
            .serialNumber(serialNumber)
            .type(type)
            .details(details)
            .priority(priority)
            .severity(severity)
            .state(state)
            .summary(summary)
            .description(description)
            .ticketId(ticketId)
            .ticketUrl(ticketUrl)
            .assigneeEmail(assigneeEmail)
            .reason(reason)
            .source(createdBy)
            .isSharedWithCustomer(true)
            .compartmentId(TEST_COMPARTMENT_ID)
            .region(TEST_REGION)
            .timeApproved(timeApproved)
            .version(0L)
            .build());
  }

  public ChangeOrderNeedsAttentionDetails createChangeOrderNeedsAttentionDetails(
      RmcId changeOrderId, NeedsAttentionType needsAttentionType, String remark) {
    return changeOrderNeedsAttentionDetailRepository.save(
        ChangeOrderNeedsAttentionDetailsBuilder.builder()
            .id(RmcId.generate(RmcIdType.CM.NEEDS_ATTENTION_DETAILS))
            .changeOrderId(changeOrderId)
            .needsAttentionType(needsAttentionType)
            .state(NeedsAttentionLifecycleState.OPEN)
            .remark(remark)
            .build());
  }

  public List<ChangeOrderNeedsAttentionDetails> getChangeOrderNeedsAttentionDetailsByChangeOrderIds(
      List<RmcId> changeOrderIds) {
    return changeOrderNeedsAttentionDetailRepository.findNeedsAttentionDetailsByChangeOrderIds(
        changeOrderIds, null, null);
  }

  public Optional<ChangeOrder> getChangeOrder(RmcId changeOrderId) {
    return changeOrderRepository.findById(changeOrderId);
  }

  public void deleteChangeOrderById(RmcId changeOrderId) {
    changeOrderRepository.deleteById(changeOrderId);
  }

  public ChangeTask createChangeTask(
      RmcId id,
      RmcId changeOrderId,
      String name,
      @Nullable String details,
      ChangeTaskState state,
      @Nullable String runbook,
      @Nullable RmcId previousChangeTaskId,
      @Nullable String operatorEmail,
      @Nullable String probeType) {
    return changeTaskRepository.save(
        new ChangeTask(
            id,
            changeOrderId,
            name,
            details,
            state,
            runbook,
            previousChangeTaskId,
            operatorEmail,
            probeType,
            0L,
            Instant.now(),
            Instant.now(),
            null,
            Instant.now()));
  }

  public Optional<ChangeTask> getChangeTask(RmcId changeTaskId) {
    return changeTaskRepository.findById(changeTaskId);
  }

  public void deleteChangeTaskById(RmcId changeTaskId) {
    changeTaskRepository.deleteById(changeTaskId);
  }

  public List<ChangeTask> createChangeTasksWithPreviousLink(
      RmcId changeOrderId, int start, int end) {
    return IntStream.rangeClosed(start, end)
        .mapToObj(
            i -> {
              RmcId changeTaskId = new RmcId(RmcIdType.CM.CHANGE_TASK, "" + i);
              return createChangeTask(
                  changeTaskId,
                  changeOrderId,
                  "serialNumber",
                  "details",
                  ChangeTaskState.OPEN,
                  "runbook",
                  i == start ? null : new RmcId(RmcIdType.CM.CHANGE_TASK, "" + (i - 1)),
                  "operatorEmail",
                  "pt");
            })
        .collect(Collectors.toList());
  }

  public List<ChangeTask> createChangeTasksWithPreviousLink(
      RmcId changeOrderId, int start, int end, ChangeTaskState changeTaskState) {
    return IntStream.rangeClosed(start, end)
        .mapToObj(
            i -> {
              RmcId changeTaskId = new RmcId(RmcIdType.CM.CHANGE_TASK, "" + i);
              return createChangeTask(
                  changeTaskId,
                  changeOrderId,
                  "serialNumber",
                  "details",
                  changeTaskState,
                  "runbook",
                  i == start ? null : new RmcId(RmcIdType.CM.CHANGE_TASK, "" + (i - 1)),
                  "operatorEmail",
                  "pt");
            })
        .collect(Collectors.toList());
  }

  public ChangeOrderNodeImpact createChangeOrderNodeImpact(
      RmcId changeOrderNodeImpactId,
      RmcId changeOrderId,
      String nodeSerialNumber,
      RmcId nodeAssetId,
      EffectiveState effectiveState,
      Instant timeCreated,
      RmcId dataHallId,
      RmcId rackPositionId,
      Instant rackHandedOverTime,
      Instant meterOnTime,
      String rackType) {
    return changeOrderNodeImpactRepository.save(
        ChangeOrderNodeImpactBuilder.builder()
            .id(changeOrderNodeImpactId)
            .changeOrderId(changeOrderId)
            .nodeSerialNumber(nodeSerialNumber)
            .nodeAssetId(nodeAssetId)
            .effectiveState(effectiveState)
            .timeCreated(timeCreated)
            .dataHallId(dataHallId)
            .rackPositionId(rackPositionId)
            .rackHandedOverTime(rackHandedOverTime)
            .meterOnTime(meterOnTime)
            .rackType(rackType)
            .isReplacement(false)
            .build());
  }

  public ChangeOrderStateTransition createChangeOrderStateTransition(
      RmcId changeOrderStateTransitionId,
      RmcId changeOrderId,
      ChangeOrderState fromState,
      ChangeOrderState toState,
      Instant timeChanged,
      String transitionOwner) {
    return changeOrderStateTransitionRepository.save(
        new ChangeOrderStateTransition(
            changeOrderStateTransitionId,
            changeOrderId,
            fromState,
            toState,
            timeChanged,
            transitionOwner));
  }

  public void updateChangeOrder(ChangeOrder changeOrder) {
    changeOrderRepository.update(changeOrder);
  }

  public void deleteAuditEvent(String eventName) {
    auditEventRepository.deleteByEventName(eventName);
  }
}
