package com.oracle.oci.rmc.changemanagement.processor.fixedinterval.changeorder;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.oracle.oci.rmc.changemanagement.api.model.common.ChangeOrderSourceType;
import com.oracle.oci.rmc.changemanagement.api.model.common.ChangeOrderState;
import com.oracle.oci.rmc.changemanagement.api.model.common.ChangeOrderType;
import com.oracle.oci.rmc.changemanagement.api.model.common.ProbeState;
import com.oracle.oci.rmc.changemanagement.api.model.common.ProbeType;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeOrder;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeOrderBuilder;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeOrderDetailsBuilder;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ChangeOrderLabel;
import com.oracle.oci.rmc.changemanagement.api.model.entity.Probe;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ProbeBuilder;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ProbeResult;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ProbeResultBuilder;
import com.oracle.oci.rmc.changemanagement.api.model.entity.ProbeResultDetailsBuilder;
import com.oracle.oci.rmc.changemanagement.api.model.entity.RequestedChangeOrder;
import com.oracle.oci.rmc.changemanagement.api.model.entity.RequestedChangeOrderBuilder;
import com.oracle.oci.rmc.changemanagement.api.service.KeyValueService;
import com.oracle.oci.rmc.changemanagement.api.service.RepairOrderCreator;
import com.oracle.oci.rmc.changemanagement.impl.dal.ChangeManagementCleanup;
import com.oracle.oci.rmc.changemanagement.impl.dal.ChangeOrderRepository;
import com.oracle.oci.rmc.changemanagement.impl.dal.ProbeRepository;
import com.oracle.oci.rmc.changemanagement.impl.dal.ProbeResultRepository;
import com.oracle.oci.rmc.changemanagement.impl.dal.RequestedChangeOrderRepository;
import com.oracle.oci.rmc.changemanagement.impl.internal.OtsService;
import com.oracle.oci.rmc.client.model.CreateInternalRepairOrderDetails;
import com.oracle.oci.rmc.lease.DatabaseLeasing;
import com.oracle.oci.rmc.model.RmcId;
import com.oracle.oci.rmc.model.RmcIdType;
import io.micronaut.context.annotation.Requires;
import io.micronaut.test.annotation.TransactionMode;
import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@MicronautTest(transactionMode = TransactionMode.SINGLE_TRANSACTION)
@Requires(property = "database.type", value = "ORACLE")
@ExtendWith(MockitoExtension.class)
public class ChangeOrderProcessorTest {
  private static final Logger LOG = LoggerFactory.getLogger(ChangeOrderProcessorTest.class);

  @Inject private ChangeOrderRepository changeOrderRepository;

  @Inject private RequestedChangeOrderRepository requestedChangeOrderRepository;

  @Inject private RepairOrderCreator repairOrderCreator;

  // This ensures that scheduled processor does not interfere with the tests
  private static final ChangeOrderProcessorConfig CONFIG =
      new ChangeOrderProcessorConfig(true, Duration.ofHours(1));

  @Inject private KeyValueService keyValueService;

  @Inject private DatabaseLeasing databaseLeasing;

  @Inject private ProbeRepository probeRepository;

  @Inject private ProbeResultRepository probeResultRepository;

  @Inject private ChangeManagementCleanup cleanup;
  @Inject private OtsService otsService;

  private static String CHANGE_ORDER_ID1 =
      String.valueOf(RmcId.generate(RmcIdType.CM.CHANGE_ORDER));

  private ChangeOrderProcessor changeOrderProcessor;

  @Singleton
  static class RepairOrderCreatorTest implements RepairOrderCreator {

    private boolean throwException = false;

    public void setThrowException(boolean throwException) {
      this.throwException = throwException;
    }

    @Override
    public String createRepairOrder(
        CreateInternalRepairOrderDetails createInternalRepairOrderDetails) {
      if (throwException) {
        throw new IllegalArgumentException("Invalid Serial #");
      }
      return CHANGE_ORDER_ID1;
    }
  }

  @BeforeEach
  void setup() {
    cleanup.execute();
    changeOrderProcessor =
        new ChangeOrderProcessor(
            CONFIG,
            keyValueService,
            databaseLeasing,
            repairOrderCreator,
            requestedChangeOrderRepository,
            changeOrderRepository,
            probeRepository,
            probeResultRepository,
            otsService);
    ChangeOrder createChangeOrderId =
        createTestChangeOrder(
            ChangeOrderSourceType.BURNINATOR, ChangeOrderType.GB200_SERVER_REPAIR);
    CHANGE_ORDER_ID1 = createChangeOrderId.id().toString();
  }

  @AfterEach
  void teardown() {
    cleanup.execute();
  }

  @Test
  @Disabled("Flaky")
  void test_changeOrderCreation_burnin_happyPath() {
    ChangeOrder c1 =
        createTestChangeOrder(
            ChangeOrderSourceType.BURNINATOR, ChangeOrderType.GB200_SERVER_REPAIR);
    Probe p1 = createTestProbe(c1);
    createTestProbeResult(p1);
    ((RepairOrderCreatorTest) repairOrderCreator).setThrowException(false);
    RequestedChangeOrder rc1 =
        createTestRequestedChangeOrder(c1, ChangeOrderType.GB200_SERVER_REPAIR);
    RequestedChangeOrder rc2 =
        createTestRequestedChangeOrder(c1, ChangeOrderType.GB200_SERVER_REPAIR);
    RequestedChangeOrder rc3 =
        createTestRequestedChangeOrder(c1, ChangeOrderType.GB200_SERVER_REPAIR);
    changeOrderProcessor.run();
    List<RequestedChangeOrder> requestedChangeOrderList =
        requestedChangeOrderRepository.findByChangeOrderId(c1.id());
    RequestedChangeOrder requestedChangeOrder = requestedChangeOrderList.get(0);
    assertEquals(rc1.id(), requestedChangeOrder.id());
    assertEquals(rc1.changeOrderId(), requestedChangeOrder.changeOrderId());
    assertEquals(rc1.serialNumber(), requestedChangeOrder.serialNumber());
    assertEquals(CHANGE_ORDER_ID1, requestedChangeOrder.createdChangeOrderId().toString());
    assertEquals(rc1.otsAssignmentId(), requestedChangeOrder.otsAssignmentId());
    assertEquals(
        rc1.timeRequested().truncatedTo(ChronoUnit.SECONDS),
        requestedChangeOrder.timeRequested().truncatedTo(ChronoUnit.SECONDS));
    assertNotNull(requestedChangeOrder.timeChangeOrderCreated());
    for (RequestedChangeOrder res : requestedChangeOrderList) {
      assertEquals(CHANGE_ORDER_ID1, res.createdChangeOrderId().toString());
    }
    ChangeOrder changeOrder = changeOrderRepository.findById(c1.id()).orElse(null);
    assertNotNull(changeOrder);
    assertEquals(c1.id(), changeOrder.id());
    assertEquals(ChangeOrderState.VALIDATED, changeOrder.state());
  }

  @Test
  void test_processChangeOrders_pendingRequestedChangeOrders_noProbe() {
    ((RepairOrderCreatorTest) repairOrderCreator).setThrowException(false);
    ChangeOrder c1 =
        createTestChangeOrder(
            ChangeOrderSourceType.BURNINATOR, ChangeOrderType.GB200_SERVER_REPAIR);
    RequestedChangeOrder rc1 =
        createTestRequestedChangeOrder(c1, ChangeOrderType.GB200_SERVER_REPAIR);
    changeOrderProcessor.processPendingRequestedChangeOrders();
    RequestedChangeOrder requestedChangeOrder =
        requestedChangeOrderRepository.findById(rc1.id()).orElse(null);
    assertNotNull(requestedChangeOrder);
    assertNotNull(requestedChangeOrder.createdChangeOrderId());
  }

  @Test
  void testProcessRequestedChangeOrder_BurninatorSource_Gb200ServerRepair() {
    ((RepairOrderCreatorTest) repairOrderCreator).setThrowException(false);
    changeOrderRepository = Mockito.mock(ChangeOrderRepository.class);
    requestedChangeOrderRepository = Mockito.mock(RequestedChangeOrderRepository.class);
    changeOrderProcessor =
        new ChangeOrderProcessor(
            CONFIG,
            keyValueService,
            databaseLeasing,
            repairOrderCreator,
            requestedChangeOrderRepository,
            changeOrderRepository,
            probeRepository,
            probeResultRepository,
            otsService);
    RmcId changeOrderId = RmcId.generate(RmcIdType.CM.CHANGE_ORDER);
    ChangeOrder c1 =
        getChangeOrder(
            changeOrderId, ChangeOrderSourceType.BURNINATOR, ChangeOrderType.GB200_SERVER_REPAIR);

    ChangeOrder generatedChangeOrder =
        getChangeOrder(
            RmcId.fromString(CHANGE_ORDER_ID1),
            ChangeOrderSourceType.BURNINATOR,
            ChangeOrderType.GB200_SERVER_REPAIR);
    RequestedChangeOrder rc1 =
        RequestedChangeOrderBuilder.builder()
            .id(RmcId.generate(RmcIdType.CM.REQUESTED_CHANGE_ORDER))
            .changeOrderId(c1.id())
            .serialNumber(RandomStringUtils.insecure().nextAlphanumeric(3))
            .manufacturer("manuf")
            .repairType(ChangeOrderType.GB200_SERVER_REPAIR)
            .timeRequested(Instant.now())
            .version(1)
            .build();

    // Mock dependencies
    when(changeOrderRepository.findById(generatedChangeOrder.id()))
        .thenReturn(Optional.of(generatedChangeOrder));
    when(changeOrderRepository.findById(c1.id())).thenReturn(Optional.of(c1));

    // Call the method under test
    changeOrderProcessor.processRequestedChangeOrder(c1, rc1, null, 1);

    // Verify the behavior
    verify(requestedChangeOrderRepository, times(1))
        .updateRequestedChangeOrder(any(), any(), any(), any(), any());
    verify(changeOrderRepository, times(2))
        .updateChangeOrderChain(any(), any(), any(), any(), any());
    verify(changeOrderRepository, times(1)).updateChangeOrderTimeApproved(any(), any(), any());
  }

  private static ChangeOrder getChangeOrder(
      RmcId changeOrderId, ChangeOrderSourceType source, ChangeOrderType repairType) {
    return ChangeOrderBuilder.builder()
        .id(changeOrderId)
        .targetAssetId(RmcId.generate(RmcIdType.Inventory.ASSET))
        .serialNumber(RandomStringUtils.insecure().nextAlphanumeric(3))
        .type(repairType)
        .details(
            ChangeOrderDetailsBuilder.builder()
                .errorCodes(List.of("1", "2", "3"))
                .diagnosticReportObjectStorageUrl("Sample")
                .build())
        .priority(1)
        .severity(1)
        .state(ChangeOrderState.ACTIONABLE)
        .summary("summary")
        .description("description")
        .ticketId("ticketId")
        .ticketUrl("ticketUrl")
        .assigneeEmail("assigneeEmail")
        .reason("reason")
        .source(source)
        .isSharedWithCustomer(false)
        .compartmentId("TEST_COMPARTMENT_ID")
        .region("TEST_REGION")
        .timeApproved(Instant.now())
        .version(0L)
        .labels(List.of((new ChangeOrderLabel(changeOrderId, "test"))))
        .build();
  }

  @Test
  void testProcessRequestedChangeOrder_BurninatorSource_Gb200ServerRepairMultipleRequestedOrders() {
    ((RepairOrderCreatorTest) repairOrderCreator).setThrowException(false);
    changeOrderRepository = Mockito.mock(ChangeOrderRepository.class);
    requestedChangeOrderRepository = Mockito.mock(RequestedChangeOrderRepository.class);
    changeOrderProcessor =
        new ChangeOrderProcessor(
            CONFIG,
            keyValueService,
            databaseLeasing,
            repairOrderCreator,
            requestedChangeOrderRepository,
            changeOrderRepository,
            probeRepository,
            probeResultRepository,
            otsService);
    RmcId changeOrderId = RmcId.generate(RmcIdType.CM.CHANGE_ORDER);
    ChangeOrder c1 =
        getChangeOrder(
            changeOrderId, ChangeOrderSourceType.BURNINATOR, ChangeOrderType.GB200_SERVER_REPAIR);

    ChangeOrder generatedChangeOrder =
        getChangeOrder(
            RmcId.fromString(CHANGE_ORDER_ID1),
            ChangeOrderSourceType.BURNINATOR,
            ChangeOrderType.GB200_SERVER_REPAIR);
    RequestedChangeOrder rc1 =
        RequestedChangeOrderBuilder.builder()
            .id(RmcId.generate(RmcIdType.CM.REQUESTED_CHANGE_ORDER))
            .changeOrderId(c1.id())
            .serialNumber(RandomStringUtils.insecure().nextAlphanumeric(3))
            .manufacturer("manuf")
            .repairType(ChangeOrderType.GB200_SERVER_REPAIR)
            .timeRequested(Instant.now())
            .version(1)
            .build();

    // Mock dependencies
    when(changeOrderRepository.findById(generatedChangeOrder.id()))
        .thenReturn(Optional.of(generatedChangeOrder));

    // Call the method under test
    changeOrderProcessor.processRequestedChangeOrder(c1, rc1, null, 2);

    // Verify the behavior
    verify(requestedChangeOrderRepository, times(1))
        .updateRequestedChangeOrder(any(), any(), any(), any(), any());
    verify(changeOrderRepository, times(1))
        .updateChangeOrderChain(any(), any(), any(), any(), any());
    verify(changeOrderRepository, times(1)).updateChangeOrderTimeApproved(any(), any(), any());
  }

  @Test
  void test_processChangeOrders_repairOrderGeneratorException() {
    ChangeOrder c1 =
        createTestChangeOrder(
            ChangeOrderSourceType.BURNINATOR, ChangeOrderType.GB200_SERVER_REPAIR);
    RequestedChangeOrder rc1 =
        createTestRequestedChangeOrder(c1, ChangeOrderType.GB200_SERVER_REPAIR);
    Probe p1 = createTestProbe(c1);
    ProbeResult pr1 = createTestProbeResult(p1);
    ((RepairOrderCreatorTest) repairOrderCreator).setThrowException(true);
    changeOrderProcessor.processPendingRequestedChangeOrders();
    RequestedChangeOrder requestedChangeOrder =
        requestedChangeOrderRepository.findById(rc1.id()).orElse(null);
    assertNotNull(requestedChangeOrder);
    assertNull(requestedChangeOrder.createdChangeOrderId());
    assertEquals("Invalid Serial #", requestedChangeOrder.response());
    assertNull(requestedChangeOrder.timeChangeOrderCreated());
  }

  @Test
  @Disabled("Flaky")
  void test_changeOrderCreation_burnin_happyPath_nvswitch() {
    ChangeOrder co =
        createTestChangeOrder(ChangeOrderSourceType.BURNINATOR, ChangeOrderType.NVSWITCH_REPAIR);
    Probe probe = createTestProbe(co);
    createTestProbeResult(probe);

    ((RepairOrderCreatorTest) repairOrderCreator).setThrowException(false);

    RequestedChangeOrder r1 = createTestRequestedChangeOrder(co, ChangeOrderType.NVSWITCH_REPAIR);
    createTestRequestedChangeOrder(co, ChangeOrderType.NVSWITCH_REPAIR);
    createTestRequestedChangeOrder(co, ChangeOrderType.NVSWITCH_REPAIR);

    changeOrderProcessor.run();

    List<RequestedChangeOrder> list = requestedChangeOrderRepository.findByChangeOrderId(co.id());

    for (RequestedChangeOrder rc : list) {
      assertNotNull(rc.createdChangeOrderId());
      assertEquals(CHANGE_ORDER_ID1, rc.createdChangeOrderId().toString());
    }

    ChangeOrder updated = changeOrderRepository.findById(co.id()).orElseThrow();
    assertEquals(ChangeOrderState.VALIDATED, updated.state());
  }

  @Test
  void test_processChangeOrders_repairOrderGeneratorException_nvswitch() {
    ChangeOrder co =
        createTestChangeOrder(ChangeOrderSourceType.BURNINATOR, ChangeOrderType.NVSWITCH_REPAIR);
    createTestRequestedChangeOrder(co, ChangeOrderType.NVSWITCH_REPAIR);
    ((RepairOrderCreatorTest) repairOrderCreator).setThrowException(true);

    changeOrderProcessor.processPendingRequestedChangeOrders();

    RequestedChangeOrder stored =
        requestedChangeOrderRepository.findByChangeOrderId(co.id()).get(0);

    assertNull(stored.createdChangeOrderId());
    assertEquals("Invalid Serial #", stored.response());
    assertNull(stored.timeChangeOrderCreated());
  }

  public ChangeOrder createTestChangeOrder(
      ChangeOrderSourceType sourceType, ChangeOrderType changeOrderType) {
    RmcId changeOrderId = RmcId.generate(RmcIdType.CM.CHANGE_ORDER);
    return changeOrderRepository.save(
        ChangeOrderBuilder.builder()
            .id(changeOrderId)
            .targetAssetId(RmcId.generate(RmcIdType.Inventory.ASSET))
            .serialNumber(RandomStringUtils.insecure().nextAlphanumeric(3))
            .type(changeOrderType)
            .details(
                ChangeOrderDetailsBuilder.builder()
                    .errorCodes(List.of("1", "2", "3"))
                    .diagnosticReportObjectStorageUrl("Sample")
                    .build())
            .priority(1)
            .severity(1)
            .state(ChangeOrderState.ACTIONABLE)
            .summary("summary")
            .description("description")
            .ticketId("ticketId")
            .ticketUrl("ticketUrl")
            .assigneeEmail("assigneeEmail")
            .reason("reason")
            .source(sourceType)
            .isSharedWithCustomer(false)
            .compartmentId("TEST_COMPARTMENT_ID")
            .region("TEST_REGION")
            .timeApproved(Instant.now())
            .version(0L)
            .labels(List.of((new ChangeOrderLabel(changeOrderId, "test"))))
            .build());
  }

  private RequestedChangeOrder createTestRequestedChangeOrder(
      ChangeOrder changeOrder, ChangeOrderType changeOrderType) {
    return requestedChangeOrderRepository.save(
        RequestedChangeOrderBuilder.builder()
            .id(RmcId.generate(RmcIdType.CM.REQUESTED_CHANGE_ORDER))
            .changeOrderId(changeOrder.id())
            .serialNumber(RandomStringUtils.insecure().nextAlphanumeric(3))
            .manufacturer("manuf")
            .repairType(changeOrderType)
            .timeRequested(Instant.now())
            .version(1)
            .build());
  }

  private Probe createTestProbe(ChangeOrder changeOrder) {
    return probeRepository.save(
        ProbeBuilder.builder()
            .id(RmcId.generate(RmcIdType.CM.PROBE))
            .changeOrderId(changeOrder.id())
            .type(ProbeType.DIAGNOSTIC)
            .state(ProbeState.SUCCEEDED)
            .version(1L)
            .build());
  }

  private ProbeResult createTestProbeResult(Probe probe) {
    return probeResultRepository.save(
        ProbeResultBuilder.builder()
            .id(RmcId.generate(RmcIdType.CM.PROBE_RESULT))
            .probeId(probe.id())
            .result(
                ProbeResultDetailsBuilder.builder()
                    .resultDetails("res")
                    .diagnosticResultObjectStorageURL("https")
                    .build())
            .isErroneous(false)
            .build());
  }
}
